#login, #reset, #new-user {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 4em 0;
  background-color: var(--color-gray);

  > div {
    width: 500px;
    border-radius: 10px;

    h2 {
      text-align: center;
      padding: .5em;
    }

    .application-form {
      gap: 2em;
      padding: 2em;
      background-color: var(--color-white);
      border-radius: 10px;

      .submit {
        input {
          width: 100%;
          padding: 12px 24px;
        }
      }

      .links {
        display: flex;
        @media only screen and (max-width: 768px) {
          flex-direction: column;
          gap: 8px;
        }
        justify-content: space-between;
        align-items: center;
      }
    }

  }

  .alert {
    font-size: .8em;
    color: var(--color-error);
  }

  #cancel-forgotten {
    text-align: center;
  }
}

#new-user {
  > div {
    width: 800px;
  }
}


@media (max-width: 600px) {
  #login, #reset, #new-user {
    margin: 0 1%;
    > div {
      width: 100%;
    }
  }
}