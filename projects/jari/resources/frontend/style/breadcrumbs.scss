.breadcrumbs-wrapper {
  background-color: var(--color-primary);
  .breadcrumbs {
    max-width: 1400px;
    padding-inline: 34px;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 87px;
    font-size: 13px;
    letter-spacing: .03em;

    @media only screen and (max-width: 768px) {
      min-height: 50px;
    }
    @media only screen and (max-width: 1400px) {
      padding: 0 var(--padding);
    }

    a, span, svg, path {
      fill: var(--color-white);
      color: var(--color-white);
    }

    a {
      display: flex;
      span {
        display: flex;
        align-items: center;
      }

      &:hover {
        color: var(--color-yellow);
        svg path {
          fill: var(--color-yellow);
        }
      }
    }

    .crumbs {
      display: flex;
      align-items: center;
      gap: 1em;

      @media only screen and (max-width: 768px) {
        display: none;
      }
      
      span.gsd-svg-icon {
        display: flex;
        align-items: center;
      }

      .active-crumb {
        color: var(--color-yellow);
      }
    }

    .back span {
      margin-right: 1em;
    }
  }

}

@media (max-width: 768px) {
  .breadcrumbs {
    div:first-child {
      display: none;
    }
  }
}