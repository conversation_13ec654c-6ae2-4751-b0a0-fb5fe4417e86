.card {
  text-decoration: none;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: #fff;
  border: 1px solid rgba(10, 101, 153, .2);
  border-radius: 10px;

  &:hover {
    border-color: var(--color-primary);
  }

  .card-image {
    height: 208px;
    padding: 20px 28px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .card-description {
    height: calc(100% - 208px);
    padding: 32px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--color-gray);

    h2 {
      color: var(--color-black);
    }

    * {
      font-size: 15px;
    }

    .btn-primary {
      white-space: nowrap;
    }
  }
}

.filter-box {
  width: 325px;
  background-color: var(--color-gray);
  font-size: 15px;
  font-weight: 400;
  color: var(--color-black);
  border-radius: 10px;

  .filter-title {
    font-weight: 500;
    font-size: 16px;
    padding: 20px 40px;
  }

  .filters {
    padding: 25px 40px;
    border-top: 1px solid rgba(8, 59, 88, .2);

    .filter {
      display: block;
      margin-bottom: 15px;
      line-height: 1.3;
      letter-spacing: .03em;
      color: var(--color-black);

      &:hover {
        color: var(--color-yellow);
      }

      &.active {
        font-weight: 600;
        color: var(--color-blue);
      }
    }
  }
}

div:has(.filter-box) {
  align-self: normal;
}

.overview {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  width: 100%;

  .pager {
    grid-column: 1 / -1;
    display: flex;
    justify-content: right;
    margin-top: 1em;

    a.disabled {
      font-weight: 600;
    }

    a.disabled:hover, a.disabled:hover * {
      color: var(--color-primary);
      fill: var(--color-primary);
    }

    .pager-pageamount, i {
      display: none;
    }

    td.nextprev {
      margin-left: 1em;
      display: flex;
      gap: .7em;
    }
  }
}

div:has(.loader) {
  position: relative;
}

.loader {
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background-color: var(--color-white);
}

#seo-section > div {
  align-items: baseline;
}

@media (min-width: 640px) {
  .overview {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .overview {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 940px) {
  .gsd-container .filter-box {
    display: none;
  }
  .gsd-container > div {
    flex-direction: column;
  }
}
