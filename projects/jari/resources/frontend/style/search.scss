#searchbar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-inline: 24px auto;
  @media only screen and (max-width: 1200px) {
    margin-inline: auto 16px;
  }

  > button {
    display: flex;
    align-items: center;
    background: unset;
    cursor: pointer;
    margin-left: 6px;
    &#search-btn-mobile {
      display: none;
    }
    @media only screen and (max-width: 1200px) {
      &#search-btn-mobile {
        display: flex;
      }
      &:not(#search-btn-mobile) {
        display: none;
      }
    }

    &:active,
    &:hover {
      svg {
        fill: var(--color-yellow);
      }
    }
  }

  #search-input {
    padding: 10px 16px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid #ccc;
    border-radius: 10px;

    &::placeholder {
      font-family: var(--font-primary);
    }

    &:hover {

      border-color: var(--color-black);
    }

    &:focus {
      border-color: var(--color-black);
      box-shadow: var(--color-black) 0 0 2.5px;
    }

    @media only screen and (max-width: 1200px) {
      display: none;
    }
  }
}

#search-form-mobile {
  display: none;
  width: 100%;
  padding: 6px 4px;

  #search-input-mobile {
    width: 100%;
    padding: 10px 16px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid #ccc;
    border-radius: 10px;

    &::placeholder {
      font-family: var(--font-primary);
    }

    &:hover {

      border-color: var(--color-black);
    }

    &:focus {
      border-color: var(--color-black);
      box-shadow: var(--color-black) 0 0 2.5px;
    }
  }
}

#search-results {
  background-color: var(--color-gray);
  padding-inline: 0;

  > div {
    max-width: 972px;

    #no-result {
      margin-inline: auto;
      margin-bottom: 100px;
      text-align: center;

      #search-val {
        color: var(--color-yellow);
      }
    }
  }

}