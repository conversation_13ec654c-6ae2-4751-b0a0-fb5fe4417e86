footer {
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 3em 5% 1em;
  margin: 0 1%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;

  img {
    height: 2em;
    width: auto;
  }

  a {
    color: var(--color-white);
  }

  > div {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    div {
      margin: .5em
    }
  }

  strong {
    display: block;
    margin-bottom: 1em;
  }

  .info-box {
    max-width: 1400px;
    width: 100%;
    margin: auto;
    padding-bottom: 32px;
  }

  hr {
    border: none;
    height: 0.5px;
    background-color: var(--color-copyright);
    width: 100%;
  }

  .copyright {
    font-size: 14px;
    div, a:not(:hover) {
      color: var(--color-copyright);
    }

    ul {
      display: flex;
      justify-content: center;
      gap: 1em;
    }
  }
}

@media (max-width: 40em) {
  footer {
    text-align: center;

    > div {
      display: flex;
      flex-direction: column;
    }
  }
}