* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  outline: none;
}

html {
  display: block;
  font-family: var(--font-primary);
  font-size: var(--body-size);
  line-height: 1.5;
  font-weight: 300;
}

body {
  position: relative;
  color: var(--color-primary);

  .no-scroll {
    overflow: hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    color: var(--color-heading);
    margin-bottom: var(--spacing-heading);
    line-height: 1.2;
    font-weight: 500;
  }

  h1 {
    font-size: var(--h1-size);
  }
  h2 {
    font-size: var(--h2-size);
  }
  h3 {
    font-size: var(--h3-size);
  }
  h4 {
    font-size: var(--h4-size);
  }
  h5 {
    font-size: var(--h5-size);
  }
  h6 {
    font-size: var(--h6-size);
  }
}

input, textarea, select { font-family: inherit; }

ul {
  list-style: none;
  padding: 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition);

  &:hover {
    color: var(--color-yellow);

    .btn-primary {
      color: var(--color-white);
      background: var(--color-yellow);
    }
  }

  svg, path {
    fill: var(--color-primary);
    transition: fill 0.2s ease-in-out;
  }

  &:hover svg, &:hover path {
    fill: var(--color-yellow);
  }
}

hr {
  border-bottom: 1px solid rgba(10, 101, 153, .2);
  width: 100%;
}

.btn-primary {
  display: inline-flex;
  align-content: center;
  justify-content: center;
  gap: 5px 10px;
  text-align: center;
  font-weight: 400;
  background: var(--color-primary);
  color: var(--color-white);
  padding: .5em 1.5em;
  border-radius: 10px;
  transition: 0.2s ease-in-out;
  cursor: pointer;
  font-size: var(--body-size);

  &:hover {
    color: var(--color-white);
    background: var(--color-yellow);
  }

  svg, path {
    fill: var(--color-white);
    transition: fill 0.2s ease-in-out;
  }

  &:hover svg, &:hover path {
    fill: var(--color-white);
  }
}

.btn-secondary {
  @extend .btn-primary;
  background: var(--color-white);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);

    &:hover {
      background: var(--color-white);
      color: var(--color-primary);
      border-color: var(--color-yellow);
    }
}

.lightbox img {
  aspect-ratio: 5 / 4;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease-in-out, filter 0.2s ease-in-out;

  &:hover {
    filter: brightness(0.7);
  }
}