.gsd-editor {
  section.gsd-container {
    overflow: hidden;

    &.bg-gray {
      background-color: var(--color-gray);
    }

    &.header-block {
      color: var(--color-white);
      background-color: var(--color-primary);
      @media only screen and (max-width: 768px) {
        padding-top: 15px;
      }
      padding-bottom: 3%;
      //margin-bottom: 30px;
      position: relative;
      z-index: 1;

      &.small-block {
        padding: 15px 0;
      }

      &:not(.small-block) {
        > div {
          @media only screen and (min-width: 1300px) {
            align-items: flex-start;

            &:after {
              content: '';
              background-color: var(--color-gray);
              position: absolute;
              left: 0;
              bottom: -1px;
              height: 13%;
              width: 100vw;
            }
          }

          @media only screen and (max-width: 768px) {
            padding-inline: 20px;

            &:after {
              content: '';
              background-color: var(--color-gray);
              position: absolute;
              left: 0;
              bottom: -1px;
              height: max(4%, 65px);
              width: 100vw;
            }
          }
        }

      }

      > div {
        max-width: 1400px;
        padding-inline: 34px;
        margin: 0 auto;
        align-items: center;

        div.gsdblock-wysiwyg-block,
        div.gsdblock-image-block {
          z-index: 2;
          margin-right: 0;
        }

        .gsdblock-wysiwyg-block {
          align-self: center;
          width: min(625px, 58%);
          @media only screen and (max-width: 768px) {
            width: 100%;
          }
        }

        div.gsdblock-image-block {
          width: 42%;
          aspect-ratio: 620 / 725;

          @media only screen and (max-width: 768px) {
            width: 100%;
            max-width: 520px;
          }

          img {
            object-position: center center;
            height: 100%;
            width: 100%;
            min-height: 100%;
            min-width: 100%;
            max-height: 100%;
            max-width: 100%;
            display: block;
            object-fit: cover;
          }
        }
      }

      a {
        color: var(--color-white);

        &:hover {
          color: var(--color-yellow);
        }
      }
    }
  }
}

.gsd-editor {
  width: 100%;
  background-color: white;
}

.gsd-container {
  width: 100%;
  margin: 0 auto;
  padding: 50px 0;
  word-break: break-word;
  @media only screen and (max-width: 768px) {
    padding: 30px 0;
  }
}

.gsd-container > div {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-inline: 34px;
  max-width: 1140px;
  width: 100%;
  gap: 30px;
}

.gsd-wide-container {
  > div {
    max-width: 1400px;
    padding-inline: 34px;
    margin: 0 auto;
  }
}

/** two column grids **/
.gsd-container.gsd-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-wysiwyg-image > div > div,
.gsd-container.gsd-image-wysiwyg > div > div,
.gsd-container.gsd-image-image > div > div {
  width: 50%;
}

/** three column grids **/
.gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-image-image-image > div > div {
  flex: 33.33%;
}

/** some default styling **/
.gsd-container.gsd-image-wysiwyg,
.gsd-container.gsd-image-image,
.gsd-container.gsd-wysiwyg-image {
  div {
    .gsdblock-image-block {
      &:not(.parralax) {
        width: 42%;
        @media only screen and (max-width: 768px) {
          width: 100%;
        }
      }

      img {
        width: 100%;
        height: auto;
        max-width: 100%;
        border-radius: 10px;
      }
    }
  }
}

.gsdblock-image-block.image-block-backgroundimage {
  min-height: 250px;
}

.image-block-backgroundimage {
  background-size: cover;
  background-position: center;
}

.gsd-wysiwyg .gsdblock-wysiwyg-block {
  //padding: 0 15px;
  //width: 94%;
}


.gsd-wysiwyg .gsdblock-wysiwyg-block p:last-child {
  margin-block-end: 0;
}

.gsd-wysiwyg-image .gsdblock-wysiwyg-block {
  margin-right: 50px;
}

.gsd-image-wysiwyg .gsdblock-wysiwyg-block {
  margin-left: 50px;
}

p {
  margin-top: 0;
  margin-bottom: 1em;
}

.image-block-title {
  padding: 10px 15px;
  font-weight: bold;
  background-color: #ebebeb;
}

.image-block-description {
  padding: 10px 15px;
  border: 1px solid #ebebeb;
}

.text-middle-width > div {
  max-width: 760px;
}

.equal-width-columns > div {
  gap: 3em;

  > div {
    flex: 1;
    width: 100%;
  }
}

/** responsive **/
@media (max-width: 767px) {
  .gsd-editor {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .gsd-container > div {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
    padding-inline: 34px;
    @media only screen and (max-width: 768px) {
      padding-inline: 20px;
    }

    div {
      margin: 0 !important;
    }
  }


  /** two column grids **/
  .gsd-container.gsd-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-wysiwyg-image > div > div,
  .gsd-container.gsd-image-wysiwyg > div > div,
  .gsd-container.gsd-image-image > div > div {
    width: 100%;
  }

  /** three column grids **/
  .gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-image-image-image > div > div {
    flex: 100%;
  }
}