.contact {
  
  #contact-form {
    > div {
      max-width: 972px;
      flex-direction: column;
      padding-inline: 0;

      > div {
        width: 100%;
        background-color: var(--color-white);
        padding: 50px 75px;
        border-radius: 10px;
        @media only screen and (max-width: 768px) {
          padding: 35px 20px;
        }
        .gsd-container {
          padding: 0;
          text-align: start;
        }
      }
    }
  }

  .gsd-container {
    margin: 0 auto;
  }

  .header-block > div > div {
    width: 50%;
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    gap: 5em;
    margin: 3em 0;
  }
}

@media (max-width: 768px) {
  .row {
    flex-direction: column;
    align-items: center;
  }
}