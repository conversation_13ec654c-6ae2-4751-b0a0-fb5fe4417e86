#cookie-banner {
  display: none;
  z-index: 999999999;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  > div {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100dvh;

    .cookie-glass {
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      position: fixed;
      background-color: black;
      opacity: 0.8;

      > div {
        opacity: 0.5;
        background-color: black;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }

    .cookie-container {
      width: min(500px, 100%);
      max-height: 100%;
      display: inline-block;
      vertical-align: middle;
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
      --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      box-shadow: 0 0 #0000, 0 0 #0000, var(--tw-shadow);
      text-align: left;
      border-radius: 0.5rem;
      overflow: auto;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-rotate: 0;
      --tw-skew-x: 0;
      --tw-skew-y: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));

      .cookie-header {
        position: sticky;
        top: 0;
        text-align: center;
        padding: 25px;
        background-color: var(--color-primary);
        color: white;
        display: flex;

        > img {
          width: 120px;
        }

        .cookie-title {
          margin-left: auto;
          display: flex;
          align-items: center;

          > svg {
            margin-right: 12px;
          }

          h2 {
            margin: 0;
            display: none;
          }
        }
      }

      .cookie-content {
        background: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .cookie-about {
          padding: 24px;

          a {
            text-decoration: underline;
          }
        }

        .cookie-btns {
          position: sticky;
          bottom: 0;
          background-color: white;
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          justify-content: space-evenly;
          gap: 4px;
          padding: 0 15px 15px 15px;
          border-top: 1px solid var(--light-brown);

          button {
            cursor: pointer;
            flex-grow: 1;
            width: 100%;
            padding: 12px 4px;
            border-radius: 10px;

            &#consent-deny {
              background-color: unset;
              &:hover {
                color: var(--color-yellow);
              }
            }
          }
        }
      }
    }

  }
}

#tooltip {
  z-index: 9999999999999999;
  background-color: unset;
  border-radius: 4px;

  #tooltip-content {
    background-color: var(--brown);
    color: white;
    border-radius: 4px;
  }

  #tooltip-arrow,
  #tooltip-arrow::before {
    background-color: var(--brown);
    border: none !important;
  }
}

@media screen and (min-width: 830px) {
  #cookie-banner {
    > div {
      .cookie-container {
        max-height: 80%;

        .cookie-header {
          .cookie-title {
            h2 {
              display: inline;
            }
          }
        }

        .cookie-content {
          .cookie-btns {
            button {
              width: fit-content;
            }
          }
        }
      }
    }
  }
}