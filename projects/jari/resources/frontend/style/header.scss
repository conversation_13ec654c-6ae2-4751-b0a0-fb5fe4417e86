/* Header */

:root {
  --logo-height: 29px;
  @media only screen and (max-width: 480px) {
    --logo-height: 24px;
  }

  --header-height: 156px;
  @media only screen and (max-width: 1180px) {
    --header-height: 65px;
  }
}

header {
  position: sticky;
  top: 0;
  width: 100%;
  background: white;
  transition-duration: 0.25s;
  transition-property: all;
  transition-timing-function: ease;
  transition-delay: 0s;
  z-index: 5;
  opacity: 1;

  &.hidden {
    // we move it up out of the frame, so we achieve a animation effect
    transform: translateY(100%);
    opacity: 0;
  }

  .show-touch-devices {
    display: none;
  }

  /* Nav */
  nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1;

    .logo {
      height: var(--logo-height);
      display: flex;
      align-items: center;
      @media only screen and (max-width: 768px) {
        margin-right: auto;
      }

      img {
        height: 100%;
        width: auto;
      }
    }

    ul {
      list-style: none;
      display: flex;
      align-items: center;

      li {
        a {
          color: #000;
          font-weight: 400;
          text-align: center;
        }
      }
    }
  }

  @media only screen and (max-width: 1180px) {
    .show-touch-devices {
      display: block;
    }
  }

  .top-nav {
    padding: 0 var(--padding);
    min-height: calc(var(--header-height) / 2);
    @media only screen and (max-width: 1180px) {
      min-height: var(--header-height);
    }

    .top-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      max-width: 1400px;
      margin: auto;

      ul {
        gap: 2em;
        @media screen and (max-width: 75em) {
          display: none;
        }
        
        li {
          a {
            font-size: 14px;
          }
        }
      }
    }
  }

  .bottom-nav {
    background-color: var(--color-gray);

    .bottom-container {
      display: flex;
      align-items: center;
      font-size: 0.9em;
      gap: 16px;
      margin: auto;
      width: 100%;
      max-width: 1400px;

      @media only screen and (max-width: 1500px) {
        padding-inline: 32px;
      }

      li {
        cursor: pointer;
        display: flex;
        align-items: center;
        word-break: break-all;

        &.dropdown {
          position: relative;
          border-radius: 10px;
          background-color: var(--color-blue);
          transition: background-color 0.2s ease-in-out;

          a {
            padding: 20px var(--padding);
            white-space: nowrap;
          }

          * {
            color: var(--color-white);
          }

          &:hover {
            background-color: var(--color-yellow);
          }

          .all-product-pages {
            display: grid;
            grid-template-columns: auto auto;
            position: absolute;
            top: 100%;
            left: 0;
            padding: 30px 25px;
            border-top: none;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            background-color: var(--color-gray);
            gap: 0;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s;
            overflow: auto;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

            * {
              color: var(--color-black);

              &:hover {
                color: var(--color-yellow);
              }
            }
          }
        }
      }

      /* Toon menu bij hover */
      .dropdown:hover .all-product-pages,
      .dropdown:focus-within .all-product-pages {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}

header.hidden {
  // we move it up out of the frame, so we achieve a animation effect
  transform: translateY(-200px);
  opacity: 0;
}

.top-nav ul, .other-pages {
  li:last-child a {
    @extend .btn-primary
  }
}

.here0 {
  color: var(--color-yellow);
  font-weight: 700;
}

/* Hamburger (For smaller screens) */
.hamburger-button {
  background: none;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-primary);
  transition: var(--transition);

  &.hamburger-active {
    .hamburger-button__inner {
      i {
        &:nth-child(1) {
          transform: rotate(45deg);
          top: 9px;
        }

        &:nth-child(2) {
          top: 7px;
          width: 0;
          left: 50%;
        }

        &:nth-child(3) {
          transform: rotate(-45deg);
          bottom: 5px;
          width: 100%;
        }
      }
    }
  }

  .hamburger-button__inner {
    width: 20px;
    height: 16px;
    position: relative;

    i {
      left: 0;
      width: 100%;
      height: 2px;
      background: var(--color-primary);
      position: absolute;
      transition-duration: .25s;
      transition-property: all;
      transition-timing-function: ease;
      transition-delay: 0s;
      display: block;

      &:nth-child(1) {
        top: 0;
      }

      &:nth-child(2) {
        top: 7px;
      }

      &:nth-child(3) {
        bottom: 0;
        width: 13px;
        left: auto;
        right: 0;
      }
    }
  }
}

/* Menu bar (appears after clicking hamburger) */
.menubar {
  position: fixed;
  top: 0;
  margin-top: var(--header-height);
  left: -100%;
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  background: rgba(255, 255, 255);
  transition-duration: .25s;
  transition-property: all;
  transition-timing-function: ease;
  transition-delay: 0s;
  z-index: 4;
  width: 100%;

  &.active {
    left: 0 !important;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    opacity: 100%;
  }

  * {
    text-align: center;
  }

  > div {
    height: calc(100dvh - var(--header-height));
    width: 100%;
    overflow-y: auto;

    .dropdown {
      position: sticky;
      top: 0;
      background-color: var(--color-blue);
      transition: background-color 0.2s ease-in-out;
      padding: 1.5em 4em;

      &.active {
        background-color: var(--color-yellow);
      }

      * {
        color: var(--color-white);
      }

      .all-product-pages {
        position: fixed;
        top: 0;
        display: block;
        opacity: 0;
        left: -100%;
        padding: 40px 25px;
        overflow-y: auto;
        height: calc(100vh - var(--header-height) * 2);
        margin-top: calc(var(--header-height) + 72px);
        background-color: var(--color-gray);
        transition-duration: .25s;
        transition-property: all;
        transition-timing-function: ease;
        transition-delay: 0s;

        &.active {
          left: 0 !important;
          box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
          opacity: 100%;
        }

        div {
          padding: 2em 4em;
        }

        * {
          color: var(--color-black);
        }
      }
    }
  }
}

.menubar ul li {
  margin-bottom: 1em;
}

.product-pages {
  background-color: var(--color-gray);
  padding: 2em 4em;
}

.other-pages {
  background-color: var(--color-white);
  padding: 2em 4em;
}

@media screen and (max-width: 75em) {
  .hamburger {
    display: block;
  }
  .top-nav > ul {
    display: none;
  }
  .bottom-nav {
    display: none !important;
  }

  body:has(.menubar.active) {
    overflow: hidden;
  }
}
@media screen and (min-width: 75em) {
  .menubar {
    left: -100% !important;

    .dropdown .all-product-pages {
      left: -100% !important;
    }
  }
}

@media screen and (max-width: 30em) {
  .menubar {
    width: 100% !important;

    .dropdown .all-product-pages {
      width: 100% !important;
    }
  }
}