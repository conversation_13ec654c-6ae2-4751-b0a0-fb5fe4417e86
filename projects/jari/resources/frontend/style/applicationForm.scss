.application-form {
  display: flex;
  flex-direction: column;
  gap: 40px;
  @media only screen and (max-width: 768px) {
    gap: 25px;
  }
  max-width: 972px;
  margin: auto;
  text-align: start;

  .btn-primary {
    border: none;
    cursor: pointer;
  }

  .form-row {
    display: flex;
    gap: 54px;
    @media only screen and (max-width: 768px) {
      gap: 25px;
    }
  }

  .form-group {
    display: flex;
    flex: 1;
    flex-direction: column;

    select {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    label {
      font-weight: 500;
      margin-bottom: 15px;
      @media only screen and (max-width: 768px) {
        margin-bottom: 5px;
      }
      font-size: 15px;
      color: var(--color-black);
    }
  }

  .form-input {
    padding: 1.3em;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid transparent;
    border-radius: 10px;
    background-color: var(--color-gray);

    &::placeholder {
      font-family: var(--font-primary);
    }

    &:hover {
      border: 1px solid #ccc;
      border-color: var(--color-black);
    }

    &:focus {
      border: 1px solid #ccc;
      border-color: var(--color-black);
      box-shadow: var(--color-black) 0 0 2.5px;
    }

    &.inputerror {
      border: 1px solid var(--color-error);
      box-shadow: var(--color-error) 0 0 2.5px;
    }
  }

  input[type="file"] {
    cursor: pointer;

    &::-webkit-file-upload-button {
      @extend .btn-primary;
      border: none;
      margin-right: 1em;
      cursor: pointer;
    }
  }

  .alert {
    font-size: .8em;
    color: var(--color-error);
  }
}

/* Textarea styling */
textarea.form-input {
  min-height: 100px;
  resize: vertical;
}

.custom-checkbox {
  appearance: none;
  width: 22px;
  height: 22px;
  border: 1px solid var(--color-primary);
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  background-color: white;
  position: relative;

  &:hover {
    border-color: var(--color-yellow);
  }

  &:checked {
    background-color: var(--color-yellow);
    border-color: var(--color-yellow);
  }

  &:before {
    content: "";
    width: 14px;
    height: 14px;
    display: block;
  }

  /* Checkmark (✔) na selectie */
  &:after {
    content: "✔";
    font-size: 16px;
    color: var(--color-white);
    font-weight: bold;
    position: absolute;
    line-height: 1;
  }

  &.inputerror {
    border: 1px solid var(--color-error);
    box-shadow: var(--color-error) 0 0 2.5px;
  }
}

.privacy {
  margin-top: 1em;
  display: flex;
  align-items: center;
  gap: 10px;

  label {
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
  }
}

.form-row:has(.privacy) {
  align-items: center;
  justify-content: space-between;
}

.warning {
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
    gap: 10px;
  }
}

