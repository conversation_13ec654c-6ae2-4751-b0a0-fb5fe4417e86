/* (Google) Font imports, see https://fonts.google.com/ */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');

@CHARSET "UTF-8";

:root {

  /* <PERSON><PERSON><PERSON> */
  --color-white: #fff;
  --color-white-rgb: 255, 255, 255;
  --color-black: #000;
  --color-primary: #083B58;
  --color-primary-rgb: 8, 59, 88;
  --color-yellow: #F7BD16;
  --color-blue: #0A6599;
  --color-medium-blue: #619DC5;
  --color-light-blue: #b8d4f0;
  --color-gray: #f5f7fa;
  --color-bg: #fff;
  --color-text: var(--bb-color-primary);
  --color-heading: var(--bb-color-primary);
  --color-permalink: var(--bb-color-text);
  --color-permalink-highlight: var(--bb-color-yellow);
  --color-on-primary: #fff;
  --color-error: #f00;
  --color-white: #fff;
  --color-placeholder: rgba(44, 51, 54, .5);
  --color-copyright: rgba(255, 255, 255, .5);
  --color-bg-success: #3c763d;
  --color-border-success: #d6e9c6;
  --color-bg-danger: #a94442;
  --color-border-danger: #ebccd1;

 /* Fonts */
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
  --font-serif: Georgia, Cambria, "Times New Roman", Times, serif;
  --font-primary: 'Poppins', sans-serif;
  --font-icomoon: 'icomoon', fantasy;

  --h1-size: 49px;
  --h2-size: 31px;
  --h3-size: 20px;
  --h4-size: 18px;
  --h5-size: 15px;
  --h6-size: 12px;
  --body-size: 17px;

  @media only screen and (max-width: 1024px) {
    --h1-size: 42px;
    --h2-size: 28px;
  }

  @media only screen and (max-width: 480px) {
    --h1-size: 37px;
    --h2-size: 26px;
    --h3-size: 20px;
    --h4-size: 16px;
    --body-size: 16px;
  }

  /* Spacing */
  --spacing-heading: 20px;
  --padding: clamp(20px, calc(7.29vw - 60px), 80px);

  /* Transition */
  --transition: all .3s ease;

  /* animate.css */
  --animate-delay: 0.25s; // delay classes take half the time to start

  /* Most used colors */

  /* --primary-color: ; */
  /* --secondary-color: ; */

  /* Button colors */
  /* --gsd-btn-bg-color: ; */
  /* --gsd-btn-text-color: ; */
  /* --gsd-btn-bg-color-hover: ; */
  /* --gsd-btn-text-color-hover: ; */

  /* --gsd-btn-primary-bg-color: ; */
  /* --gsd-btn-primary-text-color: ; */
  /* --gsd-btn-primary-bg-color-hover: ; */
  /* --gsd-btn-primary-text-color-hover: ; */

  /* --gsd-btn-secondary-bg-color: ; */
  /* --gsd-btn-secondary-text-color: ; */
  /* --gsd-btn-secondary-bg-color-hover: ; */
  /* --gsd-btn-secondary-text-color-hover: ; */

  /* --gsd-btn-tertiary-bg-color: ; */
  /* --gsd-btn-tertiary-text-color: ; */
  /* --gsd-btn-tertiary-bg-color-hover: ; */
  /* --gsd-btn-tertiary-text-color-hover: ; */


}