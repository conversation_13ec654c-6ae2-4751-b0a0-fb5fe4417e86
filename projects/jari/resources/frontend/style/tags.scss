#page-tags {
  .tags {
    display: block;
    max-width: 1140px;
    width: 100%;
    margin: 0 auto 24px auto;
    padding-inline: 34px;

    > i {
      margin-right: 6px;
    }

    > a {
      white-space: nowrap;
    }
  }
}

#tag-results {
  > div {
    display: block;
    max-width: 1140px;
    width: 100%;
    margin: 0 auto 24px auto;
    padding-inline: 34px;

    #tags_result {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      @media only screen and (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .tag-container {
        color: var(--color-white);
        background-color: var(--color-blue);
        transition: var(--transition);
        border-radius: 10px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .teaser {
          margin-bottom: 24px;
          font-size: 15px;
        }

        .readmore {
          margin-top: auto;
          color: var(--color-white);

          svg {
            vertical-align: middle;
            transform: translateX(0);
            transition: transform .2s ease-in-out;

            path {
              fill: var(--color-white);
            }
          }

          &:hover {
            svg {
              transform: translate(6px);
              transition: transform .3s ease-in-out;

              path {
                fill: var(--color-yellow)
              }
            }
          }
        }

        &:hover {
          a {
            color: var(--color-white);

            &:hover {
              color: var(--color-yellow);
            }
          }
        }
      }
    }
  }
}