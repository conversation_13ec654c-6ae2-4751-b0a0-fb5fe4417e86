main {
  width: 100%;
}

.inventory-image {
  position: relative;
  flex: 1;
  aspect-ratio: 4 / 7;
  height: 100%;
  object-fit: cover;
  overflow: hidden;
  border-radius: 10px;

  .info {
    color: var(--color-white);
    position: absolute;
    bottom: 3em;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease-in-out; // Smooth zoom effect
  }

  &:hover {
    img {
      transform: scale(1.05); // Slight zoom-in effect
    }

    .btn-primary {
      background-color: var(--color-yellow);
    }
  }
}

.parralax {
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  border-radius: 10px;

  img {
    width: 100%;
    object-fit: cover;
  }

  .link {
    position: absolute;
    color: var(--color-white);
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 1;

    svg, path {
      fill: var(--color-white);
    }
  }
}

.promotion {
  aspect-ratio: 3 / 1;

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--color-white-rgb), 0.5);
    transition: opacity 0.3s ease-in-out;
  }

  &:hover {
    svg, path {
      fill: var(--color-white);
    }

    .btn-primary {
      background-color: var(--color-yellow);
    }

    .overlay {
      opacity: 0;
    }
  }
}

.question {
  aspect-ratio: 3 / 1;

  img {
    position: relative;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--color-primary-rgb), 0.5);
  }
}

.main a {
  @extend .btn-primary;
  background: var(--color-white);
  color: var(--color-primary);

  &:hover {
    color: var(--color-white);
  }
}

.modal {
  display: none;
  position: fixed;
  z-index: 3;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}

.modal-content {
  margin: 15% auto;
  width: 60%;
  aspect-ratio: 16 / 9;

  iframe {
    width: 100%;
    height: 100%;
  }
}

.close {
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

@media (max-width: 768px) {
  .gsdblock-image-block {
    aspect-ratio: 3 / 2;
  }

  .modal-content {
    width: 100%;
  }
}

