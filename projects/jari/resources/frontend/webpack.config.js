const node_modules_root = "../../../../gsdfw/tools/node_modules/";
const path = require("path");
const MiniCssExtractPlugin = require(node_modules_root + "mini-css-extract-plugin");
const {CleanWebpackPlugin} = require(node_modules_root + "clean-webpack-plugin");
const CssMinimizerPlugin = require(node_modules_root + "css-minimizer-webpack-plugin");
const TerserJSPlugin = require(node_modules_root + "terser-webpack-plugin"); // for minimizing JS

const DEVMODE = process.env.NODE_ENV !== 'production';
const PROJECT = !process.env.PROJECT ? 'jari' : process.env.PROJECT;
const TEMPLATE = 'frontend';
const DIR_GSDFW = '../../gsdfw/';
const DIR_RESOURCE = '../../projects/' + PROJECT + '/resources/' + TEMPLATE + '/';

module.exports = {
  optimization: {
    minimize: process.env.NODE_ENV === 'production',
    minimizer: [
      new TerserJSPlugin({}),
      new CssMinimizerPlugin({})
    ]
  },
  entry: [
    DIR_RESOURCE + 'style/imports.css',
  ],
  output: {
    filename: DEVMODE ? "deploy.js" : "deploy.min.js",
    path: path.resolve(__dirname, "../../templates/" + TEMPLATE + "/dist/")
  },
  mode: process.env.NODE_ENV,
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              // you can specify a publicPath here
              // by default it use publicPath in webpackOptions.output
              publicPath: '../'
            }
          },
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,
            }
          },
          {
            loader: 'sass-loader',
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                syntax: require(node_modules_root + 'postcss-scss'),
                plugins: [
                  require(node_modules_root + 'postcss-import'),
                  require(node_modules_root + 'autoprefixer'),
                ],
              },
            },
          },
        ]
      },
      {
        test: /\.(gif|png|jpe?g|svg)$/i,
        type: 'asset/resource',
        generator: {
          publicPath: '/projects/' + PROJECT + '/templates/' + TEMPLATE + '/dist/',
        },
        use: [
          {
            loader: "image-webpack-loader",
            options: {
              bypassOnDebug: true
            }
          }
        ]
      },
      {
        test: /\.(woff(2)?|ttf|eot|otf)(\?v=\d+\.\d+\.\d+)?$/,
        type: 'asset/resource',
        generator: {
          publicPath: "/projects/" + PROJECT + "/templates/frontend/dist/fonts/",
          outputPath: "fonts/",
          filename: "[name][ext]"
        },
      },
    ]
  },
  plugins: [
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      filename: DEVMODE ? "main.css" : "main.min.css",
      chunkFilename: DEVMODE ? "[id].css" : "[id].min.css",
    }),
  ],
};