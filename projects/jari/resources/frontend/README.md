Onderstaand<PERSON> runnen in /gsdfw/tools/

De volgende build mogelijkheden zijn be<PERSON> in de package json.
Draaien bijvoorbeeld met:
npm run jari-frontend-build-production

    "jari-frontend-build-production": css productie build
    "jari-frontend-build-development": css development build
    "jari-frontend-watch-development": css development build watch


**Animaties**
Er wordt gebruik gemaakt van animate.css
Zie https://animate.style/ voor documentatie
Voeg de class animate__animated toe + de animatie class die je wilt
