<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  container: {
    type: Object,
    default: null
  },
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'add-to-basket']);

const dialog = ref(null);
const quantity = ref(1);

watch(() => props.isOpen, (newValue) => {
  if (newValue && dialog.value) {
    dialog.value.showModal();
    quantity.value = 1; // Reset quantity when opening modal
  } else if (dialog.value) {
    dialog.value.close();
  }
});

function closeDialog() {
  emit('close');
}

function handleDialogClick(event) {
  if (event.target === dialog.value) {
    closeDialog();
  }
}

function handleKeydown(event) {
  if (event.key === 'Escape') {
    closeDialog();
  }
}

function handleAddToBasket() {
  emit('add-to-basket', { product: props.product, quantity: quantity.value });
  closeDialog();
}

function incrementQuantity() {
  quantity.value++;
}

function decrementQuantity() {
  if (quantity.value > 1) {
    quantity.value--;
  }
}
</script>

<template>
  <dialog
      ref="dialog"
      class="product-dialog"
      @click="handleDialogClick"
      @keydown="handleKeydown"
  >
    <div class="dialog-content" v-if="container && product">
      <div class="dialog-header">
        <h2>{{ container.content.name }}</h2>
        <button
            type="button"
            class="close-button"
            @click="closeDialog"
            aria-label="Close dialog"
        >
          ×
        </button>
      </div>

      <div class="dialog-body">
        <div class="info-card">
          <div class="card-header">
            <h3>Productgroep Specificaties</h3>
          </div>
          <div class="spec-grid">
            <div
                v-for="option in container.options"
                :key="option.code"
                class="spec-row"
            >
              <span class="spec-label">{{ option.name }}</span>
              <span class="spec-value">{{ option.value }}</span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="card-header">
            <h3>Product Specificaties</h3>
          </div>

          <div class="spec-grid" v-if="product.code">
            <div class="spec-row">
              <span class="spec-label">Artikelnummer</span>
              <span class="spec-value">{{ product.code }}</span>
            </div>
          </div>

          <div class="spec-grid" v-if="product.options && product.options.length">
            <div
                v-for="option in product.options"
                :key="option.code"
                class="spec-row"
                :class="{ 'highlight': option.name === 'Niet voor' }"
            >
              <span class="spec-label">{{ option.name }}</span>
              <span class="spec-value" :class="{ 'text-warning': option.name === 'Niet voor' }">
                {{ option.value }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <div class="quantity-section">
          <label for="quantity" class="quantity-label">Aantal:</label>
          <div class="quantity-controls">
            <button
              type="button"
              class="quantity-btn"
              @click="decrementQuantity"
              :disabled="quantity <= 1"
            >
              -
            </button>
            <input
              id="quantity"
              v-model.number="quantity"
              type="number"
              min="1"
              class="quantity-input"
            />
            <button
              type="button"
              class="quantity-btn"
              @click="incrementQuantity"
            >
              +
            </button>
          </div>
        </div>
        <button type="button" class="btn btn-primary" @click="handleAddToBasket">
          Toevoegen
        </button>
      </div>
    </div>
  </dialog>
</template>

<style scoped lang="scss">
.product-dialog {
  border: none;
  border-radius: 8px;
  padding: 0;
  max-width: 500px;
  width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: auto;

  &::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--color-primary, #007bff);
  color: white;
  border-radius: 8px 8px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;

  h2 {
    margin: 0;
    font-size: 1.25rem;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

.dialog-body {
  padding: 1.5rem;
}

.info-card {
  border-radius: 10px;
  border: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  h3 {
    margin: 0;
    font-size: 1rem;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.spec-row {
  display: flex;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
}

.spec-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  flex: 1;
}

.spec-value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 600;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e5e5;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;

  &.btn-secondary {
    background-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5a6268;
    }
  }

  &.btn-primary {
    background-color: var(--color-primary, #007bff);
    color: white;

    &:hover {
      background-color: var(--color-primary-dark, #0056b3);
    }
  }
}

.quantity-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quantity-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ced4da;
  border-radius: 4px;
  overflow: hidden;
}

.quantity-btn {
  background-color: #f8f9fa;
  border: none;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  transition: background-color 0.2s;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: #e9ecef;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:first-child {
    border-right: 1px solid #ced4da;
  }

  &:last-child {
    border-left: 1px solid #ced4da;
  }
}

.quantity-input {
  border: none;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  width: 60px;
  height: 36px;
  outline: none;
  background-color: white;

  /* Remove spinner arrows */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
}

@media (max-width: 768px) {
  .product-dialog {
    width: 95vw;
    max-width: none;
  }

  .spec-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
  }

  .dialog-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .quantity-section {
    justify-content: center;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .dialog-body, .dialog-footer {
    padding: 1rem 1.25rem;
  }

  .card-header, .spec-row {
    padding: 0.75rem 1.25rem;
  }
}
</style>