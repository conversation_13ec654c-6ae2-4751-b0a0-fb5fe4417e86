import { fileURLToPath, URL } from 'node:url';
import { resolve } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

// https://vitejs.dev/config/
export default defineConfig({
  css: {},
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@gsdvuefw': fileURLToPath(new URL('./../../../../../gsdfw/resources/vueapps/gsdvuefw', import.meta.url)),
      '@vueapps': fileURLToPath(new URL('./node_modules/', import.meta.url)),
    }
  },
  define: {
    public_dir: JSON.stringify('/projects/jari/templates/fastbolt'),
  },
  server: {
    port: 5174,
    fs: {
      allow: ['./../../../../../'], // Allow serving files from more levels up to the project root
    },
  },
  base: '', // required for correct path of images
  build: {
    outDir: __dirname + '/../../../../../projects/jari/templates/fastbolt', // output folder for build files
    assetsDir: '/assets', // assetsDir is relative to outDir
    minify: ((process.env.NODE_ENV === 'development') ? false : true), // minify based on environment
    rollupOptions: {
      output: {
        assetFileNames: (assetInfo) => { // create .min files only in production mode
          if(assetInfo.name !== 'index.css') return 'assets/[name].[ext]'; // only create .min files for index.css, not for other assets
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].[ext]' : 'assets/[name].min.[ext]';
        },
        chunkFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
        entryFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
      },
    },
  },
})