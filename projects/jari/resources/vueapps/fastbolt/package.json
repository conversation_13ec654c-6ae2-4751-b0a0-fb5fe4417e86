{"name": "jari-fastbolt", "version": "0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build-dev": "vite build --mode development", "watch-dev": "vite build --mode development --watch", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.8.4", "pinia": "^3.0.2", "sass": "^1.90.0", "vite": "^4.0.0", "vue": "^3.2.45", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "vite": "^4.4.8"}}