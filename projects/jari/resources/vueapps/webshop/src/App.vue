<template>
  <div id="main-container">
    <filters :category-id="categoryId"/>
    <overview :category-id="categoryId"/>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import Overview from "./components/Overview.vue";
import Filters from "./components/Filters.vue";

const categoryId = document.querySelector('#vuespa').dataset.categoryId;

onMounted(() => {
  // Hide the SEO section so vue can take over
  const seoSection = document.querySelector('#seo-section');
  seoSection.style.display = 'none';
})

</script>

<style>
#main-container {
  align-items: baseline;
}
</style>