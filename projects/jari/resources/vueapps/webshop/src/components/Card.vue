<script setup>
const props = defineProps(['name', 'url', 'imageUrl', 'type']);
const { name, url, imageUrl, type } = props;
</script>

<template>
  <a class="card" :href="url" :title="name">
    <div class="card-image">
      <img :src="imageUrl" :alt="name" loading="lazy">
    </div>
    <div class="card-description">
      <h2>{{ name }}</h2>
      <span class="btn-primary">{{ type }} bekijken</span>
    </div>
  </a>
</template>

<style scoped>

</style>