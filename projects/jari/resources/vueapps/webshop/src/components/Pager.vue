<script setup>
import { computed } from "vue";
import { usePagerStore } from "@/stores/pagerStore";

const pagerStore = usePagerStore();

function nextPage() {
  if (pagerStore.pageNumber < pagerStore.amountOfPages) {
    goToPage(pagerStore.pageNumber + 1);
  }
}

function previousPage() {
  if (pagerStore.pageNumber > 1) {
    goToPage(pagerStore.pageNumber - 1);
  }
}

function goToPage(page) {
  if (page === pagerStore.pageNumber) return;

  window.scrollTo({ top: 0, behavior: 'smooth' });
  pagerStore.pageNumber = page;
}

// Helper voor paginareeks met "..." indien nodig
const pageNumbers = computed(() => {
  const total = pagerStore.amountOfPages;
  const current = pagerStore.pageNumber;
  const pages = [];

  if (total <= 7) {
    for (let i = 1; i <= total; i++) pages.push(i);
  } else {
    pages.push(1);
    if (current > 4) pages.push("...");

    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);

    for (let i = start; i <= end; i++) pages.push(i);

    if (current < total - 3) pages.push("...");
    pages.push(total);
  }

  return pages;
});
</script>

<template>
  <div class="pager" v-if="pagerStore.pager">
    <p>Aantal: {{ pagerStore.pager.count }}</p>

    <a v-if="pageNumbers.length > 1" @click="previousPage" class="pager-arrow">
      <svg width="7" height="13" viewBox="0 0 7 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M0.410156 6.28516L5.66016 1.0625C5.90625 0.789062 6.31641 0.789062 6.58984 1.0625C6.83594 1.30859 6.83594 1.71875 6.58984 1.96484L1.77734 6.75L6.5625 11.5625C6.83594 11.8086 6.83594 12.2188 6.5625 12.4648C6.31641 12.7383 5.90625 12.7383 5.66016 12.4648L0.410156 7.21484C0.136719 6.96875 0.136719 6.55859 0.410156 6.28516Z"></path></svg>
    </a>

    <template v-if="pageNumbers.length > 1" v-for="(page, index) in pageNumbers" :key="index">
      <span v-if="page === '...'">...</span>
      <a
          v-else
          @click="goToPage(page)"
          :class="['pager-page', { active: page === pagerStore.pageNumber }]"
      >{{ page }}</a>
    </template>

    <a v-if="pageNumbers.length > 1" @click="nextPage" class="pager-arrow">
      <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M7.33984 6.28516C7.58594 6.55859 7.58594 6.96875 7.33984 7.21484L2.08984 12.4648C1.81641 12.7383 1.40625 12.7383 1.16016 12.4648C0.886719 12.2188 0.886719 11.8086 1.16016 11.5625L5.94531 6.77734L1.16016 1.96484C0.886719 1.71875 0.886719 1.30859 1.16016 1.0625C1.40625 0.789062 1.81641 0.789062 2.0625 1.0625L7.33984 6.28516Z"></path></svg>
    </a>
  </div>
</template>

<style scoped>
.pager {
  gap: .7em;
}
a:hover {
  cursor: pointer;
}
.active {
  color: #000;
  font-weight: 600;
}
.active:hover {
  color: #000;
  text-decoration: none;
  cursor: auto;
}
</style>