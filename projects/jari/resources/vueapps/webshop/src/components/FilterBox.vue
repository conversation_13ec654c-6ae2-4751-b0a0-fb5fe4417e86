<script setup>
import { ref } from "vue";

const props = defineProps(['title', 'collapse']);
const collapse = props.collapse !== undefined;

const collapsed = ref(false);

function toggleCollapse() {
  if (!collapse) return;
  collapsed.value = !collapsed.value;
}
</script>

<template>
  <aside class="filter-box">
    <div class="filter-title" @click="toggleCollapse">
      <span>{{ props.title }}</span>
      <svg v-if="collapse" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" :class="{ rotated: collapsed }" class="arrow-icon"><path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/></svg>
    </div>
    <transition name="collapse">
      <div class="filters" v-if="!collapsed" :class="{ collapsed }">
        <slot/>
      </div>
    </transition>
  </aside>
</template>

<style scoped>
.filter-box {
  margin-bottom: 20px;
}
.filter-title {
  width: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
}

.arrow-icon {
  transition: transform 0.3s ease;
  transform: rotate(0deg);
}
.arrow-icon.rotated {
  transform: rotate(180deg);
}

.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.4s ease;
  overflow: hidden;
}
.collapse-enter-from,
.collapse-leave-to {
  opacity: 0;
  max-height: 0;
  padding: 0 40px;
}
.collapse-enter-to,
.collapse-leave-from {
  opacity: 1;
  padding: 25px 40px;
  max-height: 250px;
}
</style>