<script setup>
import {onMounted, watch} from 'vue';
import Card from "./Card.vue";
import { usePagerStore } from "../stores/pagerStore.js";
import Pager from "./Pager.vue";
import { useLoadingStore } from "../stores/loadingStore";
import { useItemStore } from "../stores/itemStore.js";
import Loader from "./Loader.vue";

const props = defineProps(['categoryId']);

const itemStore = useItemStore();
const pagerStore = usePagerStore();
pagerStore.context = props.categoryId;

const loadingStore = useLoadingStore();
onMounted(async () => {
  await itemStore.fetchProductsAndCategories(props.categoryId);
})
watch(() => pagerStore.pageNumber, async () => {
  await itemStore.fetchProductsAndCategories(props.categoryId);
})
watch(() => itemStore.selectedFilters,async () => {
    pagerStore.pageNumber = 1;
    await itemStore.fetchProductsAndCategories(props.categoryId);
  },
  { deep: true }
);
</script>

<template>
  <div class="overview">
    <loader v-if="loadingStore.isLoading"/>
    <card
        v-for="product in itemStore.products"
        :key="product.id"
        :name="product.content.name"
        :url="product.content.url"
        :imageUrl="product.imageUrl"
        type="Product"
    />
    <card
        v-for="category in itemStore.categories"
        :key="category.id"
        :name="category.content.name"
        :url="category.content.url"
        :imageUrl="category.imageUrl"
        type="Categorie"
    />
    <pager/>
  </div>
</template>

<style scoped>
.overview {
  position: relative;
  min-height: 400px;
}
</style>