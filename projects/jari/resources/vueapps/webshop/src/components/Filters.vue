<script setup>
import FilterBox from "./FilterBox.vue";
import { onMounted, ref } from "vue";
import axios from "axios";
import { useItemStore } from "../stores/itemStore.js";

const props = defineProps(['categoryId']);
const itemStore = useItemStore();

const categories = ref();
onMounted(async () => {
  const response = await axios.get('?action=getCategoriesAjax')
  categories.value = response.data.categories;
})
</script>

<template>
  <div>
    <filter-box title="Alle producten">
      <a v-for="category in categories" class="filter" :class="{ active: category.id == props.categoryId }" :href="category.content.url" :title="category.content.name">
        {{ category.content.name }}
      </a>
    </filter-box>
    <template v-if="itemStore.products.length">
      <filter-box v-for="option in itemStore.options" :key="option.key" :title="option.label" collapse>
        <a v-for="filter in option.values" :key="filter" class="filter" :title="filter">
          <input
              :value="filter"
              v-model="itemStore.selectedFilters[option.key]"
              type="checkbox"
              class="custom-checkbox"
              :id="`${option.key}-${filter}`"
          >
          <label class="filter-label" :for="`${option.key}-${filter}`">{{ filter }}</label>
        </a>
      </filter-box>
    </template>
  </div>
</template>

<style scoped>
.filter-label {
  display: inline-block;
  width: calc(100% - 22px);
  padding-left: 1em;
  cursor: pointer;
}
</style>