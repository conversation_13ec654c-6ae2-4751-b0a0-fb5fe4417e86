import { defineStore } from 'pinia'
import { ref, watch, computed } from 'vue'

export const usePagerStore = defineStore('pager', () => {
  const context = ref('default') // bijvoorbeeld 'products', 'categories', etc.

  const storageKey = computed(() => `pager:pageNumber:${context.value}`)

  const pageNumber = ref(1)

  // Initialiseren op basis van context
  watch(context, (newContext) => {
    const storedValue = localStorage.getItem(storageKey.value)
    pageNumber.value = storedValue ? parseInt(storedValue) : 1
  }, { immediate: true })

  // Opslaan bij verandering van pagina
  watch(pageNumber, (value) => {
    localStorage.setItem(storageKey.value, value.toString())
  })

  const pager = ref()
  const amountOfPages = computed(() => {
    if (!pager.value) return 0
    return Math.ceil(pager.value.count / pager.value.rowsPerPage)
  })

  return { context, pageNumber, amountOfPages, pager }
})