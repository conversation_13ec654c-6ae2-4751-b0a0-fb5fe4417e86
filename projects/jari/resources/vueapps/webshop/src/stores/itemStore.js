import { defineStore } from 'pinia'
import { ref, watch, computed } from 'vue'
import axios from "axios";
import { useLoadingStore } from './loadingStore'
import { usePagerStore } from './pagerStore'

export const useItemStore = defineStore('item', () => {
  const loadingStore = useLoadingStore();
  const pagerStore = usePagerStore();

  const categories = ref([]);
  const products = ref([]);
  const options = ref();
  const selectedFilters = ref({});
  async function fetchProductsAndCategories(categoryId) {
    loadingStore.isLoading = true;
    try {
      const productsParams = new URLSearchParams({
        action: 'getProductContainersAjax',
        categoryId: categoryId,
        pageNum: pagerStore.pageNumber
      });
      Object.entries(selectedFilters.value).forEach(([key, values]) => {
        if (values && values.length > 0) {
          values.forEach(value => {
            productsParams.append(`filters[${key}][]`, value);
          });
        }
      });

      const productsResponse = await axios.get(`?${productsParams.toString()}`);
      products.value = productsResponse.data.products;
      options.value ??= productsResponse.data.options;

      if (products.value.length) pagerStore.pager = productsResponse.data.pager;

      const categoriesParams = new URLSearchParams({
        action: 'getCategoriesAjax',
        categoryId: categoryId,
        pageNum: pagerStore.pageNumber
      });

      const categoriesResponse = await axios.get(`?${categoriesParams.toString()}`);
      categories.value = categoriesResponse.data.categories;
      if (categories.value.length) pagerStore.pager = categoriesResponse.data.pager;

      options.value.forEach(option => {
        if (!selectedFilters.value[option.key]) {
          selectedFilters.value[option.key] = [];
        }
      });
    } catch (error) {
      console.error('Error fetching items:', error);
    } finally {
      loadingStore.isLoading = false;
    }
  }

  return { products, categories, options, fetchProductsAndCategories, selectedFilters }
})