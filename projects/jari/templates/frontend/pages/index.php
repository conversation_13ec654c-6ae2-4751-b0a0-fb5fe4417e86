<?php
  include(DIR_INCLUDES . "headercode_frontend.inc.php");

  if (!isset($seo_title) || $seo_title == "") {
    $seo_title = Navigation::getItem($pageId)->getName();
  }
  if (isset($seo_description) && $seo_description != null) {
    $seo_description = StringHelper::cleanAndEscape($seo_description);
  }

  Context::addMetatag("description", $seo_description, true);
  Context::addMetatag("author", "JARI Systems NV", true);
  Context::addMetatag("copyright", "JARI Systems NV", true);

  //$seo_image nog verder uitwerken en zetten in product detail scherm. (zie ook tuinberegening)
  $site->addOpengraphMeta('JARI Systems NV', $seo_title, $seo_description, $seo_image);

?>
<!DOCTYPE html>
<html lang="nl-be">
<head>

  <meta charset="UTF-8">
  <title><?php echo $seo_title ?></title>

  <?php Context::printMetatags(); ?>

  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <?php $site->writeStructuredDataScript(); ?>

  <?php Context::printStylesheets() ?>
  <?php Context::printJavascripts(); ?>

  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'dist/main', true) ?>

  <link rel="shortcut icon" href="<?php echo $site->getTemplateUrl() ?>images/favicon.ico" type="image/x-icon"/>

  <!-- Base JS libraries -->
  <script src="/gsdfw/includes/jsscripts/jquery/jquery-1.10.2.min.js"></script>
  <script src="/gsdfw/includes/jsscripts/general.js?v=<?php echo get_asset_version(DIR_INCLUDES . 'jsscripts/general.js'); ?>"></script>

  <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">


  <?php if (!DEVELOPMENT): ?>

    <?php // Analytics ?>

  <?php endif; ?>

</head>
<body>
<?php TemplateHelper::includeComponent('header', 'site'); ?>

<?php include('_flashmessage.php') ?>

<?php if (isset($showBreadcrumbs) && $showBreadcrumbs === true): ?>
  <?php include('_breadcrumb.php') ?>
<?php endif; ?>

<?php TemplateHelper::includeComponent('cookiebanner', 'site'); ?>

<main>
  <?php
    if ($current_action != null) {
      include($current_action->getTemplatePath());
    }
  ?>
</main>

<?php TemplateHelper::includeComponent('footer', 'site'); ?>

<?php Context::printStylesheets(true) ?>
<?php Context::printJavascripts(true); ?>

</body>

<script type="text/javascript">
  $(document).ready(function () {
    if (!localStorage.getItem("consentSet")) {
      $("body").css('overflow', 'hidden')

      $("#cookie-banner").css('display', 'block');
      $("#consent-deny, #consent-allow-all").on('click', function () {
        updateCookieBanner();
      })
    }

    // Initialize Intersection Observer
    const observer = new IntersectionObserver(
      function (entries) {
        entries.forEach(function (entry, index) {
          if (entry.isIntersecting) {
            setTimeout(function () { // add the custom fadeinup event with a small delay
              $(entry.target).css('visibility', 'visible').addClass('animate__fadeInUpJari');
              observer.unobserve(entry.target);
            }, index * 250);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px'
      }
    );

    // Observe all elements
    $('.animate__animated').each(function () {
      observer.observe(this);
    });
  })
</script>
</html>
