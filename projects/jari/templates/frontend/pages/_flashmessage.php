<?php

  // added wrapper to each message group for additional styling

//    MessageFlashCoordinator::addMessage('Dit is een test bericht voor normale messages.');
//  MessageFlashCoordinator::addMessage('Dit is een test bericht voor een heel erg lang normaal bericht. Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ad alias aperiam aut deserunt dolor, dolorem doloremque ducimus eligendi exercitationem facilis impedit incidunt laudantium mollitia nam obcaecati officiis placeat quae qui quibusdam quos, rem repudiandae saepe suscipit tempora tempore temporibus voluptas! Commodi consequatur cum dolorum ea minus provident quia reiciendis rem?');
//    MessageFlashCoordinator::addMessageAlert('Dit is een test bericht voor alert messages.');
//  MessageFlashCoordinator::addMessageAlert('Dit is een test bericht voor een heel erg lang alert bericht. Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ad alias aperiam aut deserunt dolor, dolorem doloremque ducimus eligendi exercitationem facilis impedit incidunt laudantium mollitia nam obcaecati officiis placeat quae qui quibusdam quos, rem repudiandae saepe suscipit tempora tempore temporibus voluptas! Commodi consequatur cum dolorum ea minus provident quia reiciendis rem?');
?>
<section id="flashmessage">
  <div id="message-container">
    <?php
      $msc = MessageFlashCoordinator::getInstance();
      $msc->setOldmessages();

      $out = "";
      $messages_grouped = ArrayHelper::groupBy($msc->getMessages(), 'type');
      foreach ($messages_grouped as $message_type => $messages) {
        $class = MessageFlashCoordinator::$classes[$message_type] ?? MessageFlashCoordinator::$classes['default'];
        $out .= "<div class='alert-wrapper " . $class . "'>";
        $out .= '<div class="' . $class . '">';
        foreach ($messages as $message) {
          $out .= "<span class='alert-message'>" . $message->message . "</span>";
        }
        $out .= '</div>';
        $out .= '</div>';
      }

      echo $out;

      MessageFlashCoordinator::clearMessages();
    ?>
  </div>
</section>
