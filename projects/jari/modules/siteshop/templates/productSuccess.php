<div class="gsd-editor">
  <section class="gsd-container gsd-wide-container equal-width-columns">
    <div>
      <div class="product-image">
        <img src="<?php echo $product_container->imageUrl ?>" alt="<?php echo $product_container->content->name ?>" loading="lazy">
      </div>
      <div>
        <h1><?php echo StringHelper::convertFraction($product_container->content->name) ?></h1>
        <div class="products-attributes">
          <?php foreach ($product_container->options as $option): ?>
            <div class="attribute-row">
              <div class="attribute-label"><?php echo $option->name ?></div>
              <div class="attribute-value"><?php echo $option->value ?></div>
            </div>
          <?php endforeach; ?>
        <p><?php echo $product_container->content->description ?></p>
      </div>
    </div>
  </section>

  <section class="products-details gsd-container gsd-wide-container bg-gray">
    <div>
      <h2>Maten</h2>
    </div>
    <div>
      <?php TemplateHelper::includePartial('_fastbolt.php', 'siteshop', compact('product_container')); ?>
    </div>
  </section>

  <?php if (!empty($product_container->related)): ?>
  <section class="products-details gsd-container gsd-wide-container">
    <div>
      <h2>Bekijken ook deze producten in ons gamma</h2>
    </div>
    <div>
      <div class="carousel">
        <?php TemplateHelper::includePartial('_productOverview.php', 'siteshop', ['products' => $product_container->related]); ?>
      </div>
    </div>
  </section>
  <?php endif; ?>

  <section class="gsd-container gsd-wide-container">
    <div>
      <?php TemplateHelper::includeComponent('questions', 'site') ?>
    </div>
  </section>
</div>

