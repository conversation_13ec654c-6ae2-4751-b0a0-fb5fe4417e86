<div class="gsd-editor">
  <section class="gsd-container header-block small-block">
    <div>
      <h1><?php echo StringHelper::convertFraction($category->content->name) ?></h1>
      <p><?php echo $category->content->description ?></p>
    </div>
  </section>

  <section class="gsd-container gsd-wide-container" id="seo-section">
    <div>
      <div class="loader">
        <i class="fa fa-circle-o-notch fa-spin fa-3x"></i>
      </div>
      <div>
        <aside class="filter-box">
          <div class="filter-title">Alle Producten</div>
          <div class="filters">
            <?php foreach ($categories as $item): ?>
              <a class="filter <?php if (in_array($item->id, [$category->id, $category->parent_id])) echo "active" ?>"
                 href="<?php echo $item->content->getUrl() ?>" title="<?php echo $item->content->name ?>">
                <?php echo $item->content->name ?>
              </a>
            <?php endforeach; ?>
          </div>
        </aside>
      </div>
      <?php if (ArrayHelper::hasData($subCategories) || ArrayHelper::hasData($products)): ?>
        <div class="overview">
          <?php if (ArrayHelper::hasData($subCategories)): ?>
            <?php TemplateHelper::includePartial('_categoryOverview.php', 'siteshop', ['categories' => $subCategories]); ?>
          <?php else: ?>
            <?php TemplateHelper::includePartial('_productOverview.php', 'siteshop', compact('products')); ?>
          <?php endif; ?>
          <div class="pager">
            <?php $pager->writePreviousNext(); ?>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </section>

  <?php TemplateHelper::includePartial('_webshop.php', 'siteshop'); ?>

  <section class="gsd-container gsd-wide-container">
    <div>
      <?php TemplateHelper::includeComponent('questions', 'site'); ?>
    </div>
  </section>
</div>

<script>
  $(document).ready(function () {
    $('.loader').hide();
  });
</script>