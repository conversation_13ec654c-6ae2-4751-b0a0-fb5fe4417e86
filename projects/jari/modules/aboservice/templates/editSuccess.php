<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
</section>

<section class="module-description with-padding">
  <p>Op deze pagina kun je de logingegevens wijzigen en de token resetten voor de Aboservice API.</p>
</section>

<?php /* @var array $errors */ ?>
<?php writeErrors($errors); ?>

<form method="post" class="edit-form with-padding">
  <div class="form-row">
    <label class="form-label" for="username">Gebruikersnaam*</label>
    <input type="text" placeholder="gebruikersnaam..." name="username"
           value="<?php if(!empty($username->value)) echo $username->value ?>" required>
  </div>
  <div>
    <label class="form-label" for="password">Wachtwoord*</label>
    <input id="password" type="text" placeholder="wachtwoord..." name="password"
           value="<?php if(!empty($password->value)) echo EncryptionHelper::decrypt($password->value) ?>" required>
  </div>
  <br>
  <input class="gsd-btn gsd-btn-primary" type="submit" name="save" value="Opslaan">

  <?php if ($_SESSION["userObject"]->usergroup == User::USERGROUP_SUPERADMIN): ?>
    <br><br>
    <label class="form-label">SUPERADMIN</label>
    <input class="gsd-btn gsd-btn-secondary gsd-confirm" title="Resetten" type="submit" name="reset" value="Resetten">
    <?php if (!empty($username->id) && !empty($password->id)): ?>
      <input class="gsd-btn gsd-btn-danger gsd-confirm" title="Verwijderen" type="submit" name="delete" value="Verwijderen">
    <?php endif; ?>
  <?php endif; ?>

</form>