<?php

  class aboserviceJariActions extends gsdActions {

    public function executeEdit() {
      $username = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-username');
      if (!$username) {
        $username = new Setting([
          'type' => 'ABOSERVICE',
          'code' => 'aboservice-username'
        ]);
      }

      $password = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-password');
      if (!$password) {
        $password = new Setting([
          'type' => 'ABOSERVICE',
          'code' => 'aboservice-password'
        ]);
      }

      $errors = [];
      if (isset($_POST['save'])) $errors = $this->saveSettings($username, $password);
      if (isset($_POST['reset'])) $this->resetSettings();
      if (isset($_POST['delete'])) $this->deleteSettings($username, $password);

      $this->username = $username;
      $this->password = $password;
      $this->errors = $errors;
    }

    private function saveSettings($username, $password) {
      $errors = [];
      $username->value = $_POST['username'];
      if ($username->value == "") {
        $errors['username'] = "Gebruikersnaam is verplicht.";
      }

      if ($_POST['password'] == "") {
        $errors['password'] = "Wachtwoord is verplicht.";
      }

      if (count($errors) === 0) {
        $username->save();

        $password->value = EncryptionHelper::encrypt($_POST['password']);
        $password->save();

        ResponseHelper::redirectMessage("Gebruikersnaam en wachtwoord zijn opgeslagen.", reconstructQuery());
      }

      return $errors;
    }

    private function resetSettings() {
      $token = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-token');
      if ($token) $token->destroy();

      $tokenDateTime = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-token-datetime');
      if ($tokenDateTime) $tokenDateTime->destroy();

      MessageFlashCoordinator::addMessage("Koppeling is gereset.");
    }

    private function deleteSettings($username, $password) {
      $username->destroy();
      $password->destroy();

      ResponseHelper::redirectMessage("Koppeling is verwijderd.", reconstructQuery());
    }

  }
