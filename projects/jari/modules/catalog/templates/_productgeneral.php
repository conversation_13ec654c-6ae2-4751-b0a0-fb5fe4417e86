<?php if (isset($_POST['id']) || isset($_GET['id'])) : ?>
  <input type="hidden" name="id" value="<?php writeIfSet('id') ?>"/>
<?php elseif ($product_id != null) : ?>
  <input type="hidden" name="id" value="<?php echo $product_id ?>"/>
<?php endif; ?>
<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td colspan="2">Instellingen</td>
  </tr>

  <tr class="dataTableRow">
    <td class="head">Product container</td>
    <td>
      <a href="<?php echo PageMap::getUrl('M_PRODUCTCONT') . '&action=edit&id=' . $product_container->id . '&tabscontent_active=link_products'; ?>"
         class="gsd-btn gsd-btn-secondary">
        <?php echo $product_container->code; ?>
      </a>
    </td>
  </tr>
  <tr class="dataTableRow">
    <td class="head" style="width: 160px;"><?php echo __("Product code") ?></td>
    <td><input type="text" name="code" id="code" value="<?php echo $product->code ?>" maxlength="45"/> <span class="asterisk">*</span></td>
  </tr>
  <?php foreach (ProductOption::CODES as $code => $label): ?>
    <tr class="dataTableRow">
      <td class="head" style="width: 160px;"><?php echo $label ?></td>
      <td><input type="text" name="options[<?php echo $code ?>]" id="option_<?php echo $code ?>" value="<?php echo $product->product_options[$code]->value ?>"/></td>
    </tr>
  <?php endforeach; ?>
</table>
<?php if (count(Config::get("catalog_languages")) > 1) { ?>
  <div style="padding: 5px 5px;vertical-align: middle; border-right: 1px solid #C5D5DD;border-left: 1px solid #C5D5DD;">
  <script type="text/javascript">
    $(document).ready(function () {
      var lang = <?php echo json_encode(LanguageHelper::getLanguagesNL()) ?>;

      $('a.languageselect').click(function (item) {
        $('a.languageselect img').css('border', '1px solid white');
        $('div.languagebox').hide();
        $(this).find("img").css('border', '1px solid blue');
        $('#content_' + $(this).attr("id")).show();
        $('#currentlanguage').text(lang[$(this).attr("id")]);
      });
      $('#<?php echo isset($_SESSION['worklanguage']) ? $_SESSION['worklanguage'] : 'nl' ?>').click();
    });
  </script>
  <?php
  foreach (Config::get("catalog_languages") as $catlang) {
    echo '<a href="javascript:void(0);" class="languageselect" id="' . $catlang . '"><img alt="' . $catlang . '" src="/gsdfw/images/flags/' . $catlang . '.png" style="width: 22px; height: 15px;margin-right: 5px;"></a>';
  }
  echo ' Huidige taal: <span id="currentlanguage" style="font-weight:bold;"></span>';
  ?></div><?php
}
  foreach (Config::get("catalog_languages") as $catlang):
    ?>
    <div id="content_<?php echo $catlang ?>" class="languagebox" style="<?php if (count(Config::get("catalog_languages")) == 1): ?>display: block;<?php endif; ?>;">
      <table class="default_table notopborder">
        <tr class="dataTableRow">
          <td class="head" style="width: 160px;">Naam <?php echo strtoupper($catlang) ?></td>
          <td><input type="text" name="name[<?php echo $catlang ?>]" id="name_<?php echo $catlang ?>" value="<?php echo escapeForInput($product->contents[$catlang]->name) ?>"
                     maxlength="255"/> <span class="asterisk">*</span></td>
        </tr>

        <?php if (defined("SEOTITLE") && SEOTITLE): ?>
          <tr class="dataTableRow">
            <td valign="top" class="head">SEO titel <?php echo strtoupper($catlang) ?> </td>
            <td><input type="text" maxlength="255" name="seo_title[<?php echo $catlang ?>]" id="seo_title[<?php echo $catlang ?>]"
                       value="<?php echo $product->contents[$catlang]->seo_title ?>"
                       style="width:590px;"/> <?php echo showHelpButton("Dit veld word getoont bovenin de blauwe balk van uw webbrowser. Het is verstandig om een goede en duidelijke titel te gebruiken met eventueel een paar keer uw belangrijkste zoekwoorden. Indien dit veld leeg is word de gewone titel gebruikt.", 'SEO titel') ?>
            </td>
          </tr>
        <?php endif; ?>

        <?php if (defined("SEODESCRIPTION") && SEODESCRIPTION) : ?>
          <tr class="dataTableRow">
            <td valign="top" class="head">SEO omschrijving <?php echo strtoupper($catlang) ?></td>
            <td valign="top">
              <textarea class="seo_desc form-control lengthcounter" data-lengthcounter="200" name="seo_desc[<?php echo $catlang ?>]" id="seo_desc[<?php echo $catlang ?>]"
                        style="width:590px;vertical-align: top;"><?php echo $product->contents[$catlang]->seo_desc ?></textarea>
              <?php echo showHelpButton("Dit veld word gebruikt voor de meta-tag description in uw pagina. Vaak word dit stukje tekst in de zoekresultaten getoont van google als korte omschrijving. Het is verstandig om in dit veld in een korte omschrijving ook een aantal keer uw belangrijkste zoekwoorden terug te laten komen. Gebruik maximaal 200 karakters in dit veld. Indien dit veld leeg is, zullen de eerste 200 karakters van de omschrijving in deze tag geplaatst worden.", 'SEO omschrijving') ?>
              <input readonly type="text" class="lengthcounter-length" name="clength_<?php echo $catlang ?>" size="3" maxlength="3" value="200" style="width: 60px;"/>
            </td>
          </tr>
        <?php endif; ?>

        <tr class="dataTableRow">
          <td valign='top' class="head">Zoekwoorden <?php echo strtoupper($catlang) ?></td>
          <td>
            <select class="keywords" name="keywords[<?php echo $catlang ?>][]" id="keywords[<?php echo $catlang ?>]" multiple="multiple" style="width: 80%;">
              <?php foreach ($product->contents[$catlang]->getKeywordsAr() as $keyword): ?>
                <option value="<?php echo $keyword ?>" selected><?php echo $keyword ?></option>
              <?php endforeach; ?>
            </select>
            <?php echo showHelpButton("Woorden waarbij dit product ook gevonden moet worden.", 'Zoekwoorden') ?>
          </td>
        </tr>
      </table>
    </div>
  <?php endforeach; ?>
<table class="default_table notopborder">
  <tr class="dataTableRow">
    <td class="head" style="width: 160px;">Online</td>
    <td><input type="checkbox" value="1" name="online_custshop" <?php writeIfCheckedVal($product->online_custshop, 1) ?>/>
      <?php echo showHelpButton('Dit product is online beschikbaar. Dit kan zijn op webshop of in bijvoorbeeld de voorraad app.', 'Online'); ?>
    </td>
  </tr>
  <?php if (defined('TAGS_PRODUCT') && TAGS_PRODUCT): ?>
    <tr class="dataTableRow">
      <td class="head">Tags</td>
      <td><?php
          $tags = Tag::getTagsAndContent("nl", "", 'ORDER BY name');
          $out = '';
          for ($tel = 1; $tel <= 5; $tel++) {
            $out .= '<select name="tag' . $tel . '">';
            $out .= '<option value="">Selecteer...</option>';
            foreach ($tags as $tag) {
              $out .= '<option value="' . $tag->id . '" ' . ($product->getTag($tel) == $tag->id ? 'selected' : '') . '>' . $tag->getName('nl') . '</option>';
            }
            $out .= '</select>';
          }
          echo $out;
        ?><?php echo showHelpButton("Met tags kun je een groepering aangeven van producten. Dit is goed voor de vindbaarheid.", 'Tags') ?></td>
    </tr>
  <?php endif; ?>
  <tr class="dataTableRow">
    <td class="head">Interne opmerking</td>
    <td><textarea style="width:590px;vertical-align: top;height:60px;" name="internal_remark"
                  class="form-control <?php echo isset($errors['internal_remark']) ? 'inputerror' : ''; ?>"><?php echo $product->internal_remark ?></textarea></td>
  </tr>
</table>
<script type="text/javascript">
  $(document).ready(function () {

    $(".keywords").select2({
      tags: true,
      tokenSeparators: [',']
    });

    lengthCounter();
  });
</script>

