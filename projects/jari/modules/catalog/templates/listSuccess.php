<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem($current_action->pageId)->getName() ?>
  </h1>

  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>

<?php
  $extra = '<form action="' . reconstructQueryAdd(['catid']) . '" method="post">';
  $extra .= '<div class="box">';


  if($may_add_cat) {
    $extra .= ' <input type="button" value="' . __('Nieuwe categorie toevoegen') . '" onclick="location.href=\'' . reconstructQueryAdd(['pageId']) . 'action=categoryedit&parent_id=' . $catid . '\';" class="gsd-btn gsd-btn-primary"/>';
  }
  if($may_add_prod) {
    $extra .= ' <a href="' . PageMap::getUrlBackendRouting('M_PRODUCTCONT') . '?action=edit" class="gsd-btn gsd-btn-primary">' . __('Nieuw product toevoegen') . '</a>';
  }

  $extra .= ' <input type="button" id="sortablecontrol" class="gsd-btn gsd-btn-secondary" name="" value="' . __('Sorteren aanzetten') . '" />';
  if(Config::get("PRODUCTS_ORDER_BY_NAME", true)) {
    $extra .= ' ' . showHelpButton('Sorteren werkt alleen maar op categoriëen en niet op producten.', 'Sorteren');
  }

  $extra .= '</div>';
  echo $extra;

//  $pager->writePreviousNext();

?>
<div id="message"></div>
<?php if(isset($categories) && count($categories) > 0): ?>

  <table id="cat" class="sortable default_table">

    <tr class="dataTableHeadingRow nodrop nodrag">
        <td class="sort-td"></td>
      <td style="width: 50px;"><?php echo __('Type'); ?></td>
      <td><?php echo __('Naam'); ?></td>
      <td style="text-align: center;width: 60px;" class="qtipa"
          title="<?php echo __('Zet categorie online/offline'); ?>"><?php echo __('Online<br/>shop'); ?></td>
      <?php if(Config::get("CATEGORY_USE_ONLINE_UC", true)): ?>
        <td style="text-align: center; width: 60px;" class="qtipa"
            title="<?php echo __('Zet category special online/offline.') ?>">
          <?php echo __('Online<br/>special'); ?>
        </td>
      <?php endif; ?>
      <?php if(Config::get("CATEGORY_USE_ONLINE_ADMIN", true)): ?>
        <td style="text-align: center; width: 60px;" class="qtipa"
            title="<?php echo __('Zet categorie admin online/offline.') ?>">
          <?php echo __('Online<br/>admin'); ?>
        </td>
      <?php endif; ?>
      <td class="gsd-svg-icon-width-2"><?php echo __('Acties') ?></td>
    </tr>

    <?php foreach ($categories as $category): ?>
      <tr class="dataTableRow trhover" id="category_<?php echo $category->id ?>">
        <td class="sort-td"><span class="fa fa-bars"></span></td>
        <td style="padding-left: 5px;"><?php echo IconHelper::getFolder() ?></td>
        <td><a
            href="<?php echo reconstructQueryAdd(['pageId']) ?>catid=<?php echo $category->id ?>"><?php echo $category->content->name ?></a>
        </td>
        <td style="text-align: center;">
          <a href="#" class="toggleactive" style="display: block;" data-type="category" data-kind="online_custshop"
             data-value="<?php echo !$category->online_custshop ? 1 : 0; ?>">
            <?php echo $category->online_custshop==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
          </a>
        </td>
        <?php if(Config::get("CATEGORY_USE_ONLINE_UC", true)): ?>
          <td style="text-align: center;">
            <a href="#" class="toggleactive" data-type="category" data-kind="online_uc"
               data-value="<?php echo !$category->online_uc ? 1 : 0; ?>">
              <?php echo $category->online_uc==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
            </a>
          </td>
        <?php endif; ?>
        <?php if(Config::get("CATEGORY_USE_ONLINE_ADMIN", true)): ?>
          <td style="text-align: center;">
            <a href="#" class="toggleactive" data-type="category" data-kind="online_admin"
               data-value="<?php echo !$category->online_admin ? 1 : 0; ?>">
              <?php echo $category->online_admin==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
            </a>
          </td>
        <?php endif; ?>
        <td>
          <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=categoryedit&id=' . $category->id) ?>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=categorydelete&id=' . $category->id, __('Let op: U verwijdert ook eventuele subcategorieën samen met de producten die daar in zitten!\n\nWeet u zeker dat u deze categorie wilt verwijderen?')."\n".$category->content->name) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
  <br />
<?php endif; ?>
<?php if(isset($products) && count($products) > 0): ?>
  <table cellspacing="0" cellpadding="0" id="product_container" class="sortable default_table">
    <tr class="dataTableHeadingRow nodrop nodrag">
      <td class="sort-td"></td>
      <td style="width: 40px;">Type</td>
      <td><?php echo __('Code'); ?></td>
      <td><?php echo __('Naam'); ?></td>
      <?php if(Config::get('CATALOG_LIST_SUPPLIER_SHOW', true)) : ?>
        <td><?php echo __('Leveranciercode'); ?></td>
      <?php endif; ?>
      <td><?php echo __('Dinnr'); ?></td>
      <td><?php echo __('Kwaliteit'); ?></td>
      <td class="gsd-svg-icon-width-2"><?php echo __('Acties')?></td>
    </tr>
    <?php
    /** @var Product $product */
    foreach ($products as $product):
      if(!isset($cats[$product->category1_id])) {
        $cat = Category::find_by_id($product->category1_id);
        if($cat) {
          $cats[$product->category1_id] = $cat;
        }
      }
      ?>
      <tr class="dataTableRow trhover" id="p_<?php echo $product->id ?>">
        <td class="sort-td"><span class="fa fa-bars"></span></td>
        <td style="padding-left: 5px;"><?php echo IconHelper::getFile() ?></td>
        <td>
          <a href="<?php echo PageMap::getUrl('M_PRODUCTCONT') ?>?action=edit&id=<?php echo $product->id ?>">
            <?php echo $product->code ?>
          </a>
        </td>
        <td>
          <a href="<?php echo PageMap::getUrl('M_PRODUCTCONT') ?>?action=edit&id=<?php echo $product->id ?>">
            <?php echo $product->content->name ?>
          </a>
        </td>
        <?php if(Config::get('CATALOG_LIST_SUPPLIER_SHOW', true)) : ?>
          <td><?php echo $product->supplier_code; ?></td>
        <?php endif; ?>
        <td><?php echo $product->model; ?></td>
        <td><?php echo $product->content->material; ?></td>
        <td>
          <?php echo BtnHelper::getEdit(PageMap::getUrl('M_PRODUCTCONT')  . '?action=edit&id=' . $product->id) ?>
          <?php echo BtnHelper::getRemove(PageMap::getUrl('M_PRODUCTCONT')  . '?action=productcontdelete&id=' . $product->id, __('Weet u zeker dat u deze productcontainer wilt verwijderen?')) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>

<?php if(!((isset($categories) && count($categories) > 0) || (isset($products) && count($products) > 0))): ?>
  <br />
  <?php echo __("Geen items gevonden"); ?>
<?php endif; ?>

<script type="text/javascript">
  $(document).ready(function () {

    $("#a_search").trigger("focus")

    function setupTableDnD() {
      $("#sortablecontrol").one("click", function () {
        $(".sort-td").show();
        $("#sortablecontrol")
          .val('<?php echo __('Sorteren uitzetten');?>')
          .addClass('sorton');
        $(".sortable").tableDnD({
          onDrop: function(table, row) {
            $('#message')
              .addClass("message")
              .text('Volgorde opslaan...')
              .load("<?php echo reconstructQueryAdd(['pageId', 'catid']) ?>action=move&type="+$(table).attr('id'), { values: $.tableDnD.serialize('id')}, function() {
                $('#message').text('Volgorde opgeslagen');
              });
          }
        });

        $(this).one("click",function () {
          $(".sort-td").hide();
          $(".sortable tr").unbind().css({cursor: "default"});
          $("#sortablecontrol").val('Sorteren aanzetten').removeClass('sorton');
          setupTableDnD();
        });
      });
    }
    setupTableDnD();

    $(".toggleactive").click(function (event) {
      event.preventDefault();
      var current = $(this);
      var id      = current.parents("tr:first").attr('id').split('_')[1];
      var data    = current.data();

      var url = "<?php echo reconstructQueryAdd(['pageId']); ?>action=toggleactive" + data.type + "&id=" + id + "&value=" + data.value;
      if (data.category) {
        url += "&category1_id=" + data.category;
      }
      if (data.kind) {
        url += "&kind=" + data.kind;
      }
      $.getJSON(url).done(function (result) {
        var msg   = "<?php echo __('Wijzigingen NIET opgeslagen!');?>";
        var color = "red";
        if (result && result.result == "OK") {
          msg   = "<?php echo __('Wijzigingen succesvol opgeslagen');?>";
          color = "";

          //set new data
          var newvalue = data.value == "1" ? 0 : 1;
          current.data("value", newvalue);
          current.html(newvalue==1?'<?php echo IconHelper::getCheckboxOff() ?>':'<?php echo IconHelper::getCheckbox() ?>')

          <?php if(!Config::isdefined("PRODUCT_ONLINE_CATEGORY_ONLINE_LINKED") || Config::get("PRODUCT_ONLINE_CATEGORY_ONLINE_LINKED") == true): ?>
          if (data.type == "product" && (!data.kind || data.kind == "online_custshop")) { //ook category_product offline
              current.parents("tr:first").find("[data-type=categoryproduct]").html(newvalue==1?'<?php echo IconHelper::getCheckboxOff() ?>':'<?php echo IconHelper::getCheckbox() ?>');
          }
          <?php endif; ?>

        }
        $('#message').html('<div class="message"' + (color != "" ? 'style="color: ' + color + ';"' : '') + '>' + msg + '</div><br/>');
      });
    });
  });
</script>