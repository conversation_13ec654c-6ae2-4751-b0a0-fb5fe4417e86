<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem($current_action->pageId)->getName() ?>
  </h1>

  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>

<?php /** @var ListFilter $list_filter */ ?>
<?php echo $list_filter->renderFormOpen(); ?>
<?php echo $list_filter->renderSearch('search'); ?>
  <select name="category">
    <option value="">Filter op categorie</option>
    <?php foreach($categories as $category): ?>
      <optgroup label="<?php echo $category->getName() ?>">
        <?php if(isset($category->children)): ?>
        <?php foreach($category->children as $category_child): ?>
        <optgroup label="&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $category_child->getName() ?>">
          <?php if(isset($category_child->children)): ?>
            <?php foreach($category_child->children as $category_lvl_3): ?>
              <option value="<?php echo $category_lvl_3->id ?>"
                <?php writeIfSelectedVal($module_session['filters']['category'], $category_lvl_3->id); ?>>
                &nbsp;&nbsp;&nbsp;&nbsp;<?php echo $category_lvl_3->getName(); ?>
              </option>
            <?php endforeach; ?>
          <?php endif; ?>
        </optgroup>
        <?php endforeach; ?>
        <?php endif; ?>
      </optgroup>
    <?php endforeach; ?>
  </select>
<?php echo $list_filter->renderSelect('supplier'); ?>
<?php echo $list_filter->renderSubmit(); ?>
<?php echo $list_filter->renderReset(); ?>
<?php echo $list_filter->renderFormClose(); ?>

<?php $pager->writePreviousNext(); ?>

<?php if(count($products)==0) : ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else : ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>
        <?php echo __('Code');?>
        <a href="<?php echo reconstructQuery() ?>sort=code&order=ASC" class="order "><?php echo IconHelper::ICON_ARROW_UP ?></a>
        <a href="<?php echo reconstructQuery() ?>sort=code&order=DESC" class="order "><?php echo IconHelper::ICON_ARROW_DOWN ?></a>
      </td>
      <td>
        <?php echo __('Naam')?>
        <a href="<?php echo reconstructQuery() ?>sort=name&order=ASC" class="order "><?php echo IconHelper::ICON_ARROW_UP ?></a>
        <a href="<?php echo reconstructQuery() ?>sort=name&order=DESC" class="order "><?php echo IconHelper::ICON_ARROW_DOWN ?></a>
      </td>
      <td><?php echo __('Online')?></td>
      <td class="gsd-svg-icon-width-2"><?php echo __('Acties')?></td>
    </tr>
    <?php foreach($products as $prod) : ?>
      <tr class="dataTableRow"  id="product_<?php echo $prod->id ?>">
        <td>
          <a
            href="<?php echo reconstructQueryAdd(['pageId']) ?>action=productedit&id=<?php echo $prod->id ?>"
            title="<?php echo __('Bewerk product'); ?>">
            <?php echo $prod->code; ?>
          </a>
        </td>
        <td><?php echo $prod->getName(); ?></td>
        <?php if(Config::get('CATALOG_LIST_SUPPLIER_SHOW', true)) : ?>
          <td><?php echo $prod->supplier_code; ?></td>
        <?php endif; ?>
        <?php if(defined('CATALOG_BRAND') && CATALOG_BRAND) : ?>
          <td><?php if(isset($brands[$prod->brand_id])) echo $brands[$prod->brand_id]->getName(); ?></td>
        <?php endif; ?>
        <?php if(defined('STOCK_ENABLED') && STOCK_ENABLED):?>
          <td><?php echo $prod->stock;?></td>
        <?php endif;?>
        <td>
          <a href="#" class="toggleactive qtipa" title="<?php echo __('Zet product (in)actief in deze categorie');?>">
            <?php echo $prod->online_custshop==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
          </a>
          <input type="hidden" class="type" value="product" />
          <input type="hidden" class="value" value="<?php echo $prod->online_custshop?0:1; ?>" />
        </td>
        <td>
          <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=productedit&id=' . $prod->id) ?>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=productdelete&id=' . $prod->id, __('Weet u zeker dat u dit product wilt verwijderen?')."\n".$prod->content->name) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>

<script type="text/javascript">
  $(document).ready( function() {

    $("#article_search").focus();

    $(".fullpath").click( function(event) {
      event.preventDefault();
    });

    $(".toggleactive").click( function(event) {
      event.preventDefault();
      var current = $(this);
      var parent = current.parent();

      var id = current.parents("tr").attr('id').split('_')[1];
      var type = parent.find(".type").val();
      var value = parent.find(".value");

      var url = "<?php echo reconstructQueryAdd(['pageId']); ?>action=toggleactive" + type + "&id=" + id  + "&value="+value.val();

      $.getJSON(url).done( function(data) {
        var msg = "<?php echo __('Wijzigingen NIET opgeslagen!');?>";
        var color = "red";
        if(data && data.result == "OK") {
          msg = "<?php echo __('Wijzigingen succesvol opgeslagen');?>";
          color = "";

          //set new data
          value.val((value.val()==1?0:1));
          current.html(value.val()==1?'<?php echo IconHelper::getCheckboxOff() ?>':'<?php echo IconHelper::getCheckbox() ?>');
        }
        $('#message').html('<div class="message"' + (color!=""?'style="color: ' + color + ';"':'') + '>' + msg + '</div><br/>');
      });
    });

  });
</script>

