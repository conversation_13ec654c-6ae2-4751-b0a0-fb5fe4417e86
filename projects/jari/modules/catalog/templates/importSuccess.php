<?php
  /** @var array $errors */
  /** @var array $exportDateOptions */
  /** @var string $exportDateFromDefault */
  /** @var string $exportDateToDefault */
?>

<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem($current_action->pageId)->getName() ?>
  </h1>

  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>


<?php writeErrors($errors) ?>

<form method="post" enctype="multipart/form-data">
  <section class="module-description">
    <h2>Sales exporteren</h2>
    <p>Exporteer producten met verkoopinformatie van de geselecteerde periode.</p>
    <label for="export_sales_from">Van</label> <select id="export_sales_from" name="export_sales_from">
      <?php
        foreach ($exportDateOptions as $value => $option) {
          $selected = ($value == $exportDateFromDefault) ? "selected" : '';
          echo "<option value=\"$value\" $selected>$option</option>";
        }
      ?>
    </select>
    <label for="export_sales_to">t/m</label> <select id="export_sales_to" name="export_sales_to">
      <?php
        foreach ($exportDateOptions as $value => $option) {
          $selected = ($value == $exportDateToDefault) ? "selected" : '';
          echo "<option value=\"$value\" $selected>$option</option>";
        }
      ?>
    </select>
    <br/>
    <input type="submit" name="export_sales" value="Exporteer sales naar Excel" class="gsd-btn gsd-btn-primary" style="margin-top: 15px"/>
  </section>

  <section class="module-description">
    <h2>Producten exporteren naar Excel</h2>
    <input type="submit" name="export" value="Exporteer producten naar Excel" class="gsd-btn gsd-btn-primary"/>
  </section>

  <section class="module-description">
    <h2>Producten importeren vanuit Excel</h2>
    <p>Importeer producten vanuit de aangepaste productexport. De eerste rij bevat de kolomnamen en wordt overgeslagen.</p>

    <div style="display: flex; margin-top: 8px; margin-bottom: 12px;">
      <div>
        <p><b>Wat wordt er wel gedaan:</b></p>
        <ul>
          <li>Alleen de <span class="column-to-update">oranje kolommen</span> worden gebruikt als een product wordt bijgewerkt</li>
          <li>Nieuwe producten worden toegevoegd</li>
          <li>Nieuwe productcontainers worden toegevoegd (als ze niet bestaan)</li>
        </ul>
      </div>
      <div style="margin-left: 10px;">
        <p><b>Wat wordt er niet gedaan:</b></p>
        <ul>
          <li>Producten worden niet verhuisd naar een andere categorie</li>
          <li>Er worden geen nieuwe categorieën aangemaakt</li>
          <li>Er worden geen producten verwijderd</li>
          <li>Volgorde wordt niet gewijzigd van bestaande/nieuwe producten</li>
        </ul>
      </div>
    </div>

    <table class="csv-example-table">
      <tbody>
      <tr>
        <th></th>
        <th>A</th>
        <th>B</th>
        <th>C</th>
        <th>D</th>
        <th>E</th>
        <th>F</th>
        <th>G</th>
        <th>H</th>
        <th>I</th>
        <th>J</th>
        <th>K</th>
        <th>L</th>
        <th>M</th>
        <th>N</th>
      </tr>
      <tr>
        <th>Kolomnaam</th>
        <td>Hoofdcategorie</td>
        <td>Subcategorie</td>
        <td>SEO categorie</td>
        <td class="column-to-update">Productnaam</td>
        <td class="column-to-update">Maatvoering</td>
        <td class="column-to-update">Kwaliteit</td>
        <td class="column-to-update">Dinn nr</td>
        <td class="column-to-update">Stuksverpakking</td>
        <td>Prijs per verpakking</td>
        <td>Prijs per 100</td>
        <td>Staffel prijzen</td>
        <td>Bedrijvenkorting (%)</td>
        <td class="column-to-update">Gewicht</td>
        <td class="column-to-update">Alternatieve naam</td>
      </tr>
      </tbody>
    </table>
    <br>

    <table class="csv-example-table">
      <tbody>
      <tr>
        <th></th>
        <th>O</th>
        <th>P</th>
        <th>Q</th>
        <th>R</th>
        <th>S</th>
        <th>T</th>
        <th>U</th>
        <th>V</th>
        <th>W</th>
        <th>X</th>
        <th>Y</th>
        <th>Z</th>
        <th>AA</th>
        <th>AB</th>
        <th>AC</th>
      </tr>
      <tr>
        <th>Kolomnaam</th>
        <td class="column-to-update">Sleutelwoorden</td>
        <td class="column-to-update">SEO titel (google)</td>
        <td>Techwinkel artikelnr</td>
        <td class="column-to-update">Leverancier artikelnr</td>
        <td class="column-to-update">EAN code</td>
        <td class="column-to-update">Leverancier naam</td>
        <td class="column-to-update">Inkoop prijs</td>
        <td class="column-to-update">Inkoop prijs eenheid</td>
        <td class="column-to-update">Marge factor</td>
        <td>Huidige voorraad</td>
        <td class="column-to-update">Minimale voorraad</td>
        <td class="column-to-update">Maximale voorraad</td>
        <td class="column-to-update">Inkoop bestel aantal</td>
        <td class="column-to-update">Minimale afname</td>
        <td>Online</td>
      </tr>
      </tbody>
    </table>
    <br>

    <?php echo $product_range_uploader->getInputs(); ?><br/>
    <br/>
    <input type="submit" name="import_products" value="Excelbestand uploaden (exportformaat)" class="gsd-btn gsd-btn-primary"/>
  </section>

  <section class="module-description">
    <h2>Prijzen updaten middels een Excelbestand van een leverancier</h2>
    <p>Upload een leveranciersbestand met nieuwe prijzen. Zorg ervoor dat:</p>
    <ul>
      <li>De 1ste rij de kolomnamen bevat</li>
      <li>Er een kolom is met een artikelnummer</li>
    </ul>
    <br/>
    <?php echo $product_updates_uploader->getInputs(); ?><br/>
    <br/>
    <input type="submit" name="import_supplier_file" value="Excelbestand uploaden" class="gsd-btn gsd-btn-primary"/>
  </section>


</form>

<script type="text/javascript">
  $(document).ready(function () {
  });
</script>

<style>
  table.csv-example-table,
  .csv-example-table th,
  .csv-example-table td {
    border: 1px solid grey;
  }

  .csv-example-table td {
    padding: 2px 3px;
  }

  .column-to-update {
    color: orange;
    font-weight: bold;
  }

  section.module-description {
    margin-bottom: 40px;
  }
  section.module-description select {
    margin: 0 7px;
  }
</style>