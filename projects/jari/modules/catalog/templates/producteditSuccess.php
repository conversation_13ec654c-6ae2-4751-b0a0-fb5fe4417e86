<section class="title-bar">
  <h1>
    <?php echo __("Product"); ?> <?php echo ($product->id != '') ? __('bewerken') : __('aanmaken'); ?>
  </h1>

  <?php
    TemplateHelper::includePartial('_tabs.php', 'catalog');
  ?>
</section>

<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
<script type="text/javascript">

  $(document).ready(function () {

    ckeditorInit();
    CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
    ckeditorScaytNl();

    <?php if(!Config::isdefined('PRODUCT_CKEDITOR_TOOLBAR')): ?>
    ckeditorSimple();
    <?php else:
    $_SESSION['filebrowserPath'] = DIR_UPLOADS . 'sites/1/ckfiles';
    ?>
    CKEDITOR.config['filebrowserBrowseUrl'] = '<?php echo Context::getSiteDomain() . URL_INCLUDES ?>ckeditor4/RichFilemanager/index.html';
    <?php endif; ?>

    $(".tabslink").click(function (event) {
      var id = 'content_' + $(this).attr("id").substring(5);
      $(".tabslink").parent().removeClass('active');
      //$(".tabslink").hover();
      $(this).parent().addClass('active');

      $(".tabscontent").hide();
      $("#tabscontent_active").val($(this).attr("id"));
      $('#' + id).show();
      event.preventDefault();
    });

    <?php if(isset($tabscontent_active) && $tabscontent_active != ""): ?>
    $("#<?php echo $tabscontent_active ?>").click();
    <?php else: ?>
    $(".tabslink:first").click();
    <?php endif; ?>

    new SimpleLightbox(".logoimage",{
      fileExt: false
    });

    $(".price").change(function () {
      if ($(this).val() != "") {
        if ($(this).hasClass("decimals-4")) {
          $(this).val(decimalNL($(this).val(), 4));
        } else {
          $(this).val(decimalNL($(this).val()));
        }
      }
    });

    $("#product_type").change(function () {
      if ($("#product_type").val() == 'combi') {
        $("#combi_tabslink").show();
        $("#price_buy").prop("readonly", true);
        $("#price_buy_remark").text("(<?php echo __('Inkoop prijs word berekend a.d.h.v. combi producten bij opslaan');?>");
      } else {
        $("#combi_tabslink").hide();
        $("#price_buy").prop("readonly", false);
        $("#price_buy_remark").text("");
      }

    });

    if ($("#product_type").length > 0 && $("#product_type").val() == 'combi') {
      $("#product_type").change();
    }

  });

</script>

<ul id="tabnav" class="nav nav-tabs">
  <li style="margin-left:5px;">
    <a href="" class="tabslink" id="link_general"><?php echo __('Algemeen'); ?></a>
  </li>
  <?php if(Config::isTrue('CATALOG_PRODUCT_RELATEDPRODUCTS') && $_SESSION['userObject']->organisation->type != Organisation::TYPE_SHOP): ?>
    <li>
      <a href="#" class="tabslink" id="link_related"><?php echo __('Gerelateerde'); ?></a>
    </li>
  <?php endif; ?>
</ul>
<form class="edit-form" method="post" name="prodedit" id="prodedit" enctype="multipart/form-data">
  <input type="hidden" name="addproduct" id="addproduct" value="" />
</form>
<form class="edit-form" method="post" name="productform" id="productform" enctype="multipart/form-data">
  <input type="hidden" value="<?php echo $product->getUpdateTS() ?>" name="updateTS" id="updateTS" />
  <input type="hidden" value="" name="tabscontent_active" id="tabscontent_active" />

  <?php writeErrors($errors, true); ?>

  <div id="content_general" class="tabscontent">
    <?php TemplateHelper::includePartial('_productgeneral.php', 'catalog', compact('product','categories','category','product_id','oblidged','product_container','product_ean_codes','productoptions')); ?>
  </div>

  <?php if(Config::isTrue('CATALOG_PRODUCT_RELATEDPRODUCTS') && $_SESSION['userObject']->organisation->type != Organisation::TYPE_SHOP): ?>
    <div id="content_related" class="tabscontent">
      <?php TemplateHelper::includePartial('_productrelated.php', 'catalog', [
        'product'         => $product,
        'productsrelated' => $productsrelated,
      ]) ?>
    </div>
  <?php endif; ?>

  <input type="submit" name="go" id="go" class="submit_form gsd-btn gsd-btn-primary" value="<?php echo __('Opslaan'); ?>" title="<?php echo __('Sla uw wijzigingen op'); ?>" />
  <?php if(Privilege::hasRight('M_PRODUCTCONT')): ?>
    <input type="submit" name="go_container" id="go_container" class="submit_form gsd-btn gsd-btn-primary"
           value="<?php echo __('Opslaan en naar product container'); ?>"
           title="<?php echo __('Sla uw wijzigingen op'); ?>" />
  <?php endif; ?>
  <input type="submit" name="go_list" class="submit_form gsd-btn gsd-btn-primary" value="<?php echo __('Opslaan en naar lijst'); ?>"
         title="<?php echo __('Sla uw wijzigingen op en ga terug naar lijst'); ?>" />
  <input type="submit" name="cancel" value="<?php echo __('Annuleren'); ?>"
         title="<?php echo __('Sla wijzigen niet op'); ?>" class="gsd-btn gsd-btn-secondary" />
</form>
