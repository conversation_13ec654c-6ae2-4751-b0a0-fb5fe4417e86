<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td colspan="2">Voorraad</td>
  </tr>
  <?php if (Config::isTrue("STOCK_ACTIVE")) : ?>
    <tr class="dataTableRow">
      <td class="head"><label for="stock_active">Voorraad bijhouden</label></td>
      <td>
        <input type="checkbox" name="stock_active" id="stock_active" value="1" <?php writeIfCheckedVal($product->stock_active, 1); ?> />
      </td>
    </tr>
  <?php endif; ?>
  <?php if (defined('STOCK_ENABLED') && STOCK_ENABLED): ?>
    <tr class="dataTableRow">
      <td class="head">Huidige voorraad</td>
      <td>
        <input type="text" name="stock" id="stock" value="<?php echo $product->stock ?>"
               class="<?php echo isset($errors['stock']) ? 'inputerror' : ''; ?>" style="width: 90px;text-align:right;"
               <?php if (Config::get('STOCK_LOCATION_MULTI', true)): ?>disabled<?php endif; ?>/> <span
          class='asterisk'>*</span>
        <?php if (Config::get('STOCK_LOCATION_MULTI', true)): ?>
          <?php echo showHelpButton("Aan te passen in voorraad. Producten kunnen in meerdere vakken liggen, dus de voorraad van een product is gekoppeld aan een magazijnvak.") ?>
        <?php endif; ?>
      </td>
    </tr>
    <?php if ($product_has_combined_stock): ?>
      <tr class="dataTableRow">
        <td class="head">Gecombineerde voorraad</td>
        <td>
          <?php echo $combined_stock ?>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td class="head"></td>
        <td>
          Let op: dit product heeft een gecombineerde voorraad. De minimale voorraad, maximale voorraad en bestelaantal inkoop verwerk je bij de gecombineerde voorraad.
          <br>
          <a href="<?php echo PageMap::getUrl('M_COMBINED_STOCK') ?>?action=edit&id=<?php echo $combined_stock_product->combined_stock_id ?>">Ga naar gecombineerde voorraad</a>
        </td>
      </tr>
    <?php else: ?>
      <?php if (Config::get('STOCK_LEVEL', true)) : ?>
        <tr class="dataTableRow">
          <td class="head">Minimale voorraad</td>
          <td>
            <input type="text" name="stock_level" id="stock_level" value="<?php echo $product->stock_level ?>"
                   class="<?php echo isset($errors['stock_level']) ? 'inputerror' : ''; ?>"
                   style="width: 90px;text-align:right;"/>
            <?php echo showHelpButton("Het minimale aantal producten dat we op voorraad willen hebben van dit product. Dit is een indicatie getal voor bij het bestellen.") ?>
          </td>
        </tr>
        <tr class="dataTableRow">
          <td class="head">Maximale voorraad</td>
          <td>
            <input type="text" name="stock_level_max" id="stock_level_max"
                   value="<?php echo $product->stock_level_max ?>"
                   class="<?php echo isset($errors['stock_level_max']) ? 'inputerror' : ''; ?>"
                   style="width: 90px;text-align:right;"/>
            <?php echo showHelpButton("Het maximale aantal producten dat we op voorraad willen hebben van dit product. Dit is een indicatie getal voor bij het bestellen.") ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php if (Config::get('STOCK_PACKINGSIZE', true)) : ?>
        <tr class="dataTableRow">
          <td class="head">Bestelaantal inkoop</td>
          <td>
            <input type="text" name="packing_size" value="<?php echo $product->packing_size ?>"
                   class="<?php echo isset($errors['packing_size']) ? 'inputerror' : ''; ?>"
                   style="width: 90px;text-align:right;"/> <span class='asterisk'>*</span>
            <?php echo showHelpButton("Standaard wordt de verkoop verpakkingsaantal gebruikt bij de inkoop van producten. Als je een andere hoeveelheid wilt bestellen bij de leverancier, vul dat dan hier in. Als het product dan onder de minimale voorraad komt, dan bestel hij deze vaste aantallen.") ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php if (Config::get('STOCK_NOT_IN_BACKORDER', true)) : ?>
        <tr class="dataTableRow">
          <td class="head"><label for="not_in_backorder">Niet in backorder</label></td>
          <td><input type="checkbox" name="not_in_backorder" id="not_in_backorder"
                     value="1" <?php writeIfCheckedVal($product->not_in_backorder, 1); ?> /> <?php echo showHelpButton("Wanneer dit vinkje aanstaat kan men dit product niet bestellen bij onvoldoende voorraad.", "Niet in backorder"); ?>
          </td>
        </tr>
      <?php endif; ?>
    <?php endif; ?>
  <?php endif; ?>
  <?php if (Config::get('STOCK_LOCATION', true) && Config::get('STOCK_LOCATION_MULTI') == false): ?>
    <tr class="dataTableRow">
      <td class="head">Locatie</td>
      <td>
        <select name="warehouse_box_id">
          <option value="">Magazijn...</option>
          <?php
            $warehouseproduct = false;
            if (isset($_POST['warehouse_box_id'])) {
              $warehouseproduct = WarehouseProduct::find_by(['warehouse_box_id' => $_POST['warehouse_box_id']]);
            }
            else {
              $warehouseproduct = WarehouseProduct::find_by(['product_id' => $product->id]);
            }
          ?>
          <?php foreach (Warehouse::getWarehousesAndBoxes() as $wh): ?>
            <?php foreach ($wh->boxes as $box): ?>
              <option value="<?php echo $box->id ?>" <?php if ($warehouseproduct && $warehouseproduct->warehouse_box_id == $box->id) echo 'selected' ?>><?php echo $wh->name ?> -
                vak <?php echo $box->boxname ?></option>
            <?php endforeach; ?>
          <?php endforeach; ?>
        </select>
        <?php if (Privilege::hasRight('M_WAREHOUSE')): ?>
          of maakt direct een nieuw vak
          <input type="text" value="" name="warehouse_box_new" style="width: 100px;" placeholder="Nieuw vak..." autocomplete="off"/>
        <?php endif; ?>
      </td>
    </tr>
  <?php endif; ?>
  <?php if (Config::get('STOCK_DYMO_LABELPRINTER', true)): ?>

    <script src="http://www.labelwriter.com/software/dls/sdk/js/DYMO.Label.Framework.3.0.js" type="text/javascript" charset="UTF-8"></script>
    <script>

      var barcodeLabel;

      function loadPrinters() {
        var printers = dymo.label.framework.getLabelWriterPrinters();
        if (printers.length == 0) {
          alert("No DYMO printers are installed. Install DYMO printers.");
          return;
        }

        for (var i = 0; i < printers.length; i++) {
          var printer = printers[i];

          var printerName = printer.name;

          var option = document.createElement('option');
          option.value = printerName;
          option.appendChild(document.createTextNode(printerName));
          printersSelect.appendChild(option);
        }
      }

      $(document).ready(function () {
        loadPrinters();

        $("#print").click(function (event) {
          event.preventDefault();
          try {
            if (!printersSelect.value) {
              alert("Selecteer eerst de DYMO printer.");
              return;
            }

            $.get('?action=dymolabel&id=<?php echo $product->id ?>', function (xml) {

              //console.log(xml);
              barcodeLabel = dymo.label.framework.openLabelXml(xml);
              // barcodeLabel.setObjectText('Barcode', 'http://developers.dymo.com');
              barcodeLabel.print(printersSelect.value);

            });
          } catch (e) {
            alert(e.message || e);
          }
        });
      });

    </script>

    <tr class="dataTableRow">
      <td class="head">Print Dymo Label</td>
      <td>
        Selecteer printer: <select id="printersSelect"></select> <a id="print" class="gsd-btn">Print label</a>
      </td>
    </tr>
  <?php endif; ?>

</table>
<script type="text/javascript">
  $(document).ready(function () {
    $("#stock,#stock_level_max,#stock_level, #not_in_backorder").change(function (event) {
      var val = 0;
      if ($(this).val() != "") {
        var val = parseInt($(this).val());
        if (isNaN(val)) {
          val = 0;
        }
      }
      $(this).val(val);
    });
  });
</script>