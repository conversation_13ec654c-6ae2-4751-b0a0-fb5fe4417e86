<?php

  use domain\productrange\service\GetProductContainers;
  use gsdfw\domain\mail\service\WrapperService;

  class catalogJariActions extends catalogActions {

    public function preExecute() {
      parent::preExecute();

      if (!isset($_SESSION[$this->pageId])) $_SESSION[$this->pageId] = [];
      // create a reference to the module session value, so we can easily use it in the controller/template
      $this->module_session = &$_SESSION[$this->pageId];
    }

    public function executeList() {
      $this->depth = 0;
      if (isset($_GET['catid'])) {
        $category = Category::find_by(['id' => $_GET['catid'], 'void' => 0]);
        if ($category) {
          $this->category = $category;
          $this->depth = $this->category->getDepth();

          $this->categories = $this->getCategories($this->category->id, $_SESSION['worklanguage']);

          // on category level 2, also get all the product containers from the sub categories
          // this way they can be sorted on this level
          $category_ids = [$this->category->id];
          if ($this->depth == 2) {
            // add subcategory ids to array, because we also want the product containers from them
            foreach ($this->categories as $_sub_category) {
              $category_ids[] = $_sub_category->id;
            }
          }

          //artikelen ophalen
          $get_product_containers = new GetProductContainers();
          $this->products = $get_product_containers->getCategoryList($category_ids);

          $parents = Category::getParents($this->category);
          if ($parents != null) {
            foreach ($parents as $parent) {
              BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['worklanguage']), PageMap::getUrl('M_CATALOG_OV') . '?catid=' . $parent->id);
            }
          }
        }
      }
      else {
        // zoeken in root
        // artikelen ophalen
        $get_product_containers = new GetProductContainers();
        $this->products = $get_product_containers->getCategoryList(null);

        $this->categories = $this->getCategories('', $_SESSION['worklanguage']);
      }

      $this->catid = "";
      if (isset($this->category)) {
        $this->catid = $this->category->id;
      }

      $may_add_cat = false;
      if (CONFIGURE_MAX_DEPTH_NESTED_CATS != 0 && $this->depth < CONFIGURE_MAX_DEPTH_NESTED_CATS) {
        $may_add_cat = true;
      }

      $may_add_prod = false;
      if ((isset($this->category) || CONFIGURE_ADDPRODUCTS_IN_ROOTDIR)) {
        $may_add_prod = true;
      }

      $this->may_add_cat = $may_add_cat;
      $this->may_add_prod = $may_add_prod;
      if (isset($this->category)) {
        $this->seo_title = $this->category->getSeoTitle($_SESSION['worklanguage']);
        $this->seo_description = $this->category->getSeoDesc($_SESSION['worklanguage']);
      }
      else {
        $this->seo_title .= "Categorie en product lijst";
      }
      if (defined('CATALOG_BRAND') && CATALOG_BRAND) {
        $this->brands = Brand::getBrandsAr();
      }
    }

    public function executeProductlist() {

      $this->setProductListFilters();

      $this->categories = Category::getCategoryTree();

      $sort_by = $_GET['sort'] ?? 'product.code, product_content.name';
      $order_by = $_GET['order'] ?? 'ASC';

      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 50;
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      $filt = "";
      $filt .= "JOIN product_content ON product_content.product_id = product.id AND locale='" . $_SESSION['lang'] . "' ";
      $filt .= "LEFT JOIN product_option ON product_option.product_id = product.id ";
      if (!empty($this->module_session['filters']['category'])) {
        $filt .= "JOIN product_cont ON product_cont.id = product.product_cont_id ";
      }
      $filt .= "WHERE product.void = 0 ";

      if (!empty($this->module_session['filters']['category'])) {
        $filt .= "AND product_cont.category1_id = " . DbHelper::escape($this->module_session['filters']['category']) . " ";
      }
      if (!empty($this->module_session['filters']['supplier'])) {
        $filt .= "AND product.supplier_id = " . DbHelper::escape($this->module_session['filters']['supplier']) . " ";
      }

      if (!empty($this->module_session['filters']['search'])) {
        $searchval = escapeForDB($this->module_session['filters']['search']);

        $filt .= "AND (";
        $filt .= "product_content.name LIKE '%" . $searchval . "%' OR ";
        $filt .= "product_content.description LIKE '%" . $searchval . "%' OR ";
        $filt .= "product.code LIKE '%" . $searchval . "%' OR ";
        $filt .= "product.supplier_code LIKE '%" . $searchval . "%'";
        $filt .= ") ";
      }

      $products = [];
      $this->pager->count = Product::count_all_by([], $filt);

      $filt .= "ORDER BY " . DbHelper::escape($sort_by) . " " . DbHelper::escape($order_by) . " ";
      $filt .= $this->pager->getLimitQuery();

      $query = "SELECT product.*, product_content.*, product_option.value as margin_factor FROM product ";
      $query .= $filt;

      $results = DBConn::db_link()->query($query);
      while ($row = $results->fetch_array()) {
        $row_counter = 0;
        $product = new Product();
        $product->hydrate($row, $row_counter);
        $row_counter += count(Product::columns);

        $productc = new ProductContent();
        $productc->hydrate($row, $row_counter);
        $row_counter += count(ProductContent::columns);

        $product->margin_factor = $row[$row_counter];

        $product->content = $productc;

        $products[] = $product;
      }

      $this->products = $products;
      $this->seo_title .= "product lijst";
    }

    private function setProductListFilters() {
      // prepare session value
      if (!isset($this->module_session['filters'])) $this->module_session['filters'] = [];
      // set filter object
      $this->list_filter = new ListFilter();

      $this->list_filter->addSearch('search');

      $categories = ['' => 'Filter op categorie'];
      $this->list_filter->addSelect('category', $categories);

      // this will handle the post request and set default values for the filter
      $this->module_session['filters'] = $this->list_filter->handleRequest($this->module_session['filters'], $_POST);
    }

    public function executeProductedit() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }
      $errors = [];
      if (!isset($this->oblidged)) {
        $this->oblidged = [
          'description_nl',
          'code',
          'price_part',
          'supplier',
        ];
      }
      $product = null;
      $product_images = [];
      $productOptions = [];
      $staffel = [];
      $this->product_id = null;
      $catprod = null;
      $productsrelated = [];
      $products_linked = [];
      $productscombi = [];
      $product_categories = [];

      /** COPY a product */
      if (isset($_GET['copyid']) && $_GET['copyid'] != "" && !isset($_POST['name'])) {
        $product = Product::find_by_id($_GET['copyid']);
        if ($product) {
          $this->product_id = null;
        }
      }

      $category = null;
      if (isset($_POST['category_id'])) {
        $category = Category::find_by(['id' => $_POST['category_id'], 'void' => 0]);
      }
      elseif (isset($_GET['catid'])) {
        $category = Category::find_by(['id' => $_GET['catid'], 'void' => 0]);
      }

      if (isset($_GET['id']) || (isset($_POST['id']) && $_POST['id'] != "")) {
        $id = $_GET['id'] ?? $_POST['id'];
        $product = Product::find_by_id($id);
        if (!$product) {
          ResponseHelper::redirectError("U probeert een product te openen welke (reeds) is verwijderd of u heeft onvoldoende rechten");
        }
      }

      /** EDITING A PRODUCT */
      if ($product) {

        foreach (Config::get("catalog_languages") as $llang) {
          $cc = ProductContent::getByProductIdAndLang($product->id, $llang);
          if ($cc) {
            $product->contents[$llang] = $cc;
          }
          else { //deze taal is nog niet gezet? aanmaken dan.
            $cc = new ProductContent();
            $cc->locale = $llang;
            $product->contents[$llang] = $cc;
          }
        }

        foreach (ProductOption::CODES as $code => $label) {
          $option = ProductOption::getOption($product->id, $code);
          if (!$option) {
            $option = new ProductOption([
              'product_id' => $product->id,
              'code' => $code,
            ]);
          }
          $productOptions[$code] = $option;
        }
        $product->product_options = $productOptions;

        if (!isset($_GET['catid'])) {
          $catprod = CategoryProduct::find_by(['product_id' => $product->id]);
          if ($catprod) {
            $redirect_url = reconstructQueryAdd(['pageId']) . 'catid=' . $catprod->category_id . '&action=productedit&id=' . $product->id;
            if (!empty($this->tabscontent_active)) {
              $redirect_url .= '&tabscontent_active=' . $this->tabscontent_active;
            }
            ResponseHelper::redirect($redirect_url);
          }
        }
        else {
          $catprod = CategoryProduct::getCategoryProduct($product->id, $_GET['catid']);
        }
        if (Config::isTrue('CATALOG_PRODUCT_RELATEDPRODUCTS')) {
          $productsrelated = ProductRelated::getProducts($product->id);
        }
      }
      /** NEW PRODUCT */
      else {

        if (empty($_GET['product_container_id'])) {
          $_SESSION['flash_message_red'] = __('Een product kan alleen aangemaakt worden vanuit een product container.');
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        //default values;
        $product = new Product();
        $product->void = 0;
        $product->vatgroup = 2;
        $product->shipping_cat = Config::isdefined('PRODUCT_DEFAULT_SHIPPINGCAT') ? Config::get('PRODUCT_DEFAULT_SHIPPINGCAT') : 'B';
        $product->online_custshop = 1;
        $product->stock = 0;
        $product->stock_level = 0;
        $product->packing_size = 1;

        foreach (Config::get("catalog_languages") as $llang) {
          $cc = new ProductContent();
          $cc->locale = $llang;
          $product->contents[$llang] = $cc;
        }

        foreach (ProductOption::CODES as $code => $label) {
          $productOptions[$code] = new ProductOption([
            'product_id' => $product->id,
            'code' => $code,
          ]);
        }
        $product->product_options = $productOptions;

        //als catid is gezet, selecteer dan deze cat vast
        if (isset($_GET['catid'])) {
          $product_categories[$_GET['catid']] = $_GET['catid'];
        }

        $product->product_cont_id = (int)$_GET['product_container_id'];
      }

      $product_container = ProductCont::find_by_id($product->product_cont_id);
      if (!$product_container) {
        $_SESSION['flash_message_red'] = __('Product container niet gevonden.');
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $product_ean_codes = AppModel::mapObjectIds(ProductCode::find_all_by(['product_id' => $product->id]), 'code');

      $this->tabscontent_active = $_POST['tabscontent_active'] ?? ($_GET['tabscontent_active'] ?? "");

      if (isset($_POST['name'])) {
        if (isset($_POST['code'])) {
          $product->code = trim($_POST['code']);
          if ($product->code == "" && $this->isOblidged('code')) {
            $errors['code'] = "Productcode is verplicht";
          }
        }

        if (Config::get('PRODUCT_USE_STAFFEL', true)) {
          if (trim($_POST['staffel']) == "") {
            $product->staffel = null;
          }
          else {
            $product->setStaffel(Product::getStaffelArray($_POST['staffel']));
          }
        }
        if (Config::get('PRODUCT_SHIPPING_CAT_ENABLED', true)) {
          if ($_POST['shipping_cat'] != '') {
            $product->shipping_cat = $_POST['shipping_cat'];
          }
          else {
            $errors[] = 'Selecteer een verzend type';
          }
        }
        foreach (Config::get("catalog_languages") as $llang) {
          $product->contents[$llang]->name = trim($_POST['name'][$llang]);
          if (isset($_POST['description'][$llang])) {
            $product->contents[$llang]->description = trim($_POST['description'][$llang]);
          }
          if (isset($_POST['seo_title'][$llang])) {
            $product->contents[$llang]->seo_title = substr(trim($_POST['seo_title'][$llang]), 0, 255);
          }
          if (isset($_POST['seo_desc'][$llang])) {
            $product->contents[$llang]->seo_desc = substr(trim($_POST['seo_desc'][$llang]), 0, 255);
          }
          if (isset($_POST['url'][$llang])) {
            $product->contents[$llang]->url = trim($_POST['url'][$llang]);
          }
          if (isset($_POST['keywords'][$llang])) {
            $labeltjes = [];
            foreach ($_POST["keywords"][$llang] as $k => $label) {
              $label = strtolower(trim($label));
              if ($label != "") {
                $labeltjes[$label] = $label;
              }
            }
            $product->contents[$llang]->keywords = implode(",", $labeltjes);
          }
        }

        if (Config::isTrue("PRODUCT_DESCRIPTION_EXPORT")) {
          $product->description_export = trim($_POST['description_export']);
        }

        if (defined('TAGS_PRODUCT') && TAGS_PRODUCT) {
          $product->tag1 = cleanPostVar('tag1');
          $product->tag2 = cleanPostVar('tag2');
          $product->tag3 = cleanPostVar('tag3');
          $product->tag4 = cleanPostVar('tag4');
          $product->tag5 = cleanPostVar('tag5');
        }

        if (isset($_POST['youtube_url'])) {
          $product->youtube_vid = Product::getVidFromUrl($_POST['youtube_url']);
        }
        $product->internal_remark = cleanPostVar('internal_remark');
        $product->online_custshop = isset($_POST['online_custshop']) ? 1 : 0;
        if (Config::get("PRODUCT_USE_ONLINE_UC", true)) {
          $product->online_uc = isset($_POST['online_uc']) ? 1 : 0;
        }
        if (Config::get("PRODUCT_USE_ONLINE_ADMIN", true)) {
          $product->online_admin = isset($_POST['online_admin']) ? 1 : 0;
        }

        $product->price_on_request = (isset($_POST['price_on_request']) || Config::get('CONFIGURE_ADD_PRODUCT_WITHOUT_PRICE', true)) ? 1 : 0;

        if (Config::get('PRODUCT_WEIGHT_ENABLED', true)) {
          $product->weight = StringHelper::isInt($_POST['weight']) ? $_POST['weight'] : 0;
        }

        $product->vatgroup = 2; //19% of 21%
        $product->discount = isset($_POST['discount']) ? 1 : 0;
        $product->void = 0;
        if (isset($_POST['size'])) $product->size = trim($_POST['size']);


        //eerste taal is verplicht
        foreach (Config::get("catalog_languages") as $llang) {
          if ($product->contents[$llang]->name == "") {
            $errors[escapeIdForJQ("name_" . $llang)] = "Naam " . strtoupper($llang);
          }
          if (count($errors) == 0) {
            if ($product->contents[$llang]->url == "" || (!Config::isdefined("PRODUCT_CUSTOM_URL") || !Config::get("PRODUCT_CUSTOM_URL"))) {
              //url leeg, of geen custom, dan altijd genereren
              $product->contents[$llang]->buildDefaultUrl();
            }
            if (Config::get("PRODUCT_CUSTOM_URL", true)) {
              $errors = PageContent::validateUrlGeneric($product->contents[$llang]->url, $product->contents[$llang]->locale, count(Config::get("catalog_languages")) > 1);
            }
          }
          if (count($errors) == 0) { //er zijn geen errors (er is een product titel, dus we kunnen een unieke url bouwen)
            if (!$product->contents[$llang]->buildUrlUnique()) {
              $errors["url"] = "Uw url is niet uniek binnen deze site. Probeer een andere. (teveel pogingen)";
            }
          }
          break;
        }

        if ($this->isOblidged('description') && $product->contents['nl']->description == "") {
          $errors['description'] = "Omschrijving NL";
        }
        if (Config::get("PRODUCT_DISCOUNTGROUP", true) && $product->discountgroup_id == "") {
          $errors['discountgroup_id'] = "Kortingsgroep";
        }

        if (!empty($product->price_dist) && $product->price_dist > 0) {
          // prijs per verpakking bepalen op basis van prijs per 100
          $packaging_price = ($product->amount_in_package / 100) * $product->price_dist;
          $product->price_part = $packaging_price; // prijs per verpakking
        }

        if (defined('STOCK_ENABLED') && STOCK_ENABLED) {
          if (!Config::get('STOCK_ALERT_DISABLED', true)) {
            if ($product->stock_level == "") {
              $errors['stock_level'] = "Minmale voorraad";
            }
          }
        }
        if (defined('CALCULATE_SHIPPING_CAT') && CALCULATE_SHIPPING_CAT && $product->shipping_cat == "") {
          $errors['shipping_cat'] = "Verzendkosten categorie";
        }
        if (defined('CATALOG_BRAND') && CATALOG_BRAND && Config::get('CATALOG_BRAND_OBLIDGED')) {
          if ($product->brand_id == "") {
            $errors['brand_id'] = "Merk";
          }
        }
        if (((Config::isTrue("PRODUCT_RESELLING")) || $_SESSION['userObject']->organisation->type == Organisation::TYPE_SHOP) && Config::isdefined('PRODUCT_RESELLING_MIN_MARGIN')) {
          //Als % reseller_marge < PRODUCT_RESELLING_MIN_MARGIN uncheck online_export
          if ($product->online_export == 1 || $_SESSION['userObject']->organisation->type == Organisation::TYPE_SHOP) {
            $bprice = $product->getBasketPricePart();
            $reselling_margin = 0;
            if ($bprice != 0) {
              $reselling_margin = $product->marge_reseller / ($bprice / 100);
            }
            $minmargin = Config::get('PRODUCT_RESELLING_MIN_MARGIN');
            if (round($reselling_margin, 2) < $minmargin && round($reselling_margin, 2) < 25) {
              if ($_SESSION['userObject']->organisation->type != Organisation::TYPE_SHOP) {
                $product->online_export = false;
                $_SESSION['flash_message_red'] = "Reseller product gedeactiveerd omdat minimale reseller marge minimaal " . $minmargin . '% (' . StringHelper::getPriceDot(($product->getBasketPricePart() / 100) * 10) . ' &euro; excl. BTW) moet zijn.';
              }
              else {
                $errors['reseller_price'] = "De minimale reseller marge minimaal " . $minmargin . '% (' . StringHelper::getPriceDot(($product->getBasketPricePart() / 100) * 10) . ' &euro; excl. BTW) moet zijn.';
              }
            }
            elseif ($product->price_reseller - $product->price_buy < 7.50) {
              $_SESSION['flash_message_red'] = "Let op: de product marge bij verkoop van dit prouduct bij reseller is kleiner dan 7.50 namelijk " . getLocalePrice($product->price_reseller - $product->price_buy);
            }
          }
        }

        if (count($errors) == 0) {

          if ($catprod == null) {
            //nieuwe product, dus ook nieuwe categoryproduct
            $catprod = new CategoryProduct();
            $catprod->void = 0;
          }

          $product->save();

          foreach (Config::get("catalog_languages") as $llang) {
            $product->contents[$llang]->product_id = $product->id;
            $product->contents[$llang]->url = str_replace("--ID--", $product->id, $product->contents[$llang]->url);
            $product->contents[$llang]->save();
          }

          foreach (ProductOption::CODES as $code => $label) {
            if (isset($_POST['options'][$code])) {
              if (empty($_POST['options'][$code]) && $product->product_options[$code]->id) {
                $product->product_options[$code]->destroy();
              }
              else {
                $product->product_options[$code]->value = $_POST['options'][$code];
                $product->product_options[$code]->save();
              }
            }
          }

          // when multiple categories, we save them later
          if (!defined('CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS') || CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS === false) {
            $catprod->product_id = $product->id;
            $newcatid = ($category == null ? null : $category->id);
            if ($catprod->category_id != $newcatid) { //oude category is niet gelijk aan nieuwe, hersorteren.
              $double = CategoryProduct::find_by([
                'product_id'  => $catprod->product_id,
                'category_id' => $newcatid,
              ]);
              if ($double) {
                $_SESSION['flash_message_red'] = 'U probeert dit product te verplaatsen naar een categorie waar dit product al bestaat. Dit is niet mogelijk.';
                ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
              }
              $catprod->sort = CategoryProduct::maxSort($newcatid) + 1;
            }
            if ($catprod->sort == null) { //altijd sortering onderaan meegeven
              $catprod->sort = CategoryProduct::maxSort($newcatid) + 1;
            }
            $catprod->category_id = $newcatid;
            if (!Privilege::hasRight('M_PRODUCTCONT')) {
              $catprod->save();
            }
          }

          $product->save();

          MessageFlashCoordinator::addMessage('Product opgeslagen');

          if (isset($_POST['go'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_PRODUCTLIST') . '?action=productedit&id=' . $product->id . '&tabscontent_active=' . $this->tabscontent_active);
          }
          elseif (isset($_POST['go_container'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_PRODUCTCONT') . '&action=edit&id=' . $product->product_cont_id . '&tabscontent_active=link_products');
          }
          else {
            //is de filter aan, dan naar root categorie
            $link = reconstructQueryAdd(['pageId']);
            if ($category && empty($_SESSION['a_search_brand']) && empty($_SESSION['a_search'])) {
              $link .= 'catid=' . $category->id;
            }
            ResponseHelper::redirect($link);
          }
        }
      }

      $this->errors = $errors;
      $this->product = $product;
      $this->product_images = $product_images;
      $this->staffel = $staffel;
      $this->catprod = $catprod;
      $this->category = $category;
      $this->productsrelated = $productsrelated;
      $this->products_linked = $products_linked;
      $this->productscombi = $productscombi;
      $this->productoptions = $productOptions;
      $this->product_categories = $product_categories;
      $this->product_container = $product_container;
      $this->product_ean_codes = $product_ean_codes;

      if (Config::get('CATALOG_PRODUCT_BTWGROUP', true)) {
        $this->vatgroups = Config::get('CATALOG_PRODUCT_BTWGROUP');
      }
      $this->categories = Category::flattenCategoryTree(Category::getTreeWithContent('', true));

      BreadCrumbs::getInstance()->removeLastItem();

      if ($category) {
        $parents = Category::getParents($category);
        if ($parents != null) {
          foreach ($parents as $parent) {
            BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['worklanguage']), PageMap::getUrl('M_CATALOG_OV') . '?catid=' . $parent->id);
          }
        }
      }

      BreadCrumbs::getInstance()->addItem((isset($product->contents[$_SESSION['worklanguage']]) && $product->contents[$_SESSION['worklanguage']]->name != "") ? $product->contents[$_SESSION['worklanguage']]->name : 'Nieuw product');
      $this->seo_title .= "bewerk product";
      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.full.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    public function executeMove() {
      $order = 0;
      $items = [];
      if (isset($_GET[$_GET['type']])) {
        $items = $_GET[$_GET['type']];
      }

      if (isset($_POST['values'])) {
        foreach (explode('&', $_POST['values']) as $i) {
          $vars = explode('=', $i);
          $items[] = $vars[1];
        }
      }

      foreach ($items as $id) {
        if ($id != "") {
          $arr = explode("_", $id);
          $id = $arr[1];
          $q = "";
          if ($_GET['type'] == 'cat') {
            $q = "UPDATE category SET sort=" . $order . ' WHERE id=' . DbHelper::escape($id);
          }
          elseif ($_GET['type'] == 'product') {
            $q = "UPDATE product SET sort=" . $order . ' WHERE id=' . DbHelper::escape($id);
          }
          elseif ($_GET['type'] == 'product_container') {
            $q = "UPDATE product_cont SET sort=" . $order . ' WHERE id =' . DbHelper::escape($id);
          }

          DBConn::db_link()->query($q);
          $order++;
        }
      }
      if (count($items) > 0) {
        if ($_GET['type'] == 'cat') {
          CategoryTreeFactory::clearEntireCache();
        }
      }
      $this->template = null; //do not load template
    }

    public function executeProductsearchgo() {
      $locale = $_SESSION['lang'];

      $searchval = escapeForDB(trim($_GET['val']));
      $filtquery = "WHERE product.void=0 ";
      if ($searchval != "") {
        $filtquery .= " AND (";
        $filtquery .= " product_content.name LIKE '%" . $searchval . "%'";
        $filtquery .= " OR product_content.description LIKE '%" . $searchval . "%'";
        $filtquery .= " OR product.code LIKE '%" . $searchval . "%'";
        $filtquery .= " OR product.supplier_code LIKE '%" . $searchval . "%'";
        $filtquery .= " )";
      }

      $filtquery .= " ORDER BY product_content.name ";
      $filtquery .= " LIMIT 100 ";

      $products = Product::getProductsAndContent($locale, $filtquery);
      echo json_encode($products);
      $this->template = null;
    }

    public function executeProductajax() {
      $query = "SELECT * FROM product ";
      $query .= Product::getFilterQuery("", $_GET['query']);
      $query .= " LIMIT 30";
      //         echo $query;

      $result = DBConn::db_link()->query($query);
      $products = [];
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);

        $productc = new ProductContent();
        $productc->hydrate($row, count(Product::columns));
        $product->content = $productc;

        $products[$product->id] = $product;
      }

      $values = [];
      $values['query'] = $_GET['query'];
      $values['suggestions'] = [];
      foreach ($products as $product) {
        $sug = $product->content->name;
        if ($product->code != "") {
          $sug .= " - € " . getLocalePrice($product->price_part) . ' excl. ';
        }
        if ($product->code != "") {
          $sug .= " - " . $product->code;
        }
        $values['suggestions'][] = ['value' => $sug, 'product_id' => $product->id];
      }
      echo json_encode($values);
      $this->template = null;
    }

    public function executeCategoryedit() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }
      $errors = [];

      if (isset($_GET['id'])) {
        $category = Category::find_by_id($_GET['id']);
        foreach (Config::get("catalog_languages") as $llang) {
          $cc = CategoryContent::getByCategoryIdAndLang($_GET['id'], $llang);
          if ($cc) {
            $category->contents[$llang] = $cc;
          }
          else { //deze taal is nog niet gezet? aanmaken dan.
            $cc = new CategoryContent();
            $cc->locale = $llang;
            $category->contents[$llang] = $cc;
          }
        }
      }
      else { //nieuwe
        $category = new Category();
        foreach (Config::get("catalog_languages") as $llang) {
          $cc = new CategoryContent();
          $cc->locale = $llang;
          $category->contents[$llang] = $cc;
        }
        if (isset($_GET['catname'])) {
          $category->contents['nl']->name = ucfirst($_GET['catname']);
        }
      }

      $this->uploader_image = new Uploader('uploading_image', reconstructQuery(), DIR_UPLOAD_CAT);
      $this->uploader_image->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/bmp'   => 'bmp',
        'image/gif'   => 'gif',
      ]);

      $this->uploader_template = new Uploader('uploading', reconstructQuery(['deleteid']), DIR_UPLOAD_PDF);
      $this->uploader_template->setAllowed([
        'application/pdf' => 'pdf',
      ]);


      if (isset($_POST['name'])) {
        if ($category->id == "" && isset($_GET['parent_id']) && $_GET['parent_id'] != "") {
          $category->parent_id = $_GET['parent_id'];
        }

        if ($category->id != '' && isset($_POST['parent_id'])) { //verhuis categorie naar geselecteerde categorie;
          if ($category->id != $_POST['parent_id']) { //categorie id mag niet gelijk zijn aan parent.
            $category->parent_id = $_POST['parent_id'] != '' ? $_POST['parent_id'] : null;
          }
        }

        foreach (Config::get("catalog_languages") as $llang) {
          $category->contents[$llang]->name = trim($_POST['name'][$llang]);
          $category->contents[$llang]->description = trim($_POST['description'][$llang]);
          if (isset($_POST['description2'])) $category->contents[$llang]->description2 = trim($_POST['description2'][$llang]);
          if (isset($_POST['cat_alert'])) $category->contents[$llang]->cat_alert = trim($_POST['cat_alert'][$llang]);
          $category->contents[$llang]->seo_title = trim($_POST['seo_title'][$llang]);
          $category->contents[$llang]->seo_desc = trim($_POST['seo_desc'][$llang]);
          if (isset($_POST['url'][$llang])) $category->contents[$llang]->url = trim($_POST['url'][$llang]);

          if (Config::isTrue('USE_CATEGORY_KEYWORDS') && isset($_POST['keywords'])) {
            $category->contents[$llang]->keywords = substr(trim($_POST['keywords'][$llang]), 0, 254);
          }
        }

        $category->online_custshop = isset($_POST['online_custshop']) ? 1 : 0;
        $category->online_uc = isset($_POST['online_uc']) ? 1 : 0;
        $category->online_admin = isset($_POST['online_admin']) ? 1 : 0;
        $category->online_export = isset($_POST['online_export']) ? 1 : 0;

        if ($category->id == null) {
          $category->void = 0;
        }

        //eerste taal is verplicht
        foreach (Config::get("catalog_languages") as $llang) {
          if ($category->contents[$llang]->name == "") {
            $errors[] = "Naam " . strtoupper($llang);
          }
          if (count($errors) == 0) {
            if ($category->contents[$llang]->url == "" || (!Config::isdefined("CATEGORY_CUSTOM_URL") || !Config::get("CATEGORY_CUSTOM_URL"))) {
              //url leeg/mag niet aangepast worden: altijd genereren
              $category->contents[$llang]->buildDefaultUrl($category);
              $parents = Category::getParents($category, null, $category->contents[$llang]->locale);
              foreach ($parents as $parent) {
                $catnames[] = StringHelper::slugify(trim($parent->content->name ?? ''));
              }

              $url = "/";
              $url .= $category->contents[$llang]->locale . "/";
              $url .= implode("/", $catnames);
              $category->contents[$llang]->url = $url;
            }
            // for techwinkel: root categories do not need an URL
            if (Config::get("CATEGORY_CUSTOM_URL", true) && $category->parent_id > 0) {
              $errors = PageContent::validateUrlGeneric($category->contents[$llang]->url, $category->contents[$llang]->locale, count(Config::get("catalog_languages")) > 1);
            }
          }
          if (count($errors) == 0) { //er zijn geen errors (er is een categorie titel, dus we kunnen een unieke url bouwen)
            if (!$category->contents[$llang]->buildUrlUnique()) {
              $errors["url_" . $llang] = "Uw url " . strtoupper($llang) . " is niet uniek binnen deze site. Probeer een andere. (teveel pogingen)";
            }
          }
        }

        $resultimage = $this->uploader_image->parseUpload('', false);
        if ($this->uploader_image->hasErrors()) {
          $errors[] = 'Foto upload fout: ' . $this->uploader_image->getErrorsFormatted();
        }

        $resulttemplate = $this->uploader_template->parseUpload('', false);
        if ($this->uploader_template->hasErrors()) {
          $errors[] = 'PDF upload fout: ' . $this->uploader_template->getErrorsFormatted();
        }

        if (count($errors) == 0) {

          $category->save($errors);
          foreach (Config::get("catalog_languages") as $llang) {
            $category->contents[$llang]->category_id = $category->id;
            $category->contents[$llang]->url = str_replace("--ID--", $category->id, $category->contents[$llang]->url);
            $category->contents[$llang]->save();
          }

          if ($resultimage) {
            $thumb = 'cat_' . $category->id . '_' . time() . '_thumb.' . $this->uploader_image->getExtension();
            $orig = 'cat_' . $category->id . '_' . time() . '_orig.' . $this->uploader_image->getExtension();

            //opruimen oude beelden:
            if ($category->foto_thumb != "" && file_exists(DIR_UPLOAD_CAT . $category->foto_thumb)) {
              unlink(DIR_UPLOAD_CAT . $category->foto_thumb);
            }
            if ($category->foto_orig != "" && file_exists(DIR_UPLOAD_CAT . $category->foto_orig)) {
              unlink(DIR_UPLOAD_CAT . $category->foto_orig);
            }

            $category->foto_thumb = $thumb;
            $category->foto_orig = $orig;

            //ImageHelper::resizeToFixedSize($this->uploader_image->getTempfilename(), DIR_UPLOAD_CAT.$thumb,IMAGES_CAT_THUMB_WIDTH,IMAGES_CAT_THUMB_HEIGHT, true);
            ImageHelper::resizeImageGD($this->uploader_image->getTempfilename(), DIR_UPLOAD_CAT . $thumb, IMAGES_CAT_THUMB_WIDTH, IMAGES_CAT_THUMB_HEIGHT);
            if (defined('IMAGES_CAT_ORIG_RESIZE') && IMAGES_CAT_ORIG_RESIZE == false) {
              copy($this->uploader_image->getTempfilename(), DIR_UPLOAD_CAT . $orig);
            }
            else {
              ImageHelper::resizeImageGD($this->uploader_image->getTempfilename(), DIR_UPLOAD_CAT . $orig, IMAGES_CAT_ORIG_WIDTH, IMAGES_CAT_ORIG_HEIGHT, true);
            }
          }
          elseif (isset($_POST['foto_delete'])) {
            unlink(DIR_UPLOAD_CAT . $category->foto_thumb);
            unlink(DIR_UPLOAD_CAT . $category->foto_orig);

            $category->foto_thumb = "";
            $category->foto_orig = "";
          }

          if ($resulttemplate) {
            $filename = 'cat_' . $category->id . '.pdf';
            move_uploaded_file($this->uploader_template->getTempfilename(), $this->uploader_template->getUploadfolder() . $filename);
            $category->pdf_filename = $filename;
          }
          elseif (isset($_POST['template_delete'])) {
            unlink(DIR_UPLOAD_PDF . $category->pdf_filename);
            $category->pdf_filename = '';
          }

          $category->save($errors);

          if (count($errors) == 0) {

            //flush cache
            CategoryTreeFactory::clearEntireCache();

            if (isset($_SESSION['add_missing_cat'])) {
              $cat2map = $_SESSION['add_missing_cat'];
              if ($cat2map && mb_strtolower($cat2map->name) == mb_strtolower($category->contents['nl']->name)) {
                $cat2map->destroy();
                unset($_SESSION['add_missing_cat']);
              }
            }

            $_SESSION['flash_message'] = 'Categorie opgeslagen';
            if (isset($_POST['go'])) {
              ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'action=categoryedit&id=' . $category->id);
            }
            else {
              ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . ($category->parent_id != "" ? 'catid=' . $category->parent_id : ""));
            }
          }
        }
      }
      elseif ($category->id == "") { //default values;
        $category->online_custshop = 1;
      }

      $this->errors = $errors;
      $this->category = $category;
      $categories = [];

      if ($category->id != '') {
        $categories = Category::getOnlineTree('', true);
        $categories = Category::flattenCategoryTree($categories);
      }

      $this->categories = $categories;

      BreadCrumbs::getInstance()->removeLastItem();
      $brcat = $category;
      if ($brcat->id == null && isset($_GET['parent_id']) && $_GET['parent_id'] != "") {
        $brcat = Category::getCategoryAndContent($_GET['parent_id']);
      }
      if ($brcat) {
        $parents = Category::getParents($brcat);
        if ($parents != null) {
          foreach ($parents as $parent) {
            if ($category->id == null || $parent->id != $brcat->id) {
              if (isset($parent->content)) {
                BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['worklanguage']), PageMap::getUrl('M_CATALOG_OV') . '?catid=' . $parent->id);
              }
            }
            else {
              BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['worklanguage']));
            }
          }
        }
      }
      BreadCrumbs::getInstance()->addItem('Bewerk categorie');
      $this->seo_title .= "bewerk category";
    }

    public function executeProductdelete() {

      if (!isset($_GET['id']) || ($product = Product::find_by_id($_GET['id'])) === false) {
        $_SESSION['flash_message_red'] = "Product niet gevonden";
        ResponseHelper::redirect(reconstructQueryAdd(['pageId', 'catid']));
      }

      // extra check for techwinkel, cannot delete products which are in an 'active' order
      $query = "SELECT GROUP_CONCAT(orders.id) AS order_nrs FROM invoice_product ";
      $query .= "JOIN invoice on invoice.id = invoice_product.invoice_id ";
      $query .= "JOIN orders on orders.id = invoice.order_id AND orders.status NOT IN('send', 'cancelled') ";
      $query .= "WHERE invoice_product.product_id = " . (int)$product->id . " ";
      $query .= "GROUP BY orders.id ";
      $result = DBConn::db_link()->query($query)->fetch_object();

      if ($result && !empty($result->order_nrs)) {
        $_SESSION['flash_message_red'] = __("Kan het product niet verwijderen. ");
        $_SESSION['flash_message_red'] .= __(sprintf('Deze is nog gekoppeld aan 1 of meerdere actieve bestellingen: %s. ', $result->order_nrs));
        $_SESSION['flash_message_red'] .= __('Verwijder eerst het product van de bestelling voordat je het product verwijderd.');
        ResponseHelper::redirect(reconstructQueryAdd(['pageId', 'catid']));
      }

      $success = false;
      $catprods = CategoryProduct::find_all_by(['product_id' => $product->id]);
      $catprods_notvoid = [];
      foreach ($catprods as $cp) {
        if ($cp->void == 0) {
          $catprods_notvoid[] = $cp;
        }
      }

      if (count($catprods_notvoid) <= 1) { //staat maar in 1 of geen categorie online
        $ips = InvoiceProduct::find_all_by(["product_id" => $product->id]);
        if (count($ips) == 0) { //geen invoice_products? Dan destroy, anders void.
          $product->destroy();
        }
        else {
          $product->voidProduct();
        }
        $success = true;
        $_SESSION['flash_message'] = "Product is verwijderd.";
      }
      else { //staat in meerdere categorieen online
        $catid = isset($_GET['catid']) ? $_GET['catid'] : null;
        foreach ($catprods as $cp) {
          if ($cp->category_id == $catid && $cp->product_id == $product->id) {
            $cp->destroy(); //veggooien. staat ook nog in een andere online.
            $success = true;
            $_SESSION['flash_message'] = "Product alleen verwijderd uit huidige categorie. (Dit product bevindt zich in meerdere categorieën)";
          }
        }
      }


      if ($success) {
        CategoryTreeFactory::clearEntireCache();
      }

      if (isset($_GET['url_return'])) {
        $parts = explode(',', $_GET['url_return']);
        ResponseHelper::redirect(PageMap::getUrl($parts[0]) . '?action=edit&tabscontent_active=link_products&id=' . $parts[1]);
      }
      ResponseHelper::redirect(reconstructQueryAdd(['pageId', 'catid']));
    }

    public function informDeliveryDate(Product $product, DateTime $expected_delivery_date): int {
      $product_id = $product->id;
      $order_status_query = ArrayHelper::toQueryString([Orders::STATUS_SEND, Orders::STATUS_CANCELLED]);
      $query = <<<SQL
          SELECT orders.*, invoice_product.*
          FROM invoice_product
          JOIN invoice ON invoice_product.invoice_id = invoice.id
          JOIN orders ON invoice.order_id = orders.id AND orders.status NOT IN ($order_status_query)
          WHERE invoice_product.product_id = $product_id
          GROUP BY orders.id
      SQL;
      $result = DBConn::db_link()->query($query);
      $customers_informed = 0;
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $order = (new Orders())->hydrateNext($row, $column_counter);
        $invoice_product = (new InvoiceProduct())->hydrateNext($row, $column_counter);

        $order_userdata = $order->getUserData();

        $subject = __("Update van uw bestelling") . " " . $order->getOrderNr();
        $email_message = getLanguageFile($order_userdata['user']->locale, 'mail_delivery_date_update.html');
        $email_message = str_replace("<!--@@ AANHEF @@-->", $order_userdata['user']->getAanhef(), $email_message);
        $email_message = str_replace("<!--@@ PRODUCT @@-->", $invoice_product->description, $email_message);
        $expected_delivery_date_readable = DateTimeHelper::convertToReadable($expected_delivery_date->format('Y-m-d H:i:s'));
        $email_message = str_replace("<!--@@ EXPECTED_DELIVERY_DATE @@-->", $expected_delivery_date_readable, $email_message);
        $email_message = (new WrapperService($email_message, $order_userdata['user']->locale))->wrap();
        $mailer = GsdMailer::build($order_userdata['user']->email, $subject, $email_message);
        $mailer->send();
        $customers_informed++;
      }

      return $customers_informed;
    }

  }