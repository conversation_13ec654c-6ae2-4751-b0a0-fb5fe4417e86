<?php

  use domain\contact\service\ContactService;

  use domain\productrange\service\GetCategories;

  /**
   * @property $page
   * @property $site
   */
  class siteComponents extends gsdComponents {

    public function preExecute() {}

    public function executeSearchbar() {
      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/searchbar.js');
    }

    public function executeCookiebanner() {}

    public function executeHeader() {
      $ignoreIds = [2, 6]; // home and categories are omitted
      $this->topMenuItems = array_filter(Navigation::getItemChildren(1), fn ($item) => !in_array($item->pageId, $ignoreIds));

      $categories = (new GetCategories('nl'))->getAllOnline();
      $this->categories = $categories;

      // get first 8 items from the dummy product items
      $this->bottomMenuItems = array_slice($this->categories, 0, 7);

      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/header.js');
    }

    public function executeFooter() {
      // jari systems
      $this->organisation = Organisation::find_by_id(2);

      $this->email = $this->organisation->getEmail();
      $this->phone = $this->organisation->phone;
    }

    public function executeInventoryShowcase() {}

    public function executePromotionVideo(): void {
      // defaults to 1 is no page is set
      $pageId = $this->page->id ?? 1;
      $image = Config::get('PAGE_COMPONENT_IMAGES')[$pageId]['promotionImage'];

      $this->imageUrl = "images/$image";

      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/video.js');
    }

    public function executeQuestions(): void {
      // defaults to 1 is no page is set
      $pageId = $this->page->id ?? 1;
      $image = Config::get('PAGE_COMPONENT_IMAGES')[$pageId]['questionsImage'];

      $this->imageUrl = "images/$image";
    }

    public function executeContactForm(): void {
      $errors = [];
      if (isset($_POST['submit'])) {
        $subject = "Vacature aanvraag vanaf website " . $_SERVER['HTTP_HOST'];
        $file = $_FILES['file']['name'] ? $_FILES['file'] : null;
        $errors = ContactService::handleContactSubmission($subject, $_POST, $file);
      }

      $this->includeFile = true;
      $this->errors = $errors;
    }

    public function executeGsdeditor(): void {
      Context::addStylesheet(URL_INCLUDES . 'jsscripts/simplelightbox/v2.1/dist/simple-lightbox.css', true);
      Context::addJavascript(URL_INCLUDES . 'jsscripts/simplelightbox/v2.1/dist/simple-lightbox.jquery.js', true);
      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/lightbox.js');
      Context::addJavascriptInlineBottomFile(DIR_PROJECT_FOLDER . 'modules/site/js/parralax.js');
    }
  }
