<?php

  use domain\contact\service\ContactService;

  class siteJariActions extends siteActions {

    public function preExecute() {
      parent::preExecute();
      $this->showBreadcrumbs = count(BreadCrumbs::getInstance()->getItems()) > 1;
    }

    public function executeContact(): void {
      $errors = [];
      if (isset($_POST['submit'])) {
        $subject = "Contact aanvraag vanaf website " . $_SERVER['HTTP_HOST'];
        $errors = ContactService::handleContactSubmission($subject, $_POST);
      }

      // jari systems
      $this->organisation = Organisation::find_by_id(2);

      $this->email = $this->organisation->getEmail();
      $this->phone = $this->organisation->phone;

      $this->errors = $errors;
    }

    public function executeSearch() {
      $results = [];
      if (isset($_POST['search'])) {
        $searchVal = DbHelper::escape($_POST['search']);
        // @todo search for products based on $searchVal
      }

      $this->results = $results;
    }
  }
