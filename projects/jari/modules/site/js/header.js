$(document).ready(function() {
  $(".hamburger-button").on("click", function() {
    $("#search-form-mobile").slideUp();
    $(".menubar").toggleClass("active");
    $(this).toggleClass("hamburger-active");

    $(".menubar .dropdown").removeClass("active");
    $(".menubar .all-product-pages").removeClass("active");
  });

  $(".menubar .dropdown").on("click", function() {
    $(this).toggleClass("active");
    $(".menubar .all-product-pages").toggleClass("active");
  });

  $(window).on("scroll", function () {
    if ($(".hamburger").hasClass("hamburger-active")) return;

    const scrollTop = $(this).scrollTop();
    const scrollHeight = $(document).height();
    const windowHeight = $(this).height();
    const scrolledToBottom = scrollTop + windowHeight >= scrollHeight;

    $("header").toggleClass("hidden", scrollTop >= this.lastScrollTop && scrollTop >= 150 && !scrolledToBottom);
    this.lastScrollTop = scrollTop;
  });
});
