$(document).ready(function () {
  const $window = $(window);
  const $images = $('.parralax img'); // Select all parallax images

  function isInViewport($element) {
    const elementTop = $element.offset().top;
    const elementBottom = elementTop + $element.outerHeight();
    const viewportTop = $window.scrollTop();
    const viewportBottom = viewportTop + $window.height();
    return elementBottom > viewportTop && elementTop < viewportBottom;
  }

  function updateParallax() {
    const isMobile = /Mobi|Android/i.test(navigator.userAgent) || $window.width() < 768;
    if (isMobile) {
      $images.css('transform', 'none');
      return;
    }

    $images.each(function () {
      const $image = $(this);
      if (isInViewport($image)) {
        const scrollPos = $window.scrollTop();
        const imageOffset = $image.offset().top;
        const yPos = (scrollPos - imageOffset) * 0.4; // Adjust the multiplier to control speed
        $image.css('transform', 'translateY(' + yPos + 'px)');
      }
    });
  }

  $window.on('scroll resize', updateParallax);
  updateParallax(); // Initial check
});