<?php

  class productcontJariActions extends productcontActions {

    public function executeList() {
      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 50;
      $this->pager->setWriteCount(true);
      $this->pager->setWritePrint(true);
      $this->pager->handle();
      //einde pager props

      if (!isset($_SESSION['article_search'])) {
        $_SESSION['article_search'] = '';
      }
      if (!isset($_SESSION['article_brand'])) {
        $_SESSION['article_brand'] = '';
      }

      if (isset($_POST['article_search'])) {
        $_SESSION['article_search'] = trim($_POST['article_search']);
      }
      if (isset($_POST['article_brand'])) {
        $_SESSION['article_brand'] = trim($_POST['article_brand']);
      }

      $filter = "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id AND locale='nl' ";
      $filter .= 'WHERE 1 ';
      if ($_SESSION['article_brand'] != "") {
        $filter .= " AND brand_id=" . escapeForDB($_SESSION['article_brand']) . ' ';
      }
      if ($_SESSION['article_search'] != "") {
        $searchstr = escapeForDB($_SESSION['article_search']);
        $filter .= " AND (";
        $filter .= "product_cont.code LIKE '%" . $searchstr . "%' ";
        $filter .= "OR product_cont.material LIKE '%" . $searchstr . "%' ";
        $filter .= "OR product_cont_content.name LIKE '%" . $searchstr . "%' ";
        $filter .= "OR product_cont_content.keywords LIKE '%" . $searchstr . "%' ";
        $filter .= " )";
      }
      $this->pager->count = ProductCont::count_all_by([], $filter);
      $query = "SELECT * FROM product_cont ";
      $query .= $filter . $this->pager->getLimitQuery();

      $result = DBConn::db_link()->query($query);
      $product_conts = [];
      while ($row = $result->fetch_row()) {
        $productc = new ProductCont();
        $productc->hydrate($row);

        $productcc = new ProductContContent();
        $productcc->hydrate($row, count(ProductCont::columns));

        $productc->content = $productcc;

        $product_conts[] = $productc;
      }

      $this->product_conts = $product_conts;
      $this->categories = AppModel::mapObjectIds(Category::getCategories());
    }

    public function executeEdit() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $errors = [];

      //new product_cont
      $product_cont = new ProductCont();
      $products = [];
      if (isset($_GET['id'])) {
        $product_cont = ProductCont::find_by_id($_GET['id']);
        if (!$product_cont) {
          ResponseHelper::redirectError("U probeert een productcontainer te openen welke (reeds) is verwijderd of u heeft onvoldoende rechten");
        }
        $products = Product::find_all_by(["void" => 0, 'product_cont_id' => $_GET["id"]], " ORDER BY sort ASC");

        foreach (Config::get("catalog_languages") as $llang) {
          $cc = ProductContContent::getByIdAndLang($_GET['id'], $llang);
          if ($cc) {
            $product_cont->contents[$llang] = $cc;
          }
          else { //deze taal is nog niet gezet? aanmaken dan.
            $cc = new ProductContContent();
            $cc->locale = $llang;
            $product_cont->contents[$llang] = $cc;
          }
        }

        $product_cont->options = AppModel::mapObjectIds(ProductContOption::find_all_by(['product_cont_id' => $product_cont->id]), 'code');
      }
      else { //nieuwe
        foreach (Config::get("catalog_languages") as $llang) {
          $cc = new ProductContContent();
          $cc->locale = $llang;
          $product_cont->contents[$llang] = $cc;
        }

        foreach (ProductContOption::CODES as $option) {
          $product_cont->options[$option] = new ProductContOption([
            'code' => $option,
          ]);
        }
      }

      $uploader_main_image = new Uploader('uploading_main_image', reconstructQuery(), DIR_UPLOAD_CAT);
      $uploader_main_image->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/bmp'   => 'bmp',
        'image/gif'   => 'gif',
      ]);
      $this->uploader_main_image = $uploader_main_image;

      $uploader_product_sheet = new Uploader('uploading_product_sheet', reconstructQuery(), DIR_UPLOAD_PRODUCT_SHEET);
      $uploader_product_sheet->setAllowed([
        'application/pdf' => 'pdf',
      ]);
      $this->uploader_product_sheet = $uploader_product_sheet;

      $this->tabscontent_active = isset($_POST['tabscontent_active']) ? $_POST['tabscontent_active'] : (isset($_GET['tabscontent_active']) ? $_GET['tabscontent_active'] : "");

      $related_product_containers = $product_cont->id ? ProductContRelated::getRelatedOf($product_cont->id) : [];

      //save product container
      if (isset($_POST['code'])) {

        $product_cont->code = trim($_POST['code']);
        $product_cont->category1_id = $_POST['category1_id'];
        $product_cont->online = isset($_POST['online']) ? 1 : 0;
        $product_cont->phasingout = isset($_POST['phasingout']) ? 1 : 0;

        foreach (Config::get("catalog_languages") as $llang) {
          if (Config::get("PRODUCT_CONTAINER_CUSTOM_URL", true)) {
            $product_cont->contents[$llang]->url = trim($_POST['url'][$llang]);
          }
          $product_cont->contents[$llang]->name = trim($_POST['name'][$llang]);
          $product_cont->contents[$llang]->alternate_name = (isset($_POST['alternate_name'][$llang])) ? trim($_POST['alternate_name'][$llang]) : '';
          $product_cont->contents[$llang]->description = trim($_POST['description'][$llang]);
          $product_cont->contents[$llang]->description2 = trim($_POST['description2'][$llang]);
          if (isset($_POST['keywords'][$llang])) {
            $labeltjes = [];
            foreach ($_POST["keywords"][$llang] as $k => $label) {
              $label = strtolower(trim($label));
              if ($label != "") {
                $labeltjes[$label] = $label;
              }
            }
            $keywords_max_length = (int)ProductContContent::field_structure['keywords']['length'];
            $product_cont->contents[$llang]->keywords = substr(implode(",", $labeltjes), 0, $keywords_max_length);
          }
        }

        foreach ($_POST['options'] as $optionKey => $optionValue) {
          if (!isset($product_cont->options[$optionKey])) {
            $product_cont->options[$optionKey] = new ProductContOption(['code' => $optionKey]);
          }
          $product_cont->options[$optionKey]->value = $optionValue;
        }

        //validation
        if ($product_cont->code == '') {
          $errors['code'] = 'Voer een code in';
        }
        if ($product_cont->category1_id == '') {
          $errors['category_id'] = 'Categorie 1 is verplicht';
        }

        //check of name en desc per lang geset zijn
        foreach ($product_cont->contents as $pcc) {
          if ($pcc->name == "") {
            $errors["name_" . $pcc->locale] = "Naam " . $pcc->locale;
          }

          if (count($errors) == 0) {
            if ($product_cont->contents[$llang]->url == "" || (!Config::isdefined("PRODUCT_CONTAINER_CUSTOM_URL") || !Config::get("PRODUCT_CONTAINER_CUSTOM_URL"))) {
              $product_cont->contents[$llang]->buildDefaultUrl($product_cont->category1_id); //url leeg, of geen custom, dan altijd genereren
            }
          }
          if (count($errors) == 0) { //er zijn geen errors (er is een product container titel, dus we kunnen een unieke url bouwen)
            if (!$product_cont->contents[$llang]->buildUrlUnique()) {
              $errors["url"] = "Uw url is niet uniek binnen deze site. Probeer een andere. (teveel pogingen)";
            }
          }

        }

        $resultmainimage = $uploader_main_image->parseUpload('', false);
        if ($uploader_main_image->hasErrors()) {
          $errors[] = 'Beeld upload fout bij hoofd foto: ' . $uploader_main_image->getErrorsFormatted();
        }

        $result_product_sheet_upload = $uploader_product_sheet->parseUpload('', true);
        if ($uploader_product_sheet->hasErrors()) {
          $errors[] = 'Upload fout bij product sheet: ' . $uploader_product_sheet->getErrorsFormatted();
        }

        if (count($errors) == 0) {
          $product_cont->save();

          if ($resultmainimage) {
            $thumb = 'pr_' . $product_cont->id . '_' . time() . '_main_thumb.' . $uploader_main_image->getExtension();
            $orig = 'pr_' . $product_cont->id . '_' . time() . '_main_orig.' . $uploader_main_image->getExtension();

            //opruimen oude beelden
            if ($product_cont->image_thumb != "" && file_exists(DIR_UPLOAD_CAT . $product_cont->image_thumb)) {
              unlink(DIR_UPLOAD_CAT . $product_cont->image_thumb);
            }
            if ($product_cont->image_orig != "" && file_exists(DIR_UPLOAD_CAT . $product_cont->image_orig)) {
              unlink(DIR_UPLOAD_CAT . $product_cont->image_orig);
            }

            $product_cont->image_thumb = $thumb;
            $product_cont->image_orig = $orig;

            ImageHelper::resizeToFixedSize($uploader_main_image->getTempfilename(), DIR_UPLOAD_CAT . $thumb, IMAGES_THUMB_WIDTH, IMAGES_THUMB_HEIGHT);
            //ImageHelper::cropSquareImageGD($uploader->getTempfilename(), DIR_UPLOAD_CAT.$thumb,IMAGES_THUMB_WIDTH,IMAGES_THUMB_HEIGHT, false, true);
            ImageHelper::resizeImageGD($uploader_main_image->getTempfilename(), DIR_UPLOAD_CAT . $orig, IMAGES_ORIG_WIDTH, IMAGES_ORIG_HEIGHT, true);

            if (Config::get('PRODUCT_IMAGES_WATERMARK', true)) {
              $watermark_thumb = Config::get('PRODUCT_WATERMARK_THUMB');
              $watermark_large = Config::get('PRODUCT_WATERMARK_LARGE');

              if (file_exists($watermark_thumb) && file_exists($watermark_large)) {
                ImageHelper::mergeImage(DIR_UPLOAD_CAT . $thumb, $watermark_thumb, DIR_UPLOAD_CAT . $thumb);
                [$width] = getimagesize(DIR_UPLOAD_CAT . $orig);
                [$watermark_width] = getimagesize($watermark_large);
                if ($width < $watermark_width) {
                  ImageHelper::mergeImage(DIR_UPLOAD_CAT . $orig, $watermark_thumb, DIR_UPLOAD_CAT . $orig);
                }
                else {
                  ImageHelper::mergeImage(DIR_UPLOAD_CAT . $orig, $watermark_large, DIR_UPLOAD_CAT . $orig);
                }
              }
            }
          }
          elseif (isset($_POST['main_image_delete'])) {
            unlink(DIR_UPLOAD_CAT . $product_cont->image_thumb);
            unlink(DIR_UPLOAD_CAT . $product_cont->image_orig);

            $product_cont->image_thumb = "";
            $product_cont->image_orig = "";
          }

          if ($result_product_sheet_upload) {
            // remove old (current) file
            if ($product_cont->productsheet != "" && file_exists(DIR_UPLOAD_PRODUCT_SHEET . $product_cont->productsheet)) {
              unlink(DIR_UPLOAD_PRODUCT_SHEET . $product_cont->productsheet);
            }
            $product_cont->productsheet = $uploader_product_sheet->getFilelocation();
          }
          elseif (isset($_POST['product_sheet_delete'])) {
            @unlink(DIR_UPLOAD_PRODUCT_SHEET . $product_cont->productsheet);
            $product_cont->productsheet = "";
          }

          $product_cont->save();

          if (isset($_POST['addrelproduct']) && $_POST['addrelproduct'] != "") {
            if (ProductContRelated::isUnique($product_cont->id, $_POST['addrelproduct'])) {
              $relprod = new ProductContRelated();
              $relprod->product_cont_id = $product_cont->id;
              $relprod->related_product_cont_id = $_POST['addrelproduct'];
              $relprod->save();
            }
          }

          foreach ($product_cont->contents as $pcc) {
            $pcc->product_cont_id = $product_cont->id;
            $pcc->save();
          }

          foreach ($product_cont->options as $pco) {
            if (empty($pco->value)) {
              if ($pco->id) {
                $pco->destroy();
              }
            }
            else {
              $pco->product_cont_id = $product_cont->id;
              $pco->save();
            }
          }

          $_SESSION['flash_message'] = __("Product container is opgeslagen.");

          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
          }
          elseif (isset($_POST['go_cat'])) {
            ResponseHelper::redirect(Pagemap::getUrl('M_CATALOG_OV') . '?catid=' . $product_cont->category1_id);
          }
          else {
            ResponseHelper::redirect(reconstructQueryAdd([
                'pageId',
                'action',
              ]) . 'id=' . $product_cont->id . '&tabscontent_active=' . $this->tabscontent_active);
          }
        }
      }

      $categories = Category::getTreeWithContent('nl', true);
      $categories = Category::flattenCategoryTree($categories);
      $this->categories = $categories;
      $this->related_product_containers = $related_product_containers;

      $this->children = $products;
      $this->errors = $errors;
      $this->product_cont = $product_cont;

      BreadCrumbs::getInstance()->addItem((isset($product_cont->contents[$_SESSION['worklanguage']]) && $product_cont->contents[$_SESSION['worklanguage']]->name != "") ? $product_cont->contents[$_SESSION['worklanguage']]->name : 'Nieuwe productcontainer');

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.full.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

    }

    public function executeSearch() {
      $locale = $_SESSION['lang'];

      $searchval = escapeForDB(trim($_GET['val']));
      $filtquery = "WHERE 1 ";
      if ($searchval != "") {
        $filtquery .= " AND (";
        $filtquery .= " product_cont_content.name LIKE '%" . $searchval . "%'";
        $filtquery .= " OR product_cont_content.material LIKE '%" . $searchval . "%'";
        $filtquery .= " OR product_cont.code LIKE '%" . $searchval . "%'";
        $filtquery .= " )";
      }

      $filtquery .= " ORDER BY product_cont_content.name ";
      $filtquery .= " LIMIT 100 ";

      $product_containers = ProductCont::getProductConts($locale, $filtquery);
      echo json_encode($product_containers);
      $this->template = null;
    }

    public function executeProductcontdelete() {

      if (empty($_GET['id']) || ($product_cont = ProductCont::find_by_id($_GET['id'])) === false) {
        $_SESSION['flash_message_red'] = __("Product container is niet gevonden!");
        ResponseHelper::redirect(PageMap::getUrl('M_PRODUCTCONT'));
      }

      // extra check for techwinkel, cannot delete products which are in an 'active' order
      $query = "SELECT GROUP_CONCAT(orders.id) AS order_nrs FROM product ";
      $query .= "JOIN invoice_product ON invoice_product.product_id = product.id ";
      $query .= "JOIN invoice on invoice.id = invoice_product.invoice_id ";
      $query .= "JOIN orders on orders.id = invoice.order_id AND orders.status NOT IN('send', 'cancelled') ";
      $query .= "WHERE product.product_cont_id = " . (int)$product_cont->id . " ";
      $query .= "GROUP BY orders.id ";

      $result = DBConn::db_link()->query($query)->fetch_object();
      if ($result && !empty($result->order_nrs)) {
        $_SESSION['flash_message_red'] = __("Kan de product container niet verwijderen. ");
        $_SESSION['flash_message_red'] .= __(sprintf('Deze is nog gekoppeld aan 1 of meerdere actieve bestellingen: %s. ', $result->order_nrs));
        $_SESSION['flash_message_red'] .= __('Verwijder eerst het product van de bestelling voordat je de product container verwijderd.');
        ResponseHelper::redirect(PageMap::getUrl('M_PRODUCTCONT'));
      }

      $product_list = Product::find_all_by(['product_cont_id' => $_GET['id']]);
      foreach ($product_list as $product) {
        $product->destroy();
      }
      $product_cont->destroy();

      $_SESSION['flash_message_red'] = __("Product container is verwijderd!");
      ResponseHelper::redirect(PageMap::getUrl('M_PRODUCTCONT'));
    }

    public function executeSearchmodal() {
      $this->triggerName = $_GET['triggerName'];
      $this->template_wrapper_clear = true;
    }

    public function executeRelateddelete() {
      $prod = ProductContRelated::find_by_id($_GET['relprodid']);
      if ($prod) {
        $prod->destroy();
        echo 'Gerelateerde productcontainer verwijderd.';
      }
      else {
        echo 'Gerelateerdd productcontainer NIET gevonden.';
      }
      $this->template = null;
    }

  }