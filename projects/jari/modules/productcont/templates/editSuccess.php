<section class="title-bar">
  <h1>
    <?php echo __("Product container"); ?> <?php echo ($product_cont->id != '') ? __('bewerken') : __('aanmaken'); ?>
  </h1>

  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>

<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
<script type="text/javascript">
  $(document).ready(function () {
    ckeditorInit();
    CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
    ckeditorScaytNl();

    <?php if(!Config::isdefined('CATALOG_CKEDITOR_TOOLBAR') || Config::get('CATALOG_CKEDITOR_TOOLBAR') == "Simple"): ?>
    ckeditorSimple();
    <?php else:
    $_SESSION['filebrowserPath'] = DIR_UPLOADS . 'sites/' . $currentsite->id . '/ckfiles';
    ?>
    CKEDITOR.config['filebrowserBrowseUrl'] = '<?php echo Context::getSiteDomain() . URL_INCLUDES ?>ckeditor4/RichFilemanager/index.html';
    <?php endif; ?>

    $(".tabslink").click(function (event) {
      var id = 'content_' + $(this).attr("id").substring(5);
      $(".tabslink").removeClass('active');
      //$(".tabslink").hover();
      $(this).addClass('active');

      $(".tabscontent").hide();
      $("#tabscontent_active").val($(this).attr("id"));
      $('#' + id).show();
      event.preventDefault();
    });

    <?php if(isset($tabscontent_active) && $tabscontent_active != ""): ?>
    $("#<?php echo $tabscontent_active ?>").click();
    <?php else: ?>
    $(".tabslink:first").click();
    <?php endif; ?>

    new SimpleLightbox(".logoimage",{
      fileExt: false
    });

    $(".keywords").select2({
      tags: true,
      tokenSeparators: [',']
    });

    function setupTableDnD() {
      $("#sortablecontrol").one("click", function () {
        $("#sortablecontrol").val('<?php echo __('Sorteren uitzetten');?>');
        $("#sortablecontrol").addClass('sorton');
        $(".sortable").tableDnD(
          {
            onDrop: function (table, row) {
              $('#message').html('<div class="message"><?php echo __('Volgorde opslaan...');?> </div><br/>');
              $('#message').load("<?php echo Pagemap::getUrl('M_CATALOG') ?>?action=move&type=" + $(table).attr('id'), {values: $.tableDnD.serialize('id')}, function () {
                $('#message').html('<div class="message"><?php echo __('Volgorde opgeslagen');?></div><br/>');
              });
            }
          }
        );

        $(this).one("click", function () {
          $(".sortable tr").unbind().css({cursor: "default"});
          $("#sortablecontrol").val('Sorteren aanzetten');
          $("#sortablecontrol").removeClass('sorton');
          setupTableDnD();
        });
      });
    }

    setupTableDnD();


  });
</script>
<ul id="tabnav" class="nav nav-tabs">
  <li style="margin-left:5px;"><a href="" class="tabslink" id="link_general"><?php echo __('Algemeen'); ?></a></li>
  <?php if(!empty($product_cont->id)): ?>
    <li><a href="#" class="tabslink" id="link_products"><?php echo __('Producten'); ?></a></li>
    <li>
      <a href="#" class="tabslink" id="link_related"><?php echo __('Gerelateerde'); ?></a>
    </li>
  <?php endif; ?>
</ul>

<?php writeErrors($errors, true); ?>

<div id="message"></div>
<form class="edit-form" method="post" enctype="multipart/form-data">
  <input type="hidden" value="" name="tabscontent_active" id="tabscontent_active" />
  <div id="content_general" class="tabscontent">
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td colspan="2"><?php echo __('Product container'); ?></td>
      </tr>
      <tr class="dataTableRow">
        <td class="head">Code</td>
        <td>
          <input type="text" name="code" id="code" value="<?php echo $product_cont->code; ?>" />
          <span class='asterisk'>*</span>
          <?php echo showHelpButton('Voor intern gebruik. Standaard is dit "Productnaam - Material"', 'Code'); ?>
        </td>
      </tr>

      <tr class="dataTableRow">
        <td class="head">Categorie</td>
        <td>
          <select name="category1_id" id="category1_id">
            <option value="">Selecteer categorie...</option>
            <?php
              $parents = array();
              $intend = -1;
              foreach ($categories as $cat) :
                if(!isset($parents[$cat->parent_id == '' ? "NULL" : $cat->parent_id])) :
                  $intend++;
                  $parents[$cat->parent_id == '' ? "NULL" : $cat->parent_id] = $intend;
                else :
                  $intend = $parents[$cat->parent_id == '' ? "NULL" : $cat->parent_id];
                endif;

                $intend_space = '';
                for ($i = 0; $i < $intend; $i++) :
                  $intend_space .= "&nbsp;&nbsp;&nbsp;&nbsp;";
                endfor; ?>
                <option
                  value="<?php echo $cat->id ?>" <?php if($cat->id == $product_cont->{"category1_id"}): echo "selected"; endif; ?>><?php echo $intend_space . $cat->getName($_SESSION['lang']); ?></option>
              <?php endforeach; ?>
          </select>
          <span class='asterisk'>*</span>
        </td>
      </tr>

      <?php foreach (ProductContOption::CODES as $optionKey => $optionLabel): ?>
        <tr class="dataTableRow">
          <td class="head"><?php echo $optionLabel ?></td>
          <td>
            <input type='text' name='options[<?php echo $optionKey ?>]' value="<?php if (isset($product_cont->options[$optionKey])) echo $product_cont->options[$optionKey]->value ?>" />
          </td>
        </tr>
      <?php endforeach; ?>

      <tr class="dataTableRow">
        <td class="head">Productsheet</td>
        <td>
          <?php if($product_cont->productsheet != ""): ?>
            <?php echo __('Bestand') ?>: <?php echo $product_cont->productsheet; ?>
            <br>
            <?php echo __('Verwijder product sheet:') ?>
            <input type="checkbox" value="1" name="product_sheet_delete"><br/>
            <br>
            <?php endif; ?>
          <?php echo $uploader_product_sheet->getInputs(); ?>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td class="head" style="width: 160px;">Online</td>
        <td><input type="checkbox" value="1" name="online" <?php writeIfCheckedVal($product_cont->online, 1) ?>/>
          <?php echo showHelpButton('Deze product container staat op de webshop', 'Online'); ?>
        </td>
      </tr>

    </table>
    <?php if(count(Config::get("catalog_languages")) > 1) { ?>
      <div
        style="padding: 5px 5px;vertical-align: middle; background-color: #F8F8F8;border-right: 1px solid #C5D5DD;border-left: 1px solid #C5D5DD;">
        <script type="text/javascript">
          $(document).ready(function () {
            var lang = <?php echo json_encode(LanguageHelper::getLanguagesNL()) ?>;

            $('a.languageselect').click(function (item) {
              $('a.languageselect img').css('border', '1px solid white');
              $('div.languagebox').hide();
              $(this).find("img").css('border','1px solid blue');
              $('#content_'+$(this).attr("id")).show();
              $('#currentlanguage').text(lang[$(this).attr("id")]);
            });
            $('#<?php echo isset($_SESSION['worklanguage']) ? $_SESSION['worklanguage'] : 'nl' ?>').click();
          });
        </script>
        <?php
          foreach (Config::get("catalog_languages") as $catlang) :
            echo '<a href="javascript:void(0);" class="languageselect" id="' . $catlang . '"><img alt="' . $catlang . '" src="/gsdfw/images/flags/' . $catlang . '.png" style="width: 22px; height: 15px;margin-right: 5px;"></a>';
          endforeach;
          echo ' Huidige taal: <span id="currentlanguage" style="font-weight:bold;"></span>';
        ?></div>
      <?php
    }
      foreach ($product_cont->contents as $catlang => $contents): ?>
        <div id="content_<?php echo $catlang ?>" class="languagebox"
             style="<?php if(count(Config::get("catalog_languages")) == 1): ?>display: block;<?php endif; ?>;">
          <table class="default_table notopborder">
            <tr class="dataTableRow">
              <td class="head" style="width: 160px;">Naam <?php echo strtoupper($catlang) ?></td>
              <td><input type="text" name="name[<?php echo $catlang ?>]" id="name_<?php echo $catlang ?>"
                         value="<?php echo escapeForInput($contents->name) ?>" maxlength="255" /> <span
                  class="asterisk">*</span></td>
            </tr>
            <?php if(Config::get("PRODUCT_CONTAINER_CUSTOM_URL", true)): ?>
              <tr class="dataTableRow">
                <td valign='top' class="head">URL <?php echo strtoupper($catlang) ?></td>
                <td>
                  <input type='text' name='url[<?php echo $catlang ?>]'
                         value="<?php echo escapeForInput($contents->url) ?>" style="width:590px;" />
                  <?php echo showHelpButton("Dit is de url die wordt gebruikt voor deze product container. Als de url als ergens anders wordt gebruikt, dan wordt deze automatisch uniek gemaakt.", 'URL') ?>
                </td>
              </tr>
            <?php endif; ?>
            <tr class="dataTableRow">
              <td class="head">Omschrijving <?php echo strtoupper($catlang) ?></td>
              <td>
                <textarea name="description[<?php echo $catlang ?>]" id="description_<?php echo $catlang ?>"
                          class="ckeditor"><?php echo $contents->description ?></textarea>
              </td>
            </tr>
            <?php if(Config::isdefined("PRODUCTCONT_EDIT_CONTENT_2") && Config::get("PRODUCTCONT_EDIT_CONTENT_2")): ?>
              <tr class="dataTableRow">
                <td class="head">Omschrijving 2 <?php echo strtoupper($catlang) ?></td>
                <td>
                    <textarea name="description2[<?php echo $catlang ?>]" id="description2_<?php echo $catlang ?>"
                              class="ckeditor"><?php echo $contents->description2 ?></textarea>
                </td>
              </tr>
            <?php endif; ?>
            <tr class="dataTableRow">
              <td valign='top' class="head">Zoekwoorden <?php echo strtoupper($catlang) ?></td>
              <td>
                <select class="keywords" name="keywords[<?php echo $catlang ?>][]" id="keywords[<?php echo $catlang ?>]"
                        multiple="multiple" style="width: 80%;">
                  <?php foreach ($contents->getKeywordsAr() as $keyword): ?>
                    <option value="<?php echo $keyword ?>" selected><?php echo $keyword ?></option>
                  <?php endforeach; ?>
                </select>
                <?php echo showHelpButton("Woorden waarbij dit product ook gevonden moet worden.", 'Zoekwoorden') ?>
              </td>
            </tr>
          </table>
        </div>
      <?php endforeach; ?>
    <table class="default_table">
      <tr class="dataTableRow">
        <td class="head" style="width: 160px;"><?php echo __('Standaard foto'); ?></td>
        <td>
          <?php
            echo $uploader_main_image->getInputs();
            if($product_cont->image_thumb != "") {
              $out = "";
              $out .= ' ' . __('verwijder hoofd foto:') . ' <input type="checkbox" value="1" name="main_image_delete"><br/>';
              $out .= ' <a href="' . URL_UPLOAD_CAT . $product_cont->image_orig . '" class="logoimage" title="' . __('Hoofd foto') . '">';
              $out .= ' <img src="' . URL_UPLOAD_CAT . $product_cont->image_thumb . '" alt="foto" style="border:1px solid #BBBBBB"></a> ';
              echo $out;
            }
          ?>
        </td>
      </tr>
    </table>

  </div>
  <div id="content_products" class="tabscontent">
    <?php TemplateHelper::includePartial('_productcontainerproducts.php', 'productcont', compact('product_cont', 'children')); ?>
  </div>

  <div id="content_related" class="tabscontent">
    <?php TemplateHelper::includePartial('_productcontainerrelated.php', 'productcont', [
      'product_container'          => $product_cont,
      'related_product_containers' => $related_product_containers,
    ]) ?>
  </div>

  <input type="submit" name="go" value="Opslaan" title="Sla uw wijzigingen op" class="gsd-btn gsd-btn-primary" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"
         title="Sla uw wijzigingen op en ga terug naar lijst" class="gsd-btn gsd-btn-primary" />
  <input type="submit" name="go_cat" value="Opslaan en naar categorie"
         title="Sla uw wijzigingen op en ga terug naar categorie" class="gsd-btn gsd-btn-secondary" />
  <input type="submit" name="cancel" value="Annuleren" title="Sla wijzigen niet op" class="gsd-btn gsd-btn-secondary" />
</form>
