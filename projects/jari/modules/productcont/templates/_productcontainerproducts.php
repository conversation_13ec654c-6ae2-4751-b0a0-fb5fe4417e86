<div class="list-filter-form">
  <input type="button" id="sortablecontrol" name="" value="Sorteren aanzetten" class="gsd-btn gsd-btn-secondary" />
</div>
<?php if(count($children) == 0): ?>
  <section class="empty-list-state">
    <p>Er zijn geen onderliggende producten gevonden.</p>
    <?php if(empty($product_cont->id)): ?>
      <strong>Sla de productcontainer op voordat je producten kunt toevoegen.</strong>
    <?php else: ?>
      <a class="gsd-btn gsd-btn-primary"
         href="<?php echo PageMap::getUrl('M_PRODUCTLIST') ?>?action=productedit&product_container_id=<?php echo $product_cont->id ?>">
        Nieuw product aanmaken onder deze productcontainer
      </a>
    <?php endif; ?>
  </section>
<?php else: ?>
  <table id="product" class="default_table sortable" cellspacing="0" cellpadding="2">
    <tr class="dataTableHeadingRow nodrop nodrag">
      <td><?php echo __('Code'); ?></td>
      <td><?php echo __('Naam'); ?></td>
      <td width="70"><?php echo __('Acties'); ?></td>
    </tr>
    <?php
      $colors = Color::getColorList();
      foreach ($children as $product):
        ?>
        <tr class="dataTableRow trhover" id="product_<?php echo $product->id ?>">
          <td><?php echo $product->code ?></td>
          <td><?php echo $product->getName() ?></td>
          <td>
            <?php echo BtnHelper::getEdit(PageMap::getUrl('M_PRODUCTLIST') . '?action=productedit&id=' . $product->id) ?>
            <?php echo BtnHelper::getRemove(PageMap::getUrl('M_PRODUCTLIST') . '?action=productdelete&id=' . $product->id .'&url_return=M_PRODUCTCONT', __('Weet u zeker dat u dit item wilt verwijderen?')) ?>
          </td>
        </tr>
      <?php
      endforeach;
    ?>
  </table>

  <br>
  <a class="gsd-btn gsd-btn-primary"
     href="<?php echo PageMap::getUrl('M_PRODUCTLIST') ?>?action=productedit&product_container_id=<?php echo $product_cont->id ?>">
    + Nieuw product aanmaken onder deze productcontainer
  </a>
<?php
endif;
?>