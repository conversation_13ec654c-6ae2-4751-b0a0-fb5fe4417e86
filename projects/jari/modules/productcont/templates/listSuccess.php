<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem($current_action->pageId)->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>

<script type="text/javascript">
  $(document).ready(function () {

    $("#article_search").trigger("focus")

    $(".fullpath").on("click", function (event) {
      event.preventDefault();
    });

    $(".toggleactive").on("click", function (event) {
      event.preventDefault();
      let current = $(this);
      let data = current.data();

      const id = current.parents("tr").attr('id').split('_')[1];
      const type = "productcont";

      const url = "<?php echo reconstructQueryAdd(['pageId']); ?>action=toggleactive" + type + "&id=" + id;

      $.getJSON(url).done(function (result) {
        let msg = "<?php echo __('Wijzigingen NIET opgeslagen!');?>";
        let color = "red";
        if (result && result.result === "OK") {
          msg = "<?php echo __('Wijzigingen succesvol opgeslagen');?>";
          color = "";

          //set new data
          let newvalue = data.value === 1 ? 0 : 1;
          current.data("value", newvalue);
          current.html(newvalue === 1 ? '<?php echo IconHelper::getCheckboxOff() ?>' : '<?php echo IconHelper::getCheckbox() ?>')
        }
        $('#message').html('<div class="message"' + (color !== "" ? 'style="color: ' + color + ';"' : '') + '>' + msg + '</div><br/>');
      });
    });

  });
</script>
<form method="post">
  <div class="list-filter-form">
    <input type="text" name="article_search" id="article_search" value="<?php echo escapeForInput($_SESSION['article_search']); ?>" placeholder="<?php echo __("Zoeken") ?>..."/>
    <?php if (defined('CATALOG_BRAND') && CATALOG_BRAND) : ?>
      <select name="article_brand">
        <option value=""><?php echo __('Selecteer merk...'); ?></option>
        <?php foreach ($brands as $brand) : ?>
          <option value="<?php echo $brand->id ?>" <?php writeIfSelectedVal($_SESSION['article_brand'], $brand->id); ?>><?php echo $brand->getName(); ?></option>
        <?php endforeach; ?>
      </select>
    <?php endif; ?>
    <input type="submit" class="gsd-btn gsd-btn-primary" name="go_search" value="<?php echo __("Zoeken") ?>"/>
    <a href="<?php echo reconstructQueryAdd(['pageId']) . 'action=edit'; ?>" class="gsd-btn gsd-btn-primary qtipa"
                                                title="<?php echo __('Nieuwe handmatige product toevoegen'); ?>"><?php echo __('Nieuw product container toevoegen'); ?></a>
  </div>
</form>
<?php $pager->writePreviousNext(); ?><br/>

<div id="message"></div>

<?php if (count($product_conts) == 0): ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else: ?>
  <table id="prod_cont" class=" default_table">
    <tr class="dataTableHeadingRow">
      <td><?php echo __('Container Code'); ?></td>
      <td><?php echo __('Naam'); ?></td>
      <?php if (defined('CATALOG_BRAND') && CATALOG_BRAND): ?>
        <td><?php echo __('Merk'); ?></td>
      <?php endif ?>
      <td><?php echo __('Categorie'); ?></td>
      <td><?php echo __('Online'); ?></td>
      <td class="actions"><?php echo __('Acties') ?></td>
    </tr>
    <?php foreach ($product_conts as $product_cont): ?>
      <tr class="dataTableRow trhover" id="product_<?php echo $product_cont->id ?>">
        <td><?php echo $product_cont->code; ?></td>
        <td><?php echo $product_cont->content->name; ?></td>
        <?php if (defined('CATALOG_BRAND') && CATALOG_BRAND): ?>
          <td><?php if (isset($brands[$product_cont->brand_id])) echo $brands[$product_cont->brand_id]->name_nl; ?></td>
        <?php endif; ?>
        <td><?php if (isset($categories[$product_cont->category1_id])) echo $categories[$product_cont->category1_id]->content->name; ?></td>
        <td>
          <a href="#" class="toggleactive qtipa" title="<?php echo __('Zet (in)actief'); ?>" style="text-align: center"
             data-type="productcont" data-value="<?php echo !$product_cont->online ? 1 : 0; ?>">
            <?php echo $product_cont->online == 1 ? IconHelper::getCheckbox() : IconHelper::getCheckboxOff() ?>
          </a>
        </td>
        <td class="actions">
          <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=edit&id=' . $product_cont->id) ?>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=productcontdelete&id=' . $product_cont->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>