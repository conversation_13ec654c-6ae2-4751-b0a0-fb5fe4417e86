<script type="text/javascript">

  initDelayKeyUp();

  function search() {
    $("#product-loader").show();
    $("#result").text("");

    let searchstr = $("#product_cont_search").val();
    if(searchstr.length <= 0) {
      $("#product-loader").hide();
      $("#result").html("");
      return;
    }

    $.getJSON("<?php echo PageMap::getUrl('M_PRODUCTCONT') ?>?action=search&val=" + searchstr,
      function (data) {
        ajaxresult = data;
        var str    = '';
        if (data.length > 0) {
          str = '<table class="default_table">';
          str += '<tr class="dataTableHeadingRow">';
          str += '<td>Artikelcode</td>';
          str += '<td>Naam</td>';
          str += '<td>Selecteer</td>';
          str += '</tr>';
          for (var key in data) {
            var product_container = data[key];
            str += '<tr class="dataTableRow">';
            str += '<td>' + product_container.code + '</td>';
            str += '<td>' + product_container.content.name + '</td>';
            str += '<td>' + '<a href="#" class="selectprod gsd-btn gsd-btn-primary gsd-btn-small" id="' + key + '">Selecteer</a>' + '</td>';
            str += '</tr>';
          }
          str += "</table>";
        }
        else {
          str = "<Br/>Geen items gevonden.";
        }
        $("#product-loader").hide();
        $("#result").html(str);

        $(".selectprod").on('click', function (event) {
          event.preventDefault();
          const product = ajaxresult[$(this).attr("id")];
          $(document).trigger("<?php echo $triggerName ?>", [product]);
        });

      }
    );
  }

  var ajaxresult = null;

  $(document).ready(function () {

    $("#product_cont_search").delayKeyup(search,200);

  });
</script>
<div style="width: 100%; height: 500px;">
  <input type="text" value="" name="product_cont_search" id="product_cont_search" autocomplete="off" placeholder="Zoek product container..." />
  <img src="/gsdfw/images/loading1.gif" id="product-loader" style="display: none;"/>
  <div id="result" style="height: calc(100% - 60px);overflow: auto;"></div>
</div>
