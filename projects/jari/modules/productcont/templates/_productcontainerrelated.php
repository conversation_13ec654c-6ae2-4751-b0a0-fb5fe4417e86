<script type="text/javascript">

  let gsdModalProductRelated = new GsdModal();
  gsdModalProductRelated.init();

  $(document).ready(function () {

    $("#select-related-product").on("click", function (e) {
      e.preventDefault();
      gsdModalProductRelated.open($(this).attr("data-url"), $(this).attr("data-title"));
    });

    $(".productreldelete").click(function (event) {
      event.preventDefault();
      var tr_id = $(this).attr("id");
      if (confirm("Weet u zeker dat u de gerelateerde productcontainer wilt verwijderen?")) {
        $.get('<?php echo reconstructQuery(['action']) ?>action=relateddelete&relprodid=' + tr_id, function (data) {
          alert(data);
          $("#tr_" + tr_id).hide();
        });
      }
    });

    $(document).on("gsdModalSelect", function (e, msg) {
      $("#related_product_container").val(msg.id);
      $('input[name="go"]').click();
    });

  });
</script>
<div class="list-filter-form">
  <input type="hidden" name="addrelproduct" id="related_product_container" value="" />
  <a href="#" id="select-related-product"
     data-url="<?php echo reconstructQueryAdd(['pageId']) ?>action=searchmodal&triggerName=gsdModalSelect"
     data-title="Toevoegen gerelateerde product container" class="gsd-btn gsd-btn-primary">
    Toevoegen gerelateerde product container
  </a>
</div>
<?php if(count($related_product_containers) == 0): ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else: ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Product naam</td>
      <td>Product code</td>
      <td>Verwijder</td>
    </tr>
    <?php foreach ($related_product_containers as $prr): ?>
      <tr class="dataTableRow" id="tr_<?php echo $prr->productcontrelated_id ?>">
        <td>
          <a
            href="<?php echo reconstructQueryAdd(['pageId']) ?>action=productedit&id=<?php echo $prr->id ?>"><?php echo $prr->getName($_SESSION['worklanguage']) ?></a>
        </td>
        <td>
          <a
            href="<?php echo reconstructQueryAdd(['pageId']) ?>action=productedit&id=<?php echo $prr->id ?>"><?php echo $prr->code ?></a>
        </td>
        <td>
          <a href="" title="Verwijder product" class="productreldelete" id="<?php echo $prr->productcontrelated_id ?>">
            <i class="material-icons">delete</i>
          </a>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>