<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1><?php echo (!$user || !$user->id) ? "Nieuwe klant" : "Wijzig uw gegevens"; ?></h1>
    </div>
  </section>
</div>

<div id="new-user">
  <div>
    <div>
      <form method="post" class="application-form" name="useredit" id="submit" autocomplete="off">
        <?php if (!$user || !$user->id): ?>
          <p>Als u een nieuwe klant bent, kunt u hier een account aanmaken.<br/>Vul hierbij uw woon of vestigingsgegevens in.</p>
        <?php endif; ?>
        <?php writeErrors($errors); ?>

        <strong>Persoonsgegevens</strong>

        <div class="form-row">
          <div class="form-group">
            <label for="firstname">Voornaam</label>
            <input class="form-input" type="text" name="firstname" id="firstname" value="<?php echo displayAsHtml($user->firstname) ?>" size="30" maxlength="50" placeholder="Voornaam" required/>
          </div>
          <div class="form-group">
            <label for="insertion">Tussenvoegsels</label>
            <input class="form-input" type="text" name="insertion" id="insertion" value="<?php echo displayAsHtml($user->insertion) ?>" size="30" maxlength="50" placeholder="Tussenvoegsels"/>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="lastname">Achternaam</label>
            <input class="form-input" type="text" name="lastname" id="lastname" value="<?php echo displayAsHtml($user->lastname) ?>" size="30" maxlength="50" placeholder="Achternaam" required/>
          </div>
          <div class="form-group">
            <label for="sex">Geslacht</label>
            <select class="form-input" name="sex" id="sex">
              <option value="">Kies geslacht ...</option>
              <option value="M" <?php writeIfSelectedVal($user->sex, 'M') ?>>Man</option>
              <option value="V" <?php writeIfSelectedVal($user->sex, 'V') ?>>Vrouw</option>
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="phone">Telefoonnummer</label>
            <input class="form-input" type="text" name="phone" id="phone" value="<?php echo displayAsHtml($user->phone) ?>" size="25" maxlength="20" placeholder="Telefoonnummer"/>
          </div>
          <div class="form-group">
            <label for="cellphone">Mobiel nummer</label>
            <input class="form-input" type="text" name="cellphone" id="cellphone" value="<?php echo displayAsHtml($user->cellphone) ?>" size="25" maxlength="20" placeholder="Mobiel nummer"/>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="address">Straat</label>
            <input class="form-input" id="address" type="text" name="address" value="<?php echo displayAsHtml($user->organisation->address) ?>" maxlength="50" placeholder="Straat"/>
          </div>
          <div class="form-group">
            <label for="number">Huisnummer</label>
            <input class="form-input" type="text" name="number" id="number" value="<?php echo displayAsHtml($user->organisation->number) ?>" maxlength="10" placeholder="Nr."/>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="zip">Postcode</label>
            <input class="form-input" type="text" name="zip" id="zip" value="<?php echo displayAsHtml($user->organisation->zip) ?>" maxlength="10" placeholder="Postcode"/>
          </div>
          <div class="form-group">
            <label for="city">Plaats</label>
            <input class="form-input" type="text" name="city" id="city" value="<?php echo displayAsHtml($user->organisation->city) ?>" maxlength="50" placeholder="Plaats"/>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="country">Land</label>
            <?php echo Country::getCountrySelect('country', $user->organisation, 'form-input') ?>
          </div>
          <div class="form-group">
            <label for="organ_name">Bedrijfsnaam</label>
            <input class="form-input" type="text" name="organ_name" id="organ_name" value="<?php echo $user->organisation->name ?>" maxlength="50" placeholder="Bedrijfsnaam"/>
          </div>
        </div>

        <br>
        <strong>Accountgegevens</strong>

        <div class="form-row">
          <div class="form-group">
            <label for="email">E-mailadres</label>
            <input class="form-input" type="text" name="email" id="email" value="<?php echo displayAsHtml($user->email) ?>" size="30" maxlength="50" placeholder="E-mailadres" required/>
          </div>
          <div class="form-group">
            <label for="email_confirm">E-mail (bevestig)</label>
            <input class="form-input" type="text" name="email_confirm" id="email_confirm" value="<?php echo displayAsHtml($user->email) ?>" size="30" maxlength="50" placeholder="E-mailadres" required/>
          </div>
        </div>
        <?php if ($user && $user->id): ?>
          <div class="form-group">
            <label for="passwordchange">Wachtwoord aanpassen</label>
            <input class="form-input" type="checkbox" name="passwordchange" id="passwordchange"/>
          </div>
        <?php else: ?>
          <input type="hidden" name="passwordchange" id="passwordchange" value="1"  />
        <?php endif; ?>
        <div class="form-row">
          <div class="form-group passwordrow">
            <label for="password1">Wachtwoord</label>
            <input class="form-input" type="password" name="password1" id="password1" maxlength="30" placeholder="Wachtwoord" required>
          </div>
          <div class="form-group passwordrow">
            <label for="password2">Wachtwoord (bevestig)</label>
            <input class="form-input" type="password" name="password2" id="password2" maxlength="30" placeholder="Wachtwoord" required>
          </div>
        </div>
        <div class="submit">
          <input class="btn-primary" type="submit" name="go" value="Meld u aan">
        </div>

        <p>Al een account? Log <a href="<?php echo PageMap::getUrl('M_BASKET') . '?action=pay0' ?>"><b>hier</b></a> in</p>
      </form>
    </div>
  </div>
</div>

<script type="text/javascript">
  $(document).ready(function(){
    <?php if($user->id!=""): ?>
      $("#passwordchange").on("click", function() {
        if($(this).is(":checked")) {
          $(".passwordrow").show()
        }
        else {
          $(".passwordrow").hide()
        }
      });
      <?php if(isset($_POST["passwordchange"])): ?>
        $("#passwordchange").trigger("click");
      <?php endif; ?>
    <?php endif; ?>
  });
</script>
<?php if($user->id!=""): ?>
  <style>
    .passwordrow {
      display: none;
    }
  </style>
<?php endif; ?>
