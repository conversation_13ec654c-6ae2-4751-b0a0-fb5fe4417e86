<?php

  namespace domain\productrange\service;

  use ArrayHelper;
  use CategoryContent;
  use DBConn;
  use Category;
  use <PERSON>bHelper;
  use Exception;
  use Pager;

  class GetCategories {

    /**
     * @var string
     */
    private $language;

    public function __construct(string $language) {
      $this->language = $language;
    }

    /**
     * @param Category $category
     * @param null $maxParentId
     * @return array
     * @throws Exception
     */
    public function getParentsOf(Category $category, $maxParentId = null): array {

      if (!is_object($category)) {
        throw new Exception('No category given');
      }

      $categories = [0 => $category];
      while ($categories[0]->parent_id != $maxParentId) {
        $getCategory = new GetCategory($categories[0]->parent_id);
        $categoryParent = $getCategory->getOnline();
        $categories = ArrayHelper::arrayInsert($categories, 0, $categoryParent);
      }

      return $categories;
    }

    /**
     * @param int $categoryId
     * @param Pager|null $pager
     * @return array
     * @throws \GsdException
     */
    public function getSubCategoriesOf(int $categoryId, ?Pager $pager): array {

      if (!$categoryId || !is_numeric($categoryId)) {
        throw new Exception('No category id given');
      }

      $query = "SELECT category.*, category_content.* FROM category ";
      $filterQuery = "JOIN category_content ON category_content.category_id = category.id ";
      $filterQuery .= "AND category_content.locale='" . escapeForDB($this->language) . "' ";
      $filterQuery .= 'WHERE category.parent_id = ' . $categoryId . ' ';
      $filterQuery .= 'AND category.online_custshop = 1 AND category.void = 0 ';
      $filterQuery .= 'ORDER BY category.sort ASC ';
      $query .= $filterQuery;

      if ($pager) {
        $count = "SELECT COUNT(category.id) AS count FROM category " . $filterQuery;
        $result = DBConn::db_link()->query($count);
        $row = $result->fetch_assoc();
        if ($row) $pager->count = $row['count'];
        $query .= $pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $categories = [];
      while ($row = $result->fetch_array()) {
        $columnCounter = 0;
        $category = (new Category())->hydrateNext($row, $columnCounter);
        $category->content = (new CategoryContent())->hydrateNext($row, $columnCounter);

        $categories[] = $category;
      }
      return $categories;
    }

    public function bySearch(string $searchValue): array {

      if (empty($searchValue)) return [];

      $searchValue = DbHelper::escape($searchValue);

      $filterQuery = "JOIN category_content ON category_content.category_id = category.id ";
      $filterQuery .= "WHERE category.online_custshop = 1 AND category.void = 0 ";
      $filterQuery .= "AND (";
      $filterQuery .= " category_content.name LIKE '%" . $searchValue . "%'";
      $filterQuery .= " OR category_content.description LIKE '%" . $searchValue . "%'";
      $filterQuery .= " OR category_content.keywords LIKE '%" . $searchValue . "%'";
      $filterQuery .= ")";

      return $this->getCategories($filterQuery);
    }

    /**
     * @return array
     * @throws \GsdException
     */
    public function getAllOnline(): array {
      $filterQuery = "JOIN category_content ON category_content.category_id = category.id ";
      $filterQuery .= "JOIN category layer2 ON layer2.parent_id = category.id ";
      $filterQuery .= "JOIN product_cont layer3 ON layer3.category1_id = layer2.id ";
      $filterQuery .= "WHERE category.online_custshop = 1 AND category.void = 0 AND category.parent_id IS NULL ";

      return $this->getCategories($filterQuery);
    }

    /**
     * @param string $filterQuery
     * @return array
     * @throws \GsdException
     */
    private function getCategories(string $filterQuery): array {
      $query = "SELECT category.*, category_content.* FROM category " . $filterQuery . " ";
      $query .= "GROUP BY category.id ";
      $query .= " ORDER BY category_content.name ";

      $categories = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $columnCounter = 0;
        $category = (new Category())->hydrateNext($row, $columnCounter);
        $category->content = (new CategoryContent())->hydrateNext($row, $columnCounter);

        $categories[] = $category;
      }

      return $categories;
    }

  }



