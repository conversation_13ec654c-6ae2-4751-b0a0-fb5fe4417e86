<?php

  namespace domain\productrange\service;

  use DBConn;
  use DbHelper;
  use Organisation;
  use Product;
  use ProductCont;
  use ProductContContent;
  use ProductContent;
  use ProductOption;

  class GetProducts {

    public function getOnline(ProductCont $productContainer): array {
      $query = "SELECT product.*, product_content.* ";
      foreach (ProductOption::CODES as $key => $value) {
        $query .= ", $key.* ";
      }
      $query .= "FROM product ";
      $query .= "JOIN product_content ON product_content.product_id = product.id ";
      $query .= "LEFT JOIN product_option ON product_option.product_id = product.id ";
      foreach (ProductOption::CODES as $key => $value) {
        $query .= "LEFT JOIN product_option AS $key ON $key.product_id = product.id AND $key.code = '$key' ";
      }
      $query .= "WHERE product.product_cont_id = " . DbHelper::escape($productContainer->id) . " ";
      $query .= "AND product.online_custshop = 1 ";
      $query .= "AND product.void = 0 ";
      $query .= "GROUP BY product.id ";
      $query .= "ORDER BY product.sort ASC ";

      $products = [];
      $result = DBConn::db_link()->query($query);

      while ($row = $result->fetch_row()) {
        $columnCounter = 0;
        $product = new Product()->hydrateNext($row, $columnCounter);
        $product->content = new ProductContent()->hydrateNext($row, $columnCounter);
        $product->options = [];
        foreach (ProductOption::CODES as $value) {
          $option = new ProductOption()->hydrateNext($row, $columnCounter);
          $option->name = $value;
          $product->options[] = $option;
        }
        $products[] = $product;
      }

      return $products;
    }

    public function getOrganisationProducts(ProductCont $product_container, Organisation $customer_organisation): array {
      $query = <<<SQL
       SELECT product.*, product_content.*, combined_stock_product.*, order_product_on_hold.* FROM product 
      JOIN product_content ON product_content.product_id = product.id 
      JOIN tw_organisation_product ON tw_organisation_product.product_id = product.id AND tw_organisation_product.organisation_id = $customer_organisation->id
      LEFT JOIN combined_stock_product ON combined_stock_product.product_id = product.id 
      LEFT JOIN order_product_on_hold ON order_product_on_hold.product_id = product.id 
      WHERE product.product_cont_id = $product_container->id
      AND product.void = 0 
      GROUP BY product.id 
      ORDER BY product.sort ASC 
      SQL;
      $products = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $product = new Product();
        $product->hydrate($row, $column_counter);
        $column_counter += count(Product::columns);

        $product->content = new ProductContent();
        $product->content->hydrate($row, $column_counter);


        $products[] = $product;
      }

      return $products;
    }

    public function getOnlineOnHome(): array {
      $filter_query = "";
      $filter_query .= "JOIN category ON category.online_custshop = 1 ";
      $filter_query .= "JOIN product_cont ON product.product_cont_id = product_cont.id ";
      $filter_query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filter_query .= "WHERE product.spotlight_custshop = 1 AND product_cont.online = 1 AND product.void = 0 ";
      $filter_query .= "GROUP BY product.id ";
      $filter_query .= "ORDER BY product_cont.sort ASC ";

      $query = "SELECT product_cont.*, product_cont_content.*, product.* FROM product " . $filter_query;

      $products = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $product_container = new ProductCont();
        $product_container->hydrate($row);

        $product_container_content = new ProductContContent();
        $product_container_content->hydrate($row, count(ProductCont::columns));
        $product_container->content = $product_container_content;

        $products[] = $product_container;
      }

      return $products;
    }

    public function getOptions(Product $product): array {
      return ProductOption::find_all_by(['product_id' => $product->id]);
    }
  }