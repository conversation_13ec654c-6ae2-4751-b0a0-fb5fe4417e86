<?php

  namespace domain\productrange\service;

  use Category;
  use CategoryContent;
  use Exception;

  class GetCategory {

    /**
     * @var int
     */
    private $category_id;

    public function __construct(int $categoryId) {
      $this->category_id = $categoryId;
    }

    public function getOnline($withContent = true): Category {
      $query = "SELECT category.*, category_content.* FROM category ";
      if ($withContent) $query .= "JOIN category_content ON category_content.category_id = category.id ";
      $query .= "WHERE category.id = '" . $this->category_id . "' ";

      $result = \DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $columnCounter = 0;
        $category = (new Category())->hydrateNext($row, $columnCounter);
        if ($withContent) $category->content = (new CategoryContent())->hydrateNext($row, $columnCounter);
      }
      if ($category == null || $category->void == 1 || $category->online_custshop != 1) {
        throw new Exception('Category not found');
      }

      return $category;
    }


    public function get(): Category {
      $category = Category::find_by_id($this->category_id);
      if ($category == null || $category->void == 1) {
        throw new Exception('Category not found');
      }

      return $category;
    }

    public function getContent(Category $category): CategoryContent {
      $categoryContent = CategoryContent::find_by(['category_id' => $category->id]);
      if (!$categoryContent) {
        throw new Exception('Category content not found');
      }

      return $categoryContent;
    }

  }



