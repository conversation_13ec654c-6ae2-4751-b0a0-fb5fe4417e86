<?php

  namespace domain\productrange\service;

  use GsdException;
  use Product;
  use ProductContent;

  class GetProduct {

    /**
     * @var int
     */
    private $product_id;

    public function __construct(int $productId) {
      $this->product_id = $productId;
    }

    public function get(): Product {
      $product = Product::find_by_id($this->product_id);
      if ($product === false) {
        throw new GsdException(sprintf('Product %s not found', $this->product_id));
      }
      if ($product->void == 1) {
        throw new GsdException(sprintf('Product %s not found', $product->code));
      }

      return $product;
    }

    public function getOnline(): Product {
      $product = Product::find_by_id($this->product_id);
      if ($product === false) {
        throw new GsdException(sprintf('Product %s not found', $this->product_id));
      }
      if ($product->void == 1 || $product->online_custshop == 0) {
        throw new GsdException(sprintf('Product %s not found', $product->code));
      }

      return $product;
    }

    public function getContent(): ProductContent {
      $productContent = ProductContent::find_by(['product_id' => $this->product_id]);
      if (!$productContent) {
        throw new GsdException('Product content not found');
      }

      return $productContent;
    }

  }