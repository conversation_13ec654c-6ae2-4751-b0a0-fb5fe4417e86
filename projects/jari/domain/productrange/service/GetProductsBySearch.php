<?php

  namespace domain\productrange\service;

  use DBConn;
  use DbHelper;
  use ProductCont;
  use ProductContContent;

  class GetProductsBySearch {

    public function getOnlineBySearchValue(string $search_value): array {

      if (empty($search_value)) return [];

      $filter_query = "";
      $filter_query .= "JOIN category ON category.id = product_cont.category1_id AND category.online_custshop = 1 ";
      $filter_query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filter_query .= "JOIN product ON product.product_cont_id = product_cont.id ";
      $filter_query .= "JOIN product_content ON product_content.product_id = product.id ";
      $filter_query .= "WHERE product_cont.online = 1 AND product.online_custshop = 1 ";

      $search_value = DbHelper::escape($search_value);
      $filter_query .= "AND ( ";
      $filter_query .= "product.code = '" . $search_value . "' "; // techwinkel artikel nr
      $filter_query .= "OR product.supplier_code = '" . $search_value . "' "; // leverancier artikel nr
      $filter_query .= "OR product_content.name LIKE '%" . $search_value . "%' ";
      $filter_query .= "OR product_cont.model LIKE '%" . $search_value . "%' "; // dinnr
      $filter_query .= "OR product_cont_content.name LIKE '%" . $search_value . "%' ";
      $filter_query .= "OR product_cont_content.alternate_name LIKE '%" . $search_value . "%' ";
      $filter_query .= "OR product_cont_content.material LIKE '%" . $search_value . "%' ";
      $filter_query .= "OR product_cont_content.keywords LIKE '%" . $search_value . "%' ";
      $filter_query .= " ) ";

      $query = "SELECT product_cont.*, product_cont_content.* FROM product_cont " . $filter_query . " ";
      $query .= "GROUP BY product_cont.id ";

      $products = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $product_container = new ProductCont();
        $product_container->hydrate($row);

        $product_container_content = new ProductContContent();
        $product_container_content->hydrate($row, count(ProductCont::columns));
        $product_container->content = $product_container_content;

        $products[] = $product_container;
      }

      return $products;
    }

  }