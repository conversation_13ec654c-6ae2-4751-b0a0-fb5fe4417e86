<?php

  namespace domain\productrange\service;

  use GsdException;
  use ProductCont;
  use ProductContContent;
  use ProductContOption;
  use ProductContRelated;

  class GetProductContainer {

    /**
     * @var int
     */
    private $product_container_id;

    public function __construct(int $productContainerId) {
      $this->product_container_id = $productContainerId;
    }

    public function get(): ProductCont {
      $productContainer = ProductCont::find_by_id($this->product_container_id);
      if ($productContainer === false) {
        throw new GsdException(sprintf('Product %s not found', $this->product_container_id));
      }

      return $productContainer;
    }

    public function getOnline(): ProductCont {
      $productContainer = ProductCont::find_by_id($this->product_container_id);
      if ($productContainer === false) {
        throw new GsdException(sprintf('Product %s not found', $this->product_container_id));
      }
      if ($productContainer->online == 0) {
        throw new GsdException(sprintf('Product %s not found', $productContainer->code));
      }

      return $productContainer;
    }

    public function getContent(): ProductContContent {
      $productContainerContent = ProductContContent::find_by(['product_cont_id' => $this->product_container_id]);
      if (!$productContainerContent) {
        throw new GsdException('Product content not found');
      }

      return $productContainerContent;
    }

    public function getOptions(): array {
      $options = ProductContOption::find_all_by(['product_cont_id' => $this->product_container_id]);
      foreach ($options as $option) {
        $option->name = ProductContOption::CODES[$option->code];
      }
      return $options;
    }

    public function getRelated(): array {
      return ProductContRelated::getRelatedOf($this->product_container_id);
    }
  }