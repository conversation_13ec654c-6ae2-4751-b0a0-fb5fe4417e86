<?php

  namespace domain\productrange\collection;

  use ArrayHelper;
  use DBConn;
  use DbHelper;
  use ProductCont;
  use ProductContContent;

  class ProductContainers {

    private $query = '';

    public function selectWithContent(): void {
      $this->query = "SELECT product_cont.*, product_cont_content.* FROM product_cont ";
    }

    public function joinCategory(): void {
      $this->query .= "JOIN category ON (category.id = product_cont.category1_id ";
      $this->query .= "OR category.id = product_cont.category2_id ";
      $this->query .= "OR category.id = product_cont.category3_id) ";
    }

    public function filterCategory(?array $category_ids): void {
      if ($category_ids === null) {
        // root
        $this->query .= "AND category.id IS NULL ";
      }
      else {
        $this->query .= "AND category.id IN(" . ArrayHelper::toQueryString($category_ids) . ") ";
      }
    }

    public function joinContent() {
      $this->query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
    }

    public function whereNotRemoved() {
      $this->query .= "WHERE category.void=0 ";
    }

    public function filterSearch(string $search_value) {
      if (empty(trim($search_value))) return;

      $search_value = DbHelper::escape($search_value);
      $this->query .= " AND (";
      $this->query .= "code LIKE '%" . $search_value . "%' ";
      $this->query .= "OR product_cont_content.name LIKE '%" . $search_value . "%'";
      $this->query .= "OR product_cont_content.keywords LIKE '%" . $search_value . "%'";
      $this->query .= " )";
    }

    public function orderBy(string $column, string $order) {
      $this->query .= "ORDER BY " . DbHelper::escape($column) . " " . DbHelper::escape($order) . " ";
    }

    public function retrieveWithContent(): array {
      $product_containers = [];
      $result = DBConn::db_link()->query($this->query);
      while ($row = $result->fetch_row()) {
        $product_container = new ProductCont();
        $product_container->hydrate($row);

        $product_container_content = new ProductContContent();
        $product_container_content->hydrate($row, count(ProductCont::columns));
        $product_container->content = $product_container_content;

        $product_containers[] = $product_container;
      }

      return $product_containers;
    }


  }