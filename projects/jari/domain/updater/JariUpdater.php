<?php

  namespace domain\updater;

  use Gsd\Updater\GsdUpdater;

  /**
   * Class JariUpdater
   * Updaten van specifieke changes.
   * @package domain\updater
   */
  class JariUpdater extends GsdUpdater {

    protected function execute2(): bool {
      $query = <<<SQL
        CREATE TABLE `jari_product_uid` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `product_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `abo_uid` VARCHAR(18) NOT NULL UNIQUE,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `product_id` (`product_id`),
          CONSTRAINT `fk_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
      $this->executeQuery($query);

      return true;
    }

    /**
     * 16-04-2025 - Jim - Link uid from ABO to category and to product container
     * @return bool
     */
    protected function execute1(): bool {
      $categoryquery = <<<SQL
        CREATE TABLE `jari_category_uid` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `category_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `abo_uid` VARCHAR(18) NOT NULL UNIQUE,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `category_id` (`category_id`),
          CONSTRAINT `fk_category_id` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
      $this->executeQuery($categoryquery);

      $containerquery = <<<SQL
        CREATE TABLE `jari_product_container_uid` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `product_container_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `abo_uid` VARCHAR(18) NOT NULL UNIQUE,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `product_container_id` (`product_container_id`),
          CONSTRAINT `fk_product_container_id` FOREIGN KEY (`product_container_id`) REFERENCES `product_cont` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
      $this->executeQuery($containerquery);

      return true;
    }

//    /**
//     * [dd-mm-yyyy] - [Username] - [Description]
//     * @return bool
//     */
//    protected function execute123(): bool {
//      // run code
//      return true; // return true for succesfull increment
//    }

    public function __construct() {
      $this->setVersionCode(PROJECT . "-version");
    }

  }