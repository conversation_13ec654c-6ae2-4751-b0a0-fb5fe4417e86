<?php

  namespace domain\contact\service;

  use gsdfw\domain\mail\service\WrapperService;
  use GsdMailer;
  use MessageFlashCoordinator;
  use ResponseHelper;
  use StringHelper;
  use ValidationHelper;

  class ContactService {
    public static function handleContactSubmission($subject, $input, $file = null): array {
      ValidationHelper::isSpamFreePost();

      $input = self::sanitizeInput($input);
      $errors = self::validateInput($input, $file);

      if (empty($errors)) {
        self::sendContactEmail($subject, $input, $file);
        ResponseHelper::redirectMessage(
          __("Uw aanvraag is ontvangen.<br/>Wij nemen zo spoedig mogelijk contact met u op."),
          reconstructQuery()
        );
      }

      MessageFlashCoordinator::addMessageAlert("Er zijn foutmeldingen opgetreden, controleer uw invoer");

      return $errors;
    }

    private static function sanitizeInput(array $input, ): array {
      return [
        'name' => htmlspecialchars($input['name'] ?? ''),
        'company' => htmlspecialchars($input['company'] ?? ''),
        'phone' => htmlspecialchars($input['phone'] ?? ''),
        'email' => htmlspecialchars($input['email'] ?? ''),
        'question' => htmlspecialchars($input['question'] ?? ''),
        'terms' => isset($input['terms']) ? 1 : 0
      ];
    }

    private static function validateInput(array $input, $file): array {
      $errors = [];

      if (empty($input['name'])) $errors['name'] = 'Naam is verplicht.';
      if (empty($input['company'])) $errors['company'] = 'Bedrijf is verplicht.';
      if (empty($input['question'])) $errors['question'] = 'Vraag/opmerking is verplicht.';
      if ($input['terms'] != 1) $errors['terms'] = 'U moet akkoord gaan met de Privacy Policy.';
      if (empty($input['email']) || !ValidationHelper::isEmail($input['email'])) {
        $errors['email'] = 'E-mailadres is ongeldig.';
      }
      if (!empty($input['phone']) && !self::isValidPhoneNumber($input['phone'])) {
        $errors['phone'] = 'Telefoonnummer is ongeldig.';
      }

      // validate file if provided
      if ($file) self::validateFile($file, $errors);

      return $errors;
    }

    private static function isValidPhoneNumber($number): bool {
      // remove any spaces, dashes etc. before validating
      $number = StringHelper::cleanPhone($number);
      return preg_match('/^[+() 0-9.-]+$/', $number);
    }

    private static function validateFile(array $file, $errors): void {
      if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors['file'] = 'Bestand uploaden mislukt.';
      } elseif ($file['size'] > 5 * 1024 * 1024) { // 5 MB
        $errors['file'] = 'Bestandsgrootte mag maximaal 5 MB zijn.';
      } elseif (!in_array($file['type'], ['application/pdf', 'image/jpeg', 'image/png'])) {
        $errors['file'] = 'Ongeldig bestandstype. Alleen PDF, JPG en PNG zijn toegestaan.';
      }
    }

    private static function sendContactEmail($subject, array $input, $file): void {
      $fields = [
        'Naam' => $input['name'],
        'Bedrijf' => $input['company'],
        'E-mailadres' => $input['email'],
        'Vraag/opmerking' => $input['question']
      ];
      if (!empty($input['phone'])) {
        $fields['Telefoon'] = $input['phone'];
      }

      $message = self::buildEmailMessage($fields);
      $gsdMailer = GsdMailer::build(MAIL_FROM, $subject, $message);

      // Add file attachment if provided
      if ($file) $gsdMailer->addFile($file['tmp_name'], $file['name']);

      $gsdMailer->setReplyTo($input['email']);
      $gsdMailer->send();
    }

    private static function buildEmailMessage(array $fields): string {
      $message = '<table style="text-align: left">';
      foreach ($fields as $label => $value) {
        $message .= "<tr><td><strong>$label</strong></td><td>$value</td></tr>";
      }
      $message .= '</table>';

      // Add email template
      $wrapperService = new WrapperService($message);
      $message = $wrapperService->wrap();
      return str_replace("{domain}", $_SERVER['HTTP_HOST'], $message);
    }
  }