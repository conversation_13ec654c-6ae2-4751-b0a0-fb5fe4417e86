<?php

  namespace domain\aboservice\service;

  use domain\aboservice\exception\AboserviceException;
  use GsdDbException;
  use GsdException;
  use Setting;

  class AboserviceCredentials {

    protected Setting $username;
    protected Setting $password;
    protected Setting $token;
    protected Setting $token_date_time;

    /**
     * @throws AboserviceException
     */
    public function __construct() {
      $username = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-username');
      if (empty($username->value)) {
        throw new AboserviceException("Gebruikersnaam is leeg. Stel deze in via de beheeromgeving.");
      }
      $this->username = $username;

      $password = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-password');
      if (empty($password->value)) {
        throw new AboserviceException("Wachtwoord is leeg. Stel deze in via de beheeromgeving.");
      }
      $this->password = $password;

      $token = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-token');
      if (!$token) $token = new Setting(['type' => 'ABOSERVICE', 'code' => 'aboservice-token']);
      $this->token = $token;

      $tokenDatetime = Setting::getByTypeAndCode('ABOSERVICE', 'aboservice-token-datetime');
      if (!$tokenDatetime) $tokenDatetime = new Setting(['type' => 'ABOSERVICE', 'code' => 'aboservice-token-datetime']);
      $this->token_date_time = $tokenDatetime;
    }

    /**
     * @return string
     */
    public function getUsername(): string {
      return $this->username->value;
    }

    /**
     * @return string
     */
    public function getPassword(): string {
      return $this->password->value;
    }

    /**
     * @return string
     */
    public function getToken(): string {
      return $this->token->value;
    }

    /**
     * @param string $token
     * @return void
     * @throws GsdDbException
     * @throws GsdException
     */
    public function setToken(string $token): void {
      $this->token->value = $token;
      $this->token->save();
    }

    /**
     * @return string
     */
    public function getTokenDateTime(): string {
      return $this->token_date_time->value;
    }

    /**
     * @return void
     * @throws GsdDbException
     * @throws GsdException
     */
    public function setTokenDateTime(): void {
      $this->token_date_time->value = date('Y-m-d H:i:s');
      $this->token_date_time->save();
    }

  }
