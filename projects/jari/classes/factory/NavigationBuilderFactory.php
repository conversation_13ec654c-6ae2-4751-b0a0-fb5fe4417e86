<?php

  class NavigationBuilderFactory extends NavigationBuilder {

    public static function buildFrontend($lang = false) {
      parent::buildFrontend($lang);
      $nav = Navigation::getInstance();
      $nav->addPMItem('M_BASKET', 1, false);
    }

    public static function buildBackend() {
      parent::buildBackend();
      (Navigation::getInstance())->addPMItem('M_ABOSERVICE', 'M_TOP', true, 'M_WEBSITE');
    }

  }