<?php

  AppModel::loadModelClass('ProductContModel');

  class ProductCont extends ProductContModel {

    /**
     * @param $uid
     * @return ProductCont|null
     * @throws GsdException
     */
    public static function getByUid($uid) {
      $query = "SELECT product_cont.* FROM product_cont ";
      $query .= "JOIN jari_product_container_uid ON jari_product_container_uid.product_container_id = product_cont.id AND jari_product_container_uid.abo_uid = '$uid'";
      $result = DBConn::db_link()->query($query);
      $data = $result->fetch_row();

      return ($data === null) ? null : (new self())->hydrateNext($data);
    }

    /**
     * @param $data
     * @param $uid
     * @param $categoryId
     * @return ProductCont|null
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function createFromAboGroupData($data, $uid, $categoryId): ?ProductCont {
      if (self::getByUid($uid)) return null;

      $container = (new self([
        'category1_id' => $categoryId,
        'code' => StringHelper::slugify($data['omschr']),
      ]));
      $container->save();

      $content = new ProductContContent([
        'product_cont_id'             => $container->id,
        'locale'                      => 'nl',
        'name'                        => ucfirst($data['omschr']),
      ]);
      $content->buildDefaultUrl($container->category1_id);
      $content->buildUrlUnique();
      $content->save();

      $container->content = $content;

      $container->uid = new JariProductContainerUid([
        'product_container_id' => $container->id,
        'abo_uid' => $uid,
      ]);
      $container->uid->save();

      $normData = trim($data['din'] ?? ''); // zowel din als iso worden onder 'din' ingeschoten vanuit de api - moet norm zijn
      if (!empty($normData)) {
        ProductContOption::createNewNormOption($container->id, $normData);
      }

      $kwaliteitData = trim($data['kwaliteit'] ?? '');
      if (!empty($kwaliteitData)) {
        ProductContOption::createNewKwaliteitOption($container->id, $kwaliteitData);
      }

      $behandelingData = trim($data['behandel'] ?? '');
      if (!empty($behandelingData)) {
        ProductContOption::createNewBehandelingOption($container->id, $behandelingData);
      }

      return $container;
    }

  }