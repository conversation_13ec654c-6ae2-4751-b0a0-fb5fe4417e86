<?php

  AppModel::loadModelClass('ProductContOptionModel');

  class ProductContOption extends ProductContOptionModel {

    const string CODE_NORM = 'norm';
    const string CODE_KWALITEIT = 'kwaliteit';
    const string CODE_BEHANDELING = 'behandeling';

    const array CODES = [
      self::CODE_NORM       => 'Norm',
      self::CODE_KWALITEIT => 'Kwaliteit',
      self::CODE_BEHANDELING      => 'Behandeling',
    ];

    /**
     * @param int $productContId
     * @param mixed $value
     * @return mysqli_result|bool
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function createNewNormOption(int $productContId, mixed $value): mysqli_result|bool {
      $option = new self([
        'product_cont_id' => $productContId,
        'code' => self::CODE_NORM,
        'value' => $value,
      ]);

      return $option->save();
    }

    /**
     * @param int $productContId
     * @param mixed $value
     * @return mysqli_result|bool
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function createNewKwaliteitOption(int $productContId, mixed $value): mysqli_result|bool {
      $option = new self([
        'product_cont_id' => $productContId,
        'code' => self::CODE_KWALITEIT,
        'value' => $value,
      ]);

      return $option->save();
    }

    /**
     * @param int $productContId
     * @param mixed $value
     * @return mysqli_result|bool
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function createNewBehandelingOption(int $productContId, mixed $value): mysqli_result|bool {
      $option = new self([
        'product_cont_id' => $productContId,
        'code' => self::CODE_BEHANDELING,
        'value' => $value,
      ]);

      return $option->save();
    }

  }