<?php

  AppModel::loadModelClass('ProductOptionModel');

  class ProductOption extends ProductOptionModel {
    const string CODE_DIAMETER = 'diameter';
    const string CODE_LENGTH = 'length';

    const array CODES = [
      self::CODE_DIAMETER => 'Diameter',
      self::CODE_LENGTH => 'Lengte'
    ];

    /**
     * @param $productid
     * @return array
     */
    public static function getOptions($productid): array {
      $options = [];
      foreach (self::CODES as $code) {
        $options[] = match($code) {
          self::CODE_DIAMETER => self::getDiameterOption($productid),
          self::CODE_LENGTH => self::getLengthOption($productid),
        };
      }

      return $options;
    }

    /**
     * @param $productid
     * @param $code
     * @return false|ProductOption|null
     */
    public static function getOption($productid, $code) {
      return match ($code) {
        self::CODE_DIAMETER => self::getDiameterOption($productid),
        self::CODE_LENGTH => self::getLengthOption($productid),
        default => null,
      };
    }

    /**
     * @param int $productId
     * @param mixed $value
     * @return mysqli_result|bool
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function createNewDiameterOption(int $productId, mixed $value): mysqli_result|bool {
      $option = new self([
        'product_id' => $productId,
        'code' => self::CODE_DIAMETER,
        'value' => $value,
      ]);

      return $option->save();
    }

    /**
     * @param int $productId
     * @return ProductOption|false
     */
    public static function getDiameterOption(int $productId): ProductOption|false {
      return self::find_by(['product_id' => $productId, 'code' => self::CODE_DIAMETER]);
    }

    public static function createNewLengthOption(int $productId, mixed $value): mysqli_result|bool {
      $option = new self([
        'product_id' => $productId,
        'code' => self::CODE_LENGTH,
        'value' => $value,
      ]);

      return $option->save();
    }

    /**
     * @param int $productId
     * @return ProductOption|false
     */
    public static function getLengthOption(int $productId): ProductOption|false {
      return self::find_by(['product_id' => $productId, 'code' => self::CODE_LENGTH]);
    }
  }