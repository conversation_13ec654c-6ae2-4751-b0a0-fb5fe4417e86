<?php

AppModel::loadModelClass('ProductContRelatedModel');

class ProductContRelated extends ProductContRelatedModel {

  public static function getRelatedOf(int $product_container_id): array {
    $product_containers = [];
    $query = "SELECT product_cont_related.id, product_cont.*, product_cont_content.*, related_product_cont.*, related_product_cont_content.* FROM product_cont_related  ";
    $query .= "JOIN product_cont ON product_cont.id = product_cont_related.product_cont_id ";
    $query .= "JOIN product_cont_content ON product_cont.id = product_cont_content.product_cont_id ";
    $query .= "JOIN product_cont as related_product_cont ON related_product_cont.id = related_product_cont_id ";
    $query .= "JOIN product_cont_content as related_product_cont_content ON related_product_cont.id = related_product_cont_content.product_cont_id ";
    $query .= "WHERE (product_cont_related.product_cont_id =" . DbHelper::escape($product_container_id) . " OR related_product_cont_id =" . DbHelper::escape($product_container_id) . ") ";
    $result = DBConn::db_link()->query($query);
    while ($row = $result->fetch_row()) {
      $column_counter = 1;
      $product_container = new ProductCont();
      $product_container->hydrate($row, $column_counter);
      $product_container->productcontrelated_id = $row[0];
      $column_counter += count(ProductCont::columns);

      $product_container->content = new ProductContContent();
      $product_container->content->hydrate($row, $column_counter);
      $column_counter += count(ProductContContent::columns);

      $product_containers[$product_container->id] = $product_container;

      $related_product_container = new ProductCont();
      $related_product_container->hydrate($row, $column_counter);
      $related_product_container->productcontrelated_id = $row[0];
      $column_counter += count(ProductCont::columns);

      $related_product_container->content = new ProductContContent();
      $related_product_container->content->hydrate($row, $column_counter);
      $column_counter += count(ProductContContent::columns);

      $product_containers[$related_product_container->id] = $related_product_container;
    }
    unset($product_containers[$product_container_id]); //remove this product
    usort($product_containers, 'Product::sortByName');

    return $product_containers;
  }

  public static function getUnique($product_container_id, $related_product_container_id) {
    return ProductContRelated::find_by([], 'WHERE (product_cont_id=' . $product_container_id . ' AND related_product_cont_id=' . $related_product_container_id . ') OR (product_cont_id=' . $related_product_container_id . ' AND related_product_cont_id=' . $product_container_id . ')');
  }

  public static function isUnique($product_container_id, $related_product_container_id) {
    if (ProductContRelated::getUnique($product_container_id, $related_product_container_id)) {
      return false;
    }

    return true;
  }

}