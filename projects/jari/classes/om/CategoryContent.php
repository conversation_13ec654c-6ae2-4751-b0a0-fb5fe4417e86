<?php

  AppModel::loadModelClass('CategoryContentModel');

  class CategoryContent extends CategoryContentModel {

    /**
     * Build a default seo friendly url. Could be extended on project level
     * @param $category
     */
    public function buildDefaultUrl($category) {
      $category->content = $this;
      $parents = Category::getParents($category, null, $this->locale);
      foreach ($parents as $parent) {
        $catnames[] = StringHelper::slugify(trim($parent->content->name));
      }

      $url = "/" . $this->locale . "/";
      $url .= implode("/", $catnames);

      $this->url = $url;
    }

  }