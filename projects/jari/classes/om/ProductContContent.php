<?php

  AppModel::loadModelClass('ProductContContentModel');

  class ProductContContent extends ProductContContentModel {

    /**
     * Build a default seo friendly url
     */
    public function buildDefaultUrl($categoryId = false): void {

      // if no category id is given, take the one from the product container
      if ($categoryId === false) {
        $productContainer = \ProductCont::find_by_id($this->product_cont_id);
        if (!$productContainer) {
          return;
        }
        $categoryId = $productContainer->category1_id;
      }

      // get the url from the main category
      $mainCategory = CategoryContent::find_by(['category_id' => $categoryId]);
      $category_url = '/';
      if ($mainCategory) {
        $category_url = $mainCategory->url;
      }

      $material = trim($this->material ?? '') == '' ? '' : trim($this->material);
      $url = $category_url . '/' . StringHelper::slugify(trim($this->name) . $material);
      $this->url = $url;
    }

  }
