<?php

  const PROJECT = 'jari';

  const DIR_PROJECT_FOLDER = DIR_ROOT . 'projects/' . PROJECT . '/';
  const URL_PROJECT_FOLDER = '/projects/' . PROJECT . '/';
  const URL_UPLOADS = '/uploads/' . PROJECT . '/';
  const DIR_UPLOADS = DIR_ROOT . 'uploads/' . PROJECT . '/';
  const URL_TEMP = '/temp/';
  const DIR_TEMP = DIR_ROOT . 'temp/';
  const DIR_ROOT_GSDFW = DIR_ROOT . 'gsdfw/';
  const DIR_CLASSES = DIR_ROOT_GSDFW . 'includes/classes/';
  const URL_INCLUDES = '/gsdfw/includes/';
  const DIR_INCLUDES = DIR_ROOT_GSDFW . 'includes/';
  const DIR_MODULES = DIR_ROOT_GSDFW . 'modules/';
  const CONFIGURE_ADDPRODUCTS_IN_ROOTDIR = false; //Voeg producten toe aan de root.
  const CONFIGURE_MAX_DEPTH_NESTED_CATS = 4; //Maximaal genestte niveau van categorieën.
  const CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS = false; //Hetzelfde product onder meerdere categorieën hangen.

  const DIR_LANGUAGES = DIR_ROOT_GSDFW . 'includes/languages/';
  const DIR_LANGUAGES_PROJECT = DIR_PROJECT_FOLDER . 'languages/';
  const LOG_FOLDER = DIR_ROOT . "logs/";
  const LOGLEVEL = "DEBUG";//DEBUG is only active string at this moment


  const HASH_STRING = '#34tkdf';
  const ADMIN_DEFAULT_ID = 2;

  const DIR_UPLOAD_CAT = DIR_UPLOADS . 'images/catalog/';
  const URL_UPLOAD_CAT = URL_UPLOADS . 'images/catalog/';
  const DIR_UPLOAD_BRAND = DIR_UPLOADS . 'images/brands/';
  const URL_UPLOAD_BRAND = URL_UPLOADS . 'images/brands/';
  const IMAGES_THUMB_WIDTH = 170; //product image
  const IMAGES_THUMB_HEIGHT = 170; //product image
  const IMAGES_ORIG_WIDTH = 750; //product image
  const IMAGES_ORIG_HEIGHT = 550; //product image
  const IMAGES_BRAND_THUMB_WIDTH = 170; //brand image
  const IMAGES_BRAND_THUMB_HEIGHT = 100; //brand image
  const IMAGES_BRAND_ORIG_WIDTH = 700; //brand image
  const IMAGES_BRAND_ORIG_HEIGHT = 600; //brand image
  const IMAGES_CAT_ORIG_RESIZE = false; //cat image
  const IMAGES_CAT_THUMB_WIDTH = 217; //cat image
  const IMAGES_CAT_THUMB_HEIGHT = 160; //cat image
  const IMAGES_CAT_ORIG_WIDTH = 690; //cat image
  const IMAGES_CAT_ORIG_HEIGHT = 280; //cat image
  const IMAGES_PAGE_THUMB_WIDTH = 180; //page image
  const IMAGES_PAGE_THUMB_HEIGHT = 180; //page image
  const IMAGES_PAGE_PREVIEW_WIDTH = 700; //page image
  const IMAGES_PAGE_PREVIEW_HEIGHT = 600; //page image
  const IMAGES_PAGE_THUMBPREVIEW_WIDTH = 600; //page image
  const IMAGES_PAGE_THUMBPREVIEW_HEIGHT = 159; //page image
  const DIR_UPLOAD_PDF = DIR_UPLOADS . 'pdf/';
  const URL_UPLOAD_PDF = URL_UPLOADS . 'pdf/';

  const DIR_SITE = DIR_PROJECT_FOLDER . 'sites/';
  const URL_SITE = URL_PROJECT_FOLDER . 'sites/';
  const DIR_TEMPLATE = DIR_PROJECT_FOLDER . 'templates/';
  const URL_TEMPLATE = URL_PROJECT_FOLDER . 'templates/';
  const DIR_UPLOADS_SITE = DIR_UPLOADS . 'sites/';
  const URL_UPLOADS_SITE = URL_UPLOADS . 'sites/';

  //pagina beheer

  const DISABLE_1STLEVEL_CREATENEW = false;
  const DISABLE_1STLEVEL_EDIT = false;
  const DISABLE_1STLEVEL_DELETE = false;
  const DISABLE_1STLEVEL_MOVE = false;

  const PAGEIMAGES_GALLERY = true;

  Config::set("PAGE_EDIT_CONTENT_2", true);
  Config::set("CATEGORY_EDIT_CONTENT_2", true);
  Config::set("PRODUCTCONT_EDIT_CONTENT_2", true);

  const SEOTITLE = true;
  const SEODESCRIPTION = true;
  const TAGS = true;
  const TAGS_PRODUCT = true;

  const NROFNAVIGATIONLEVELS = 3;
  const IMAGES_EXIF = false; //retrieve lat/long of image
  const STOCK_ENABLED = false; // Gebruikt voorraad syteem + controles.

  Config::set("STOCK_LOCATION_MULTI", false); //meerder productlocaties mogelijk
  const CALCULATE_SHIPPING_CAT = false; //Verzendkosten codes, bijv: A = €5, B = €7,50, etc.
  Config::set('BASKET_PICKUP_ENABLED', true); //Maak afhalen beschikbaar
  const CATALOG_BUY_DISCOUNT = false; //inkoopkorting aangeven
  Config::set("PRODUCT_YOUTUBE", false);
  Config::set("PRODUCT_SPOTLIGHT", false);

  Config::set("buy_discount_codes", ['A' => 0, 'B' => 20, 'C' => 45, 'D' => 48, 'E' => 49.6]); //Inkoop kortingscodes

  Config::set('BASKET_SHIPPING_METHODS', [
    'STANDARD' => 0.00,
    'PICKUP'   => 0.00,
    'REMBOURS' => 19.835,
  ]); //verzend types
  Config::set('CONFIGURE_ADD_PRODUCT_WITHOUT_PRICE', true); //28-05-2018 - PAUL - Product hoeft geen prijs te hebben.

  Config::set("catalog_languages", ['nl']); //languages for catalog/product descriptions
  Config::set("organisation_types_usg", [
    'ADMIN' => ['BEDRIJF'],
  ]); //welke organisatie.types mag een usergroup aanmaken [USER.USERGROUP=>[ORGANISATION.TYPE,ORGANISATION.TYPE,...]]
  Config::set("usergroups_for_usg", [
    'ADMIN' => ['BEDRIJF'],
  ]); //welke gebruikersgroepen mag een bepaalde gebruikersgroep zien/bewerken [USER.USERGROUP=>[USER.USERGROUP,USER.USERGROUP,...]]

  const CATALOG_BRAND = false; //producten aan merken koppelen
  Config::set('CATALOG_PRODUCT_BTWGROUP', [
    'nl' => [1 => 9, 2 => 21],
    'be' => [1 => 21, 2 => 21],
  ]); //producten aan btwgroep kunnen koppelen
  Config::set('USER_SHOW_NEWSLETTER', true); //nieuwsbrief kunnen aanmelden
  Config::set('CATALOG_PRODUCT_OPTIONS', true); //producten kunnen opties hebben. bijv kleuren bij t-shirt

  const PAYMENT_OVERMAKEN_PART_ENABLED = true; //overmaken aan voor particulier
  const CREDITS_ENABLED = false; //gebruik credit systeem
  Config::set('PAGES_MAX_PAGE_COUNT', [
    'free'     => 0,
    'standard' => 100,
    'large'    => 100,
  ]); //maximale aantal pagina's aan te maken per abo
  Config::set('ORGANISATION_EMAIL_OBLIDGED', false); //organisatie is verplicht emailadres in te vullen
  Config::set('INVOICE_TYPE_DEFAULT', 'invoice'); //standaard factuurtype bij nieuwe factuur



  Config::set("PAGES_FRONTEND_IMAGES_PER_ROW", 4); //aantal beelden per rij
  Config::set('CATALOG_BRAND_OBLIDGED', false); //merk verplicht invoeren bij product bewerken
  Config::set('FRONTEND_PAGER_SIZE', 24); //aantal producten per pagina
  Config::set('INVOICE_TYPE_DEFAULT', 'invoice'); //standaard factuurtype bij nieuwe factuur
  Config::set('ORGANISATION_NAME_OBLIDGED', false); //organisatie is verplicht bedrijfsnaam in te vullen

  Config::set('SHIPPING_FREE_AMOUNT', [
    'nl' => ['A' => 150.00, 'B' => 500.00],
    'be' => ['A' => 300.00, 'B' => 500.00],
    'de' => ['A' => 900.00, 'B' => false],
    'fr' => ['A' => 900.00, 'B' => false],
  ]); //Geen verzendkosten vanaf
  Config::set('PRODUCT_SELL_SIZE', false); //Verkoop aantal
  Config::set('ORDER_USE_EXISTING_CUST', false); //vlaggetje bestaande klant
  Config::set('ORDER_BESTELBON', true); //toon bestelbon


  Config::set('ORDER_USE_EMAILCUST', true); //mag klant mailen
  Config::set('CATALOG_PRODUCT_RELATEDPRODUCTS', true); //product heeft gerelateerde producten

  Config::set('PRODUCT_RESELLING', false); //Producten kunnen gereselled worden.
  Config::set("PRODUCT_USE_BRUTO_PRICE", false); //Men kan adviesprijs (bruto prijs) invoeren bij product.

  Config::set('PAGE_CKEDITOR_TOOLBAR', 'Full'); //toolbar to load ckeditor
  Config::set('PAGE_CKEDITOR_FILEBROWSER', true); //use ckeditor filebrowser
  Config::set('PAGE_IMAGES_NOT_RESIZE_PAGEIDS', ['site_home']); //paginaids van pagina's waarvan we de large formaat niet aanpassen. Bijv fotoslider pagina
  Config::set('PAGE_IMAGES_CKEDITOR_PAGEIDS', null); //paginaids van pagina's waarvan we de textarea vervangen door de ckeditor
  Config::set('PAGES_FILES_AUTHORIZE', false); //enable authorization flag bij bestanden
  Config::set('ORGANISATION_LANGUAGES', [
    'nl' => 'Nederlands',
  ]); //definieer selecteerbare talen in organisation edit.

  Config::set('PAGE_IMAGES_CROPPING_TOOL', 'ALL'); //paginaids van pagina's waarvan we de beelden kunnen croppen middels de croppingtool.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_TO_RESIZE', ['ORIG' => 'ORIG']); //Welke formaten er met de tool gecropt mogen worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG', 'ALL'); //'ALL' || pageids OR parent_id. Bij de opgegeven paginaids zal de large(orig) foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB', 'ALL'); //'ALL' || pageids OR parent_id. Bij d eopgegeven paginaids zal de thumb foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG_SIZE', [
    "ALL" => [
      1140,
      300,
    ],
  ]); //Breedte, Hoogte. Naar welke aspect-ratio en grootte de large foto gerescaled of gecropt dient te worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB_SIZE', [
    IMAGES_PAGE_THUMB_WIDTH,
    IMAGES_PAGE_THUMB_HEIGHT,
  ]); //Breedte, Hoogte. Naar welke aspect-ratio en grootte de thumb foto gerescaled of gecropt dient te worden.

  Config::set('PAGES_IMAGES_THUMB_RESIZE', "SQUAREFILL"); //variants: SQUAREFILL: image fully visible as square, SQUARECROP: image cropped into square, DEFAULT: image rescalled proportional
  Config::set('USE_CATEGORY_KEYWORDS', true); //true/false : Men kan keywords invoeren welke woorden er gemapt moeten worden bij de import feed van producten. bijv: maaier komt in categorie grasmaaier
  Config::set('SHOW_PRODUCT_IMAGE_IN_BASKET', true); //toon de eerste product beeld in het winkelmandje
  Config::set("PRODUCT_IMAGES_WATERMARK", false); //Gebruik watermerk op product beelden.
  Config::set("PRODUCT_WATERMARK_LARGE", DIR_PROJECT_FOLDER . 'templates/frontend/images/watermark_large.png'); //DIR large watermerk
  Config::set("PRODUCT_WATERMARK_THUMB", DIR_PROJECT_FOLDER . 'templates/frontend/images/watermark_thumb.png'); //DIR thumb watermerk

  Config::set("PDF_SHOW_PRODUCTCODE", true);
//  Config::set("PRODUCT_USE_SUPPLIER", true); //Aankunnen geven van leverancier
  Config::set("PRODUCT_DESCRIPTION_EXPORT", false); //Extra product omschrijving voor export naar bijv. hovenierwinkel, om zo duplicate content te voorkomen.
  Config::set("USE_ORGAN_IS_CUST_WEBSHOP", false); //Vinkje is webshop klant bij organisaties
  
  Config::set("BRAND_USE_SUB_CATEGORIES", true); //true/false men kan subcategorieën toevoegen bij Merken, en deze koppelen bij de catalogus
//  Config::set("PRODUCT_TYPE_USE", ['standard']); //product type mogelijk en welke types kan gebruiker kiezen

  Config::set('ORDERS_STATI', [
    'custom_order'         => 'Maatwerk offerte',
    'pending'              => "Pending (in afwachting betaling)",
    'new'                  => "Nieuw",
    'topay'                => "Nog te betalen",
    'paid'                 => "Betaald",
    'open'                 => "In verwerking",
    'packing_slip_printed' => "Pakbon geprint",
    'packed'               => "Ingepakt (Klaar voor verzenden / klaar voor afhalen)",
    'send'                 => "Verzonden",
    'backorder'            => "Backorder",
    'cancelled'            => "Geannuleerd",
    'toordersupplier'      => "Te bestellen leverancier",
    'orderedsupplier'      => "Besteld leverancier",
  ]); //mogelijke order statussen

  Config::set('BASKET_ORDER_NO_ACCOUNT', true); //klant kan bestellen zonder account aan te maken
  Config::set('USER_LIST_DIRECTLOGIN', false); //toon direct login knoppen.

  Config::set('PRODUCT_DISCOUNT', true); //Het is mogelijk een kortingsprijs in te voeren.
  Config::set("PRICE_PRODUCT_DECIMALS", 4); //aantal decimalen bij bewerken factuur. Zet op 4 wanneer er ronde prijzen dienen te komen.

  Config::set("PRODUCT_CONTAINERS", true); //uses product containers ('products with different sizes/weights/')
//  Config::set('BASKET_DISCOUNTCODE', true); //18-05-2016 - ROBERT - mag kortingscode invoeren

//  Config::set("PRODUCT_ORGAN_PRICE", true); //klantspecifieke  prijs mogelijk

//  Config::set('GA_E-COM_TRACKING', true); //14-06-2016 - ROBERT - Goolge analytcis E-commerce tracking enabled in basket finished

  Config::set("USER_PASSWORD_ENCRYPT", true);

  Config::set('USER_DEFAULT_SOURCE', "Aanvraag offerte/bestelling"); // standaard tekst in user.source (bron persoons gegevens)
  Config::set('USER_DEFAULT_PURPOSE', "Zie privacy voorwaarden"); // standaard tekst in user.purpose (doel gebruik persoons gegevens)

  Config::set("LOGIN_FRONTEND_USERGROUPS", [
    'PARTICULIER',
    'BEDRIJF',
  ]);

//  Config::set("ORGANISATION_SPECIFIC_PAYMETHODES", [
//    'online'   => true,
//    'rekening' => true, // vooruit betalen
//    'postpay'  => true, // achteraf betalen
//  ]); //27-03-2017 - ROBERT - bij een organisatie kunnen betaalmethodes worden uit en aangezet.

  Config::set("PAGE_TEASER_ENABLED", true);

  Config::set('PAGE_CUSTOM_URL', true); //19-09-2015 - Robert - mogelijkheid om een zelf de url in voeren.
  Config::set('PRODUCT_CONTAINER_CUSTOM_URL', true); // 19-04-2019 - ROB - mogelijkheid om een zelf de product container url in te voeren
  Config::set('PRODUCT_CUSTOM_URL', false); //31-01-2019 - ROBERT - mogelijkheid om een zelf de product url in te voeren
  Config::set('CATEGORY_CUSTOM_URL', true); //31-01-2019 - ROBERT - mogelijkheid om een zelf de categorie url in te voeren
  Config::set("ORGAN_EDIT_SHOW_SOCIAL_MEDIA", false); //true/false. Toont Social media inputvelden bij bewerken organisatie.

//  Config::set("STOCK_LEVEL", true); // toon Product stock_level en stock_level_max
//  Config::set("STOCK_PACKINGSIZE", true); // toon Product packing_size
//  Config::set("STOCK_FLOW_EMAIL_CC", ""); // emailadres op cc van stockflow bestelling

  /*********************
   * HARDCODED PAGE IDS
   *********************/

//  Config::set("GSDFW_BACKEND_PRIVILIGES_OPEN", ['M_API' => 'api']); //dit zijn de pagina id's welke extern bereikbaar zijn zonder standaard authenticatie id=>name

  Config::set("INVOICE_USE_PRODUCTTYPE", true); //13-06-2018 - ROBERT - 60 dagen verwachting

  Config::set('CRYPT_SALT_KEY', 'winkeljari');
  Config::set('CRYPT_SALT_IV', 'CedricRules#1');

  Config::set('PRODUCT_STOCK_CHANGE_LOG', true); //27-08-2019 - ROBERT - log alle product->stock wijzigingen

  Config::set("LOGIN_BACKEND_USERGROUPS", ['SUPERADMIN', 'ADMIN']); //usergroups welke mogen inloggen op de backend [USER.USERGROUP,USER.USERGROUP,...]

  //-----------GSDFW PLUGINS-----------
//  const DIR_PLUGIN_FOLDER = DIR_ROOT_GSDFW . 'plugins/'; //locatie van GSDFW plugin folder
//  const URL_PLUGIN_FOLDER = '/gsdfw/plugins/'; //url van GSDFW plugin folder
//  Config::set("GSDFW_PLUGINS", ['faq']);  //geactiveerd plugins voor dit project

//  config::set('PRODUCT_SUPPLIER_CODE', true);

  Config::set("LOG_ALL_MAIL_TO_DB", true);

  Config::set("ORGAN_PAYTMENTTERM_DEFAULT", 14); // standaard betalingstermijn
//  Config::set("ORGANISATION_INVOICE_TYPE_EDITABLE", false);
//  Config::set("INVOICE_CUSTOMER_INFO_ALIGN_LEFT", true);

//  Config::set('SITE_HOSTS_ALTERNATES', [
//    'nl'    => ['lang' => 'nl', 'url' => ''],
//    'nl-be' => ['lang' => 'nl', 'url' => ''],
//  ]); //Deze worden gebruikt voor het aanmaken van verschillende talen in de sitemap

  const DIR_UPLOAD_PRODUCT_SHEET = DIR_UPLOADS . 'productsheets/';
  const URL_UPLOAD_PRODUCT_SHEET = URL_UPLOADS . 'productsheets/';

//  Config::set('PRODUCT_OPTIONS', ['price_buy_unit', 'is_discontinued', 'expected_delivery_date']); //product options tabel

  Config::set("ORGAN_CUST_NR_ENABLED", true); //klantspecifiek klantnummer mogelijk.
  Config::set("USER_LIST_SHOW_CUSTNR", true); // Tonen van klantnummer op gebruiker overzicht

  Config::set("BREADCRUMBS_IGNORE_IDS", [2]); //Pagina's die niet zichtbaar zijn in de breadcrumbs
  Config::set('PAGES_IGNORE_IN_FEED', [2, 'M_SHOP_CATEGORY', 'M_PRODUCTS', 'M_BASKET']); //06-09-2016 - ROBERT - pageid's negeren in feed (alleen deze id's niet de kinderen van deze pagina)

//  Config::set('PRODUCT_WEIGHT_ENABLED', true);//true if the product weight is available and mandatory

  Config::set("GSDEDITOR", [
    "active"                => true, //is blockeditor active
    "toggle"                => true, //may toggle between old and new editore
    "developer"             => DEVELOPMENT && false, //set true if you want to load in dev mode
    "tinymce_stylesheets"   => [
      '/projects/jari/templates/frontend/dist/main.min.css',
    ], //stylesheets to use in tinymce
    "tinymce_content_style" => 'https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900&display=swap', //extra style to use in tinymce
  ]);  //24-02-2022 - ROBERT - blockeditor

  Config::set('opening-hours', [
    'Ma t/m Do: 8u–12u & 13u–17u',
    'Vrij: 8u–12u & 13u–15u',
    'Zat & Zon: Gesloten',
    'Gesloten op feestdagen'
  ]);

  // use https://squoosh.app/ to convert images to WebP for performance
  Config::set('PAGE_COMPONENT_IMAGES', [
      1 => [
        'questionsImage' => '15-jari-systems-15.webp'
      ],
      2 => [
        'promotionImage' => '19-jari-systems-19.webp',
        'questionsImage' => '53-jari-systems-53.webp'
      ],
      3 => [
        'promotionImage' => '58-jari-systems-58.webp',
        'questionsImage' => '40-jari-systems-40.webp'
      ],
      4 => [
        'questionsImage' => '15-jari-systems-15.webp'
      ],
  ]);

  Config::set('ABOSERVICE_API', [
    'url' => 'http://185.92.199.28:8888',
    'tokenExp' => 43200, // 12 hours (in seconds)
  ]);
