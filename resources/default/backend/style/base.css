@CHARSET "UTF-8";

html {
  height: 100%;
}

body {
  color: #4E4E4E;
  margin: 0;
  font-family: 'Open Sans', sans-serif;
  font-size: 11px;
}

#maincontainer {
  margin: 0 auto;
  width: 1200px;
}

.officina, #navmenu-h a, #footer, #tabnav a, ul#leftmenu a, .footermenu {
  font-family: 'Open Sans', sans-serif;
}

td {
  color: #4E4E4E;
  vertical-align: top;
  text-align: left;
  font-family: 'Open Sans', sans-serif;
  font-size: 11px;
}

img {
  border: 0;
}

form {
  display: inline;
}

h1 {
  font-size: 17px;
}

h2 {
  font-size: 15px;
}

h3 {
  font-size: 13px;
}

h4 {
  font-size: 11px;
}

.clear {
  clear: both;
}

/* zodat tekst achter input veldjes mooi in het midden staat */
.table_td_middle tr.dataTableRow td {
  line-height: 20px;
}

.head {
  font-size: 11px;
  height: 23px;
  font-weight: bold;
}

.tablehead {
  font-size: 11px;
  font-weight: bold;
}

div.content {
  padding: 0 0 25px 0;
  vertical-align: top;
  min-height: 400px;
  width: 100%;
}
.has_leftmenu {
  display: flex;
}
.has_leftmenu .div_leftmenu {
  width: 200px;
  margin-right: 15px;
  padding: 10px 0;
  background-color: #f0f0f080;
}
div.content.has_leftmenu {
  padding-top: 5px;
}

.lockonsubmit_spinner {
  margin-left: 5px;
  margin-right: 5px;
  color: #f44336;
}

div.scroll {
  /* width: 100%; */
  height: 300px;
  overflow: auto;
  background-color: White;
  padding: 5px 10px 10px 10px;
}

div.box, form.list-filter-form {
  padding: 3px 5px;
  background-color: #f2f2f229;
  display: block;
}

form.list-filter-form {
  display: block;
}

div.label {
  width: 80px;
  float: left;
  padding-top: 3px;
}


/**** ERRORS/ALERTS START *****/

.asterisk, .error {
  color: #DE0000;
}

.alert {
  padding: 10px;
  margin-bottom: 10px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid transparent;
}
tr.alert td {
  color: red;
}

.alert-dismissable, .alert-dismissible {
  padding-right: 35px;
}

.alert.alert-danger {
  background-color: #F2DEDE;
  border-color: #EED3D7;
  color: #B94A48;
}

.alert.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}

.alert.alert-success {
  color: black;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.alert .alert-link, .alert a {
  font-weight: 700;
}

.alert-danger .alert-link {
  color: #843534;
}

.alert-warning .alert-link {
  color: #66512c;
}

.alert-success .alert-link {
  color: #245269;
}
.alert .fa {
  font-size: 18px;
  vertical-align: top;
  padding-right: 5px;
}

div.errorbox ul,
div.warningbox ul,
div.alert ul {
  padding: 0 20px;
  margin: 5px 0 0 0;
  list-style-type: disc;
}

.messagered {
  padding: 10px;
  margin: 5px 0;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  background-color: #F2DEDE;
  border: 1px solid #EED3D7;
  border-radius: 4px;
  color: #B94A48;
  font-size: 12px;
}

.message {
  padding: 10px;
  margin: 5px 0;
  background-color: #E6F1F6;
  border: 1px solid #BCE8F1;
  border-radius: 4px;
  color: #29235C;
  font-size: 12px;
}

/**** ERRORS/ALERTS END *****/


/**** A START *****/

a.popuplink,
a.popuplink:hover,
a.popuplink:visited,
a.popuplink:active {
  text-decoration: none;
  font-weight: normal;
  color: #696969;
}

a {
  color: #4E4E4E;
  text-decoration: underline;
  font-weight: bold;
}

a:hover {
  color: Black;
  text-decoration: underline;
}

a:active {
  color: #4E4E4E;
}

a.btn.btn-primary, a.btn.btn-secondary, a.btn.btn-tertiary {
  padding: 4px 10px;
  display: inline-block;
  font-weight: normal;
}

/**** A END *****/

/**** FORM ELEMENTS / INPUT / SELECT / TEXTAREA START *****/
.inputerror {
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 4px -2px #DE0000 !important;
  border: 1px solid #DE0000 !important;
}

.inputerror:focus {
  box-shadow: 0 4px 8px -4px #DE0000 !important;
}

input:focus,textarea:focus,select:focus, button:focus {
  outline: none;
}

input.disabled, input.readonly, input[readonly], input[disabled], select[disabled], textarea[readonly] {
  background-color: #F0F0F0;
  border: 1px solid #BBBBBB;
}

input[type=button][disabled], input[type=submit][disabled], input[type=button].disabled, input[type=submit].disabled {
  color: #808080;
}

input, select, textarea {
  font-family: inherit;
  font-size: 11px;
  border-radius: 3px;
}

textarea {
  width: 300px;
  border: 1px solid #BBBBBB;
  padding: 4px;
}

input[type="text"], input[type="password"] {
  width: 300px;
}

/** @todo: Vanuit de backend hebben all knop-input velden dezelfde stijl als gsd-btn. hmmm.... eigenlijk zou je de class gsd-btn op deze inputs moeten zetten.. **/
input[type=submit], input[type=reset], input[type=button], .btn.btn-primary {
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  border: 1px solid transparent;
  padding: 4px 10px;
  text-align: center;
  text-decoration: none;
  border-radius: 3px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

/** making sure all input/select/a.buttons are same height **/
input, .btn.btn-primary, select {
  box-sizing: border-box;
  height: calc(1.5em + .5rem + 1.5px);
}

input[type=submit]:not([disabled]):hover, input[type=reset]:hover, input[type=button]:not([disabled]):hover, .btn.btn-primary:hover {
  /*border: 1px solid #2E7ABA;*/
  /*color: #2E7ABA;*/
  /*box-shadow: 0 0 2px 0 #4a4a4a;*/
  background: var(--gsd-btn-bg-color-hover);
}

label input[type=checkbox] {
  vertical-align: middle;
}

input[type=text], input[type=password], input[type=date], input[type=datetime], input[type=time], input[type=number], select {
  border: 1px solid rgba(187, 187, 187, 0.65);
  padding: 4px;
}

input[type=text]:focus, textarea:focus {
  border-color: #999;
}

select {
  padding: 3px 2px 3px 0;
}

option:disabled {
  color: #0091BD;
}


/**** FORM ELEMENTS / INPUT / SELECT / TEXTAREA END *****/

.footer a, .footer a:active, .footer a:visited {
  color: #d6d6d6;
  font-size: 10px;
  text-decoration: none;
}

.footer a:hover {
  color: Black;
  text-decoration: underline;
}

td.footer {
  text-align: center;
  padding: 5px;
  color: #d6d6d6;
  font-size: 10px;
}

#footer {
  background-color: #F0F0F0;
}

.footermenu {
  font-size: 12px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  justify-content: space-between;
}

.footermenu a {
  text-decoration: none;
  font-weight: normal;
}

.footermenu a:hover {
  color: black;
}

/* This is for IE */
.hiddenBtn {
  width: 0;
  height: 0;
}

/* This is for Firefox since IE ignores it */
.hiddenBtn[class] {
  display: none;
}
.hidden {
  display: none;
}

a.imagegallery, a.imagegallery_special {
  padding: 10px 0;
}

a.imagegallery {
  display: inline-block;
}

a.fancyboxmap {
  float: left;
  display: block;
}

div.questiontitle {
  font-weight: bold;
}

input.datesel, input.datepicker, input.datepicker_week {
  width: 80px;
}

input.datetimesel, input.datetimepicker {
  width: 125px;
}

input.price {
  text-align: right;
  width: 70px;
}

input.percentage {
  text-align: right;
  width: 70px;
}

.multi-select {
  width: 100%;
  height: 400px;
}

div.st {
  padding-top: 10px;
  font-size: 12px;
  padding-left: 0;
  float: left;
}

#topmenu {
  float: right;
  color: #282828;
  padding: 20px 30px 0 0;
  text-align: right;
  vertical-align: top;
  font-size: 12px;
}

#topmenu a {
  color: #282828;
  text-decoration: none;
}

#topmenu .name {
  color: #c4013d;
  font-weight: bold;
}

a#return2admin {
  color: #184EA2;
}

/*********************
 breadcrumbs
*********************/
#breadcrumbs {
  border-bottom: 1px solid #f2f2f2;
  padding: 4px 0 4px 20px;
}

#breadcrumbs a {
  text-decoration: none;
}

#breadcrumbs a:hover {
}

#breadcrumbs ol {
  display: inline-block;
  margin: 0;
  padding: 0;
}

#breadcrumbs li {
  display: inline-block;
  margin: 0;
}

#breadcrumbs li:not(:last-child):after {
  content: "-";
  margin: 0 5px;
}
#breadcrumbs li:first-child a:before {
  content: "\f015";  /* this is your text. You can also use UTF-8 character codes as I do here */
  font-family: FontAwesome;
  position: relative;
  margin: 0;
  font-size: 13px;
  font-weight: normal;
}
#breadcrumbs li:first-child span {
  display: none;
}

#topsearch {
  float: right;
  padding: 6px 0 0 0;
  width: 254px;
}

#topsearch input[type="text"] {
  width: 150px;
}

#topsearch input[type="submit"] {

}

#homelink {
  width: 440px;
  height: 95px;
  display: block;
  float: left;
  text-decoration: none;
}

.productprice {
  font-weight: bold;
  color: black;
  padding: 3px 0 0 0;
}

.productcode {
  height: 14px;
}

input.productsize {
  width: 30px;
  text-align: right;
  margin: 8px 0 0 0;
}

#productinfo {
}

.productinfo_table {
  border-spacing: 0;
  border: 1px solid #EEEEEE;
}

.productinfo_table tr td:first-child {
  background-color: #EEEEEE;
}

.productinfo_table tr td {
  padding: 5px 7px;
  border: 1px solid #EEEEEE;
}

.productinfo_table a {
  text-decoration: none;
}

#productinfo .left {
  float: left;
  width: 350px;
}

#productinfo .right {
  float: right;
  width: 200px;
}

#productinfo .right img {
  border: 1px solid #DDD4CF;
  margin: 0 10px 10px 0;
}

#productinfo .imgbottom img {
  border: 1px solid #DDD4CF;
  margin: 0 10px 10px 0;
}

#productinfo .price, #productinfo .discountprice {
  font-weight: bold;
  font-size: 15px;
  color: black;
  padding: 5px 0;
}

#productinfo .discountprice {
  color: #c4013d;
}

#productinfo .description {
  padding: 5px 0;
  width: 430px;
}

.cart {
  padding: 5px 0;
}

/* BASKET */
#basket input.productsize {
  width: 30px;
  text-align: right;
  margin: 0;
}

#basket .subtotalrow td {
  text-align: right;
  font-weight: bold;
  border-top: 1px solid #BAB9B9;
}

#importexcel {
  float: right;
  display: inline-block;
  margin: 10px 0;
}

table.steps, table.basket {
  width: 100%;
}

table.steps td:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

table.steps td:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

table.steps td {
  font-weight: bold;
  padding: 8px;
  text-align: center;
}

td.step_on {
  background-color: #1B396D;
  color: white;
}

td.step_on a {
  color: white;
  text-decoration: none;
}

td.step_off {
  background-color: #EAEBEC;
  color: black;
}

#shippingadres table {
  float: left;
  margin: 10px 20px 0 0;
  padding: 5px;
  border: 1px solid #DDD4CF;
}

#shippingcosts {
  margin: 10px 0;
  padding: 5px;
  font-weight: bold;
  border: 1px solid #DDD4CF;
}

.container_payment label {
  padding: 2px 0;
  display: inline-block;
}

.container_payment label input {
  vertical-align: sub;
}

.prevstep {
  float: left;
}

.nextstep {
  float: right !important;
}

/* HOME spotlight products*/
.shop_discount {
  width: 210px;
  height: 190px;
  float: left;
  margin: 8px 20px 10px 0;
  background-color: white;
  border-radius: 5px 5px 10px 10px;
  border: 1px solid #5a5a5a;
}

.shop_discount_img {
  width: 200px;
  height: 130px;
  margin: 5px 5px 0 5px;
}

.shop_discount_name {
  height: 45px;
  background-color: #5a5a5a;
  border-radius: 0 0 5px 5px;
  padding: 5px 6px;
  overflow: hidden;
  font-weight: bold;
  color: white;
  font-size: 11px;
}

.shop_discount_name a, .shop_discount_name a:visited {
  color: white !important;
  display: block;
  height: 27px;
  overflow: hidden;
  float: left;
  text-decoration: line-through;
  padding: 3px 10px 0 0;
  font-size: 13px;
}

.shop_discount_price {
  float: left;
  color: white;
  padding: 3px 0 0 0;
  font-size: 13px;
}

img.productimage {
  width: 180px;
  height: 110px;
  margin: 10px;
}

.tabscontent {
  width: 100%;
  display: none;
  padding: 5px 0;
}

input.size {
  width: 50px;
}

.tabsconenttitle {
  padding: 5px 0 0 0;
  font-size: 13px;
}

#optiontable td {
  vertical-align: top;
  padding: 2px;
}

.websiteex {
  float: left;
  width: 165px;
  border: 1px solid grey;
  text-align: center;
  padding: 10px;
  margin-right: 5px;
}

.header {
  background: url(../images/gsd_logo_rand.svg) no-repeat 10px 14px;
  background-size: 190px;
  height: 95px;
}

#header_company_logo {
  display: inline-block;
  height: 76px;
  width: 150px;
  vertical-align: bottom;
  float:left;
  margin: -10px 15px 0 0;
  text-align: center;
}
#header_company_logo_h {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
#header_company_logo img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}
#header_my_info {
  padding-top: 3px;
  float:right;
}

.selectprod {
  padding: 3px 0;
  display: block;
}

/* css for timepicker */
.ui-timepicker-div .ui-widget-header {
  margin-bottom: 8px;
}

.ui-timepicker-div dl {
  text-align: left;
}

.ui-timepicker-div dl dt {
  height: 25px;
  margin-bottom: -25px;
}

.ui-timepicker-div dl dd {
  margin: 0 10px 10px 65px;
}

.ui-timepicker-div td {
  font-size: 90%;
}

.ui-tpicker-grid-label {
  background: none;
  border: none;
  margin: 0;
  padding: 0;
}

.ui-timepicker-rtl {
  direction: rtl;
}

.ui-timepicker-rtl dl {
  text-align: right;
}

.ui-timepicker-rtl dl dd {
  margin: 0 65px 10px 10px;
}

.languageselect {
  vertical-align: middle;
  display: inline-block;
}

.qq-upload-button {
  background-color: #EAEBEC !important;
  border: 1px solid #BBBBBB !important;
  color: black !important;
  padding: 2.5px 10px !important;
  text-align: center !important;
  text-decoration: none !important;
  width: 150px !important;
}

.descriptiondate {
  display: inline-block;
}

#orderform .emailcust {
  display: none;
}

#ordertable {
  margin: 5px 0;
}

#ordertable td {
  vertical-align: middle;
  font-size: 13px;
  padding-bottom: 5px;
}

#content_invoice td {
  vertical-align: middle;
}

#invoice_list_table .qtipa.fa_info {
  color: orange;
}

.status_div {
  float: left;
  width: 125px;
  margin-right: 3px;
  padding: 3px 5px;
  text-decoration: none;
  font-weight: normal;
  font-size: 12px;
  border-radius: 3px;
}

.datepicker.descriptiondateinput {
  width: 60px;
}

a.delproduct, a.minbasket, a.plusbasket {
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
  padding: 3px;
}

a.delproduct img {
  vertical-align: bottom;
}

/* PRODUCT TABLE */
.producttable {
  float: left;
  border-top: 1px solid #E9E9E9;
  margin-top: 3px;
  width: 700px
}

.producttd {
  width: 100%;
  float: left;
  border-bottom: 1px solid #E9E9E9;
}

.producttd:hover {
  background-color: #F0F1F1;
}

a.producttd_img, .producttd_txt, .producttd_size, .producttd_add, .producttd_stockinfo {
  height: 71px;
  float: left;
  padding: 3px;
}

.producttd_txt {
  height: 61px;
  float: left;
  font-size: 12px;
}

.producttd_txt a {
  display: block;
  padding-bottom: 3px;
  text-decoration: none;
}

.producttd_img img {
  max-width: 120px;
  max-height: 70px;
}

.producttd_img {
  width: 120px;
  display: block;
}

.producttd_txt {
  width: 400px;
  padding: 5px;
}

.producttd_size {
  width: 40px;
}

.producttd_add {
  width: 110px;
}

.producttd_size input.productsize {
  margin: 0;
}

a.plusinput, a.mininput {
  text-decoration: none;
  font-size: 17px;
  padding: 8px 2px;
}

.producttd_stockinfo {
  width: 100px;
}

.product_outofstock {
  color: red;
  font-style: italic;
}

.not_in_backorder_info {
  font-style: italic;
  margin-top: 5px;
  display: inline-block;
}

.orderstatusdiv {
  float: left;
  width: 150px;
  margin-right: 3px;
  padding: 3px 5px;
  text-decoration: none;
  font-weight: normal;
  font-size: 12px;
  border-radius: 3px;
}

.orderstatusdiv:hover {
  text-decoration: none;
}

.phonebutton {
  display: inline-block;
  vertical-align: middle;
}

.info-box a {
  text-decoration: none;
}

.ui-colorpicker, .ui-dialog.ui-colorpicker {
  width: 350px !important;
}

/* ORGANISATION EDIT */
#pac-input, .pac-input {
  margin-top: 10px;
  border: 1px solid transparent;
  border-radius: 2px 0 0 2px;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  height: 40px;
  outline: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  padding: 0 6px 0 13px;
  width: 300px;
  font-family: Roboto, serif;
  font-size: 18px;
  font-weight: 300;
  text-overflow: ellipsis;
}

::placeholder, :-ms-input-placeholder, ::-ms-input-placeholder {
  color: #999;
}

.material-icons.material-icon-green {
  color: #A8BA2A;
}
.material-icons.material-icon-orange {
  color: #ff7811;
}
.material-icons.material-icon-off {
  color: #bbbbbb;
}

.user-logo {
  float:      left;
  padding:    8px 0 0 10px
}
.user-logo img {
  max-width:  170px;
  max-height: 61px;
}

#message_development, #message_updater {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
  display: block;
}

#message_updater ul {
  list-style: none;
  margin: 0;
  padding: 0;
  font-weight: normal
}

/* ------------------------ JQuery Automplete CSS ----------------------- */
.autocomplete-suggestions { border: 1px solid #999; background: #FFF; cursor: default; overflow: auto; -webkit-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64); -moz-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64); box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64); }
.autocomplete-suggestion { padding: 2px 5px; white-space: nowrap; overflow: hidden; }
.autocomplete-no-suggestion { padding: 2px 5px;}
.autocomplete-selected { background: #F0F0F0; }
.autocomplete-suggestions strong { font-weight: bold; color: #FF9900; }
.autocomplete-group { padding: 2px 5px; }
.autocomplete-group strong { font-weight: bold; font-size: 16px; color: #FF9900; display: block; border-bottom: 1px solid #000; }

/* CKEDITOR MODAL */
.fm-modal {
  z-index: 10011; /** Because CKEditor image dialog was at 10010 */
  width:80%;
  height:80%;
  top: 10%;
  left:10%;
  border:0;
  position:fixed;
  -moz-box-shadow: 0px 1px 5px 0px #656565;
  -webkit-box-shadow: 0px 1px 5px 0px #656565;
  -o-box-shadow: 0px 1px 5px 0px #656565;
  box-shadow: 0px 1px 5px 0px #656565;
}

.swal2-popup .swal2-styled:focus {
  box-shadow: none;
}

.select2.select2-container {
  vertical-align: top;
}
.select2-container .select2-selection--single, .select2-container--default .select2-selection--single .select2-selection__rendered {
  height: 26px !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 26px !important;
}

/** used for address fields */
input.address_street {
  width: 240px;
}

input.address_nr {
  width: 50px;
}

input.address_zip {
  width: 50px;
}

input.address_city {
  width: 240px;
}
