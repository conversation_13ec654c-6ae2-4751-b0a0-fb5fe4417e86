<?php

  use gsdfw\domain\backendpreferences\service\BackendPreferences;

  Config::set('IS_BACKEND', true); //config to define frontend/backend

  ini_set('memory_limit', '512M'); //beheer default hogere memory_limit

  PrivilegeBuilderFactory::buildTree(!empty($_SESSION['userObject']) ? $_SESSION['userObject'] : false);
  PageMapBuilder::buildBackend();
  NavigationBuilderFactory::buildBackend();
  (new BackendPreferences())->load($_SESSION['userObject'] ?? false);

  $pageId = 'M_LOGIN';
  if (isset($_SESSION['loggedIn']) && $_SESSION['loggedIn']) {
    if (isset($_GET['pageId']) && $_GET['pageId'] != 'index') {
      $pageId = $_GET['pageId'];
    }
    elseif (isset($_SERVER["REQUEST_URI"]) && ($_SERVER["REQUEST_URI"] == "/" || str_starts_with($_SERVER['REQUEST_URI'], '/?'))) {
      $pageId = 'M_HOME';
      if (Config::isdefined("HOMEPAGEID") && Config::get("HOMEPAGEID")[$_SESSION['userObject']->usergroup]) {
        $pageId = Config::get("HOMEPAGEID")[$_SESSION['userObject']->usergroup];
      }
    }
    else {
      //de url in de backend applicatie is niet gevonden. dan naar 404 pagina.
      $errors_msg = "Url niet gevonden: https://" . $_SERVER["HTTP_HOST"] . $_SERVER["REQUEST_URI"];
      if (ENVIRONMENT == "PRODUCTION"
        && !in_array($_SERVER["REQUEST_URI"], ['/android-chrome-192x192.png', '/favicon.ico', '/apple-touch-icon-precomposed.png', '/apple-touch-icon.png'])
        && !str_starts_with($_SERVER["REQUEST_URI"], '/uploads/')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/index.php')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/nl/')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/de/')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/en/')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/fr/')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/nl')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/de')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/en')
        && !str_starts_with($_SERVER["REQUEST_URI"], '/fr')
      ) {
        //Robert: tijdelijk even mailen, ik ben benieuwd of dit vaak voorkomt op productie.
        //negeer de apple-touch-icon.png, en bestanden in uploads folder
        (new GsdExceptionHandler())->sendMail(PROJECT . ' [url not found]', nl2br($errors_msg));
      }
      ResponseHelper::exit404($errors_msg);
    }
  }

  //Niet ingelogd mag je niet naar M_ERROR of M_ERROR_RIGHTS. Waarschijnlijk treed een foutmelding op omdat je niet bent ingelogd. Dus gewoon naar inlog pagina.
  $privilige_open = ["M_LOGIN" => "inloggen", "M_LOGOFF" => "uitloggen", "M_EXTERNAL" => "external"];
  if (Config::isdefined("GSDFW_BACKEND_PRIVILIGES_OPEN")) {
    $privilige_open = array_merge($privilige_open, Config::get("GSDFW_BACKEND_PRIVILIGES_OPEN"));
  }

  if (!isset($_SESSION['loggedIn']) || !$_SESSION['loggedIn']) {
    if (isset($_GET['pageId'])) {
      //deze paginas hoef je niet voor ingelogd te zijn, en deze is dus extern bereikbaar
      if (isset($privilige_open[$_GET['pageId']])) {
        $pageId = $_GET['pageId'];
      }
      elseif (in_array($_GET['pageId'], $privilige_open)) {
        $pageId = array_search($_GET['pageId'], $privilige_open);
      }
    }
  }
  elseif (isset($_GET['redirect'])) {
    //redirect to a certain page
    ResponseHelper::redirect(Context::getSiteDomain() . $_GET['redirect']);
  }
  elseif (isset($_SESSION['loggedIn']) && $_SESSION['loggedIn']) {
    if (isset($_GET['pageId']) && $_GET['pageId'] != '' && $_GET['pageId'] != 'index') {
      $pageId = $_GET['pageId'];
    }
    elseif (isset($_GET['var1'])) {
      $pageId = PageMap::getInstance()->getPageId($_GET['var1']);
    }
    elseif (isset($site) && $site->site_host->homepage_id != null) { //open default page
      $pageId = $site->site_host->homepage_id;
    }
  }

  MessageCoordinator::validate();

  //geen pageId in de pageId variable
  $pageId = PageMap::getInstance()->getPageId($pageId);

  if (!isset($privilige_open[$pageId]) && ((isset($_SESSION['project']) && $_SESSION['project'] != PROJECT) || !isset($_SESSION['session_version']) || $_SESSION['session_version'] != Setting::getByCode('session_version')->value)) {
    GsdSession::stopSession(true);
    ResponseHelper::redirect(PageMap::getUrl('M_LOGIN'));
  }
  elseif (in_array($pageId, ['M_LOGIN', 'M_ERROR', 'M_ERROR_RIGHTS']) && isset($_GET['action'])) {
    //voor deze pageId's hebben we geen $_GET['action'] als deze geset is even unsetten
    unset($_GET['action']);
  }
  elseif (isset($_SESSION['loggedIn']) && $_SESSION['loggedIn'] && $pageId != "M_LOGOFF") {
    $GSdone = GsdGoogleAuthenticator::verifyGoogleAuthenticator($_SESSION['userObject']);
    if ($GSdone) {
      GsdLoginIpaddress::verify($_SESSION['userObject']);
    }
  }

  if (isset($_GET['dl'])) {
    $pageId = "M_LOGIN";
    $_GET['action'] = "directlogin";
  }

  $skip = [
    'M_ORGANISATIONS',
    'M_PAGES_EDIT',
    'M_PAGES_IMAGES',
    'M_PAGES_FILES',
    'M_PAGES_YOUTUBE',
    'M_PAGES_GALLERY',
    'M_PAGES_LABELS',
    'M_PAGES_RELATED',
  ];
  if (Config::isdefined('BREADCRUMBS_BACKEND_IGNORE_IDS')) {
    $skip = array_merge($skip, Config::get('BREADCRUMBS_IGNORE_IDS'));
  }
  Navigation::getInstance()->fillBreadCrumbs($pageId, 'M_TOP', '', 'Home', $skip);

  if ($pageId === false || !Navigation::getInstance()->setNavActive($pageId)) {
    if (isset($_SERVER["REQUEST_URI"]) && $_SERVER["REQUEST_URI"] == "/") {
      GsdSession::stopSession(true); //clear session.
      logToFile("fatal", "Loop prevention. Unknown pageId: " . $pageId);
      die("Loop prevention. Unknown pageId: " . $pageId); //loop prevention.
    }
    if (ENVIRONMENT == 'LOCAL') {
      throw new GsdException('DEVELOPER MESSAGE: pageId or URL not found. On production page will be redirected to / (pageId=' . $pageId . ', url: ' . $_SERVER["REQUEST_URI"] . ') Is this correct behaviour? Hints: define the pageId in routing | add it to the Navigation class | js could crash on DEVELOPMENT if tries to load unexisting js/css');
    }
    ResponseHelper::redirect('/'); //redirect naar homepage
  }
  elseif (!Privilege::hasRight($pageId)) {
    ResponseHelper::redirectAccessDenied();
  }

  // this makes it possible to set the api/module action on the url like so: /nl/api/getuser, where getuser is the action of the module
  // $_GET['action'] overrules this
  if ($pageId === 'M_API' && !empty($_GET['rest']) && $_GET['rest'] !== '/' && empty($_GET['action'])) {
    $rest_params = explode('/', $_GET['rest']);
    // set the action, $_GET['rest'] starts with a slash so action is on key index 1
    Navigation::getInstance()->getActiveItem()->setAction($rest_params[1]);
  }

  require(DIR_INCLUDES . "actionloader.inc.php");

  if (!isset($seo_title) || $seo_title == null) {
    $seo_title = Navigation::getItem($pageId)->getName();
  }
  $seo_title .= " - " . (Config::isdefined("BEHEER_NAME") ? Config::get("BEHEER_NAME") : __("Beheer") . ' ' . ucfirst(PROJECT));

  if (!isset($seo_description) || $seo_description != null) {
    $seo_description = PROJECT;
  }
