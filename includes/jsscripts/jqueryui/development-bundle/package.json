{"name": "jquery-ui", "title": "jQuery <PERSON>", "description": "A curated set of user interface interactions, effects, widgets, and themes built on top of the jQuery JavaScript Library.", "version": "1.10.4", "homepage": "http://jqueryui.com", "author": {"name": "jQuery Foundation and other contributors", "url": "https://github.com/jquery/jquery-ui/blob/1.10.4/AUTHORS.txt"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://scottgonzalez.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "url": "http://bassistance.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://krisborchers.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://gnarf.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mike.sherov.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjvantoll.com"}], "repository": {"type": "git", "url": "git://github.com/jquery/jquery-ui.git"}, "bugs": "http://bugs.jqueryui.com/", "licenses": [{"type": "MIT", "url": "https://github.com/jquery/jquery-ui/blob/1.10.4/MIT-LICENSE.txt"}], "scripts": {"test": "grunt"}, "dependencies": {}, "devDependencies": {"grunt": "0.4.1", "grunt-contrib-jshint": "0.7.1", "grunt-contrib-uglify": "0.1.1", "grunt-contrib-concat": "0.1.3", "grunt-contrib-qunit": "0.2.0", "grunt-contrib-csslint": "0.1.1", "grunt-contrib-cssmin": "0.4.2", "grunt-compare-size": "0.4.0-rc.3", "grunt-html": "0.3.3", "grunt-git-authors": "1.2.0", "rimraf": "2.1.4", "testswarm": "1.1.0"}, "keywords": []}