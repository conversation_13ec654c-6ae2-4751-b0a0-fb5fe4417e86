{"version": 3, "sources": ["webpack:///./lang/da_DK.js", "webpack:///../../../src/lang/da_DK.ts"], "names": ["window", "am4lang_da_DK", "_decimalSeparator", "_thousandSeparator", "_big_number_suffix_3", "_big_number_suffix_6", "_big_number_suffix_9", "_big_number_suffix_12", "_big_number_suffix_15", "_big_number_suffix_18", "_big_number_suffix_21", "_big_number_suffix_24", "_small_number_suffix_3", "_small_number_suffix_6", "_small_number_suffix_9", "_small_number_suffix_12", "_small_number_suffix_15", "_small_number_suffix_18", "_small_number_suffix_21", "_small_number_suffix_24", "_byte_suffix_B", "_byte_suffix_KB", "_byte_suffix_MB", "_byte_suffix_GB", "_byte_suffix_TB", "_byte_suffix_PB", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_millisecond_second", "_duration_millisecond_minute", "_duration_millisecond_hour", "_duration_millisecond_day", "_duration_millisecond_week", "_duration_millisecond_month", "_duration_millisecond_year", "_duration_second", "_duration_second_minute", "_duration_second_hour", "_duration_second_day", "_duration_second_week", "_duration_second_month", "_duration_second_year", "_duration_minute", "_duration_minute_hour", "_duration_minute_day", "_duration_minute_week", "_duration_minute_month", "_duration_minute_year", "_duration_hour", "_duration_hour_day", "_duration_hour_week", "_duration_hour_month", "_duration_hour_year", "_duration_day", "_duration_day_week", "_duration_day_month", "_duration_day_year", "_duration_week", "_duration_week_month", "_duration_week_year", "_duration_month", "_duration_month_year", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "res", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Flow diagram", "Chord diagram", "TreeMap chart", "Sliced chart", "Series", "Candlestick Series", "OHLC Series", "Column Series", "Line Series", "Pie Slice Series", "Funnel Series", "Pyramid Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eC4DEC,kBAAqB,IACrBC,mBAAsB,IAUtBC,qBAAwB,IACxBC,qBAAwB,IACxBC,qBAAwB,IACxBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IAEzBC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAE3BC,eAAkB,IAClBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KAWnBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAuBdC,sBAAyB,MACzBC,6BAAgC,SAChCC,6BAAgC,YAChCC,2BAA8B,eAC9BC,0BAA6B,iBAC7BC,2BAA8B,iBAC9BC,4BAA+B,uBAC/BC,2BAA8B,6BAE9BC,iBAAoB,KACpBC,wBAA2B,QAC3BC,sBAAyB,WACzBC,qBAAwB,gBACxBC,sBAAyB,gBACzBC,uBAA0B,sBAC1BC,sBAAyB,4BAEzBC,iBAAoB,KACpBC,sBAAyB,QACzBC,qBAAwB,aACxBC,sBAAyB,aACzBC,uBAA0B,mBAC1BC,sBAAyB,yBAEzBC,eAAkB,QAClBC,mBAAsB,aACtBC,oBAAuB,aACvBC,qBAAwB,mBACxBC,oBAAuB,yBAEvBC,cAAiB,OACjBC,mBAAsB,OACtBC,oBAAuB,aACvBC,mBAAsB,mBAEtBC,eAAkB,OAClBC,qBAAwB,OACxBC,oBAAuB,OAEvBC,gBAAmB,OACnBC,qBAAwB,aAExBC,eAAkB,OAGlBC,QAAW,QACXC,QAAW,QAUXC,EAAK,IACLC,EAAK,IACLC,GAAM,KACNC,GAAM,KACNC,OAAQ,KACRC,OAAQ,KAoBRC,QAAW,SACXC,SAAY,UACZC,MAAS,QACTC,MAAS,QACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,aAAc,MACdC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OAGPC,OAAU,SACVC,OAAU,SACVC,QAAW,UACXC,UAAa,SACbC,SAAY,UACZC,OAAU,SACVC,SAAY,SACZC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OAWPC,SAAY,SAASC,GACnB,IAAIC,EAAM,KACV,GAAKD,EAAM,IAAQA,EAAM,GACvB,OAAQA,EAAM,IACZ,KAAK,EACHC,EAAM,KACN,MACF,KAAK,EACHA,EAAM,KACN,MACF,KAAK,EACHA,EAAM,KAIZ,OAAOA,GAKTC,WAAY,OAGZC,KAAQ,SACRC,KAAQ,OAGRC,OAAU,qBAGVC,sCAAuC,GAGvCC,QAAW,WAIXC,KAAQ,OAKRC,MAAS,GACTC,eAAgB,GAChBC,YAAa,GACbC,YAAa,GACbC,cAAe,GACfC,cAAe,GACfC,iBAAkB,GAClBC,eAAgB,GAChBC,gBAAiB,GACjBC,gBAAiB,GACjBC,eAAgB,GAKhBC,OAAU,GACVC,qBAAsB,GACtBC,cAAe,GACfC,gBAAiB,GACjBC,cAAe,GACfC,mBAAoB,GACpBC,gBAAiB,GACjBC,iBAAkB,GAClBC,aAAc,GAGdC,IAAO,GACPC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,oCAAqC,GACrCC,8DAA+D,GAY/DC,OAAU,UACVC,MAAS,UACTC,KAAQ,OACRC,MAAS,UACTC,oCAAqC,GACrCC,sCAAuC,GACvCC,6CAA8C,GAC9CC,2EAA4E,GAC5EC,uFAAwF,GACxFC,oCAAqC,GACrCC,wBAAyB,GACzBC,+EAAgF,GAChFC,aAAc,GACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,GAC/EC,8CAA+C,GAC/CC,mDAAoD,GACpDC,oDAAqD,GACrDC,wEAAyE,GACzEC,2CAA4C,GAC5CC,iDAAkD,GAClDC,iDAAkD,GAClDC,gBAAiB,gBACjBC,UAAW,SACXC,QAAS,SAGTC,mCAAoC,GACpCC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,eAAgB", "file": "./lang/da_DK.js", "sourcesContent": ["import m from \"../../es2015/lang/da_DK\";\nwindow.am4lang_da_DK = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/da_DK.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n *\n * Locale: da_DK\n * Language: Danish\n * Author: <PERSON><PERSON><PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n  // Number formatting options.\n  //\n  // Please check with the local standards which separator is accepted to be\n  // used for separating decimals, and which for thousands.\n  \"_decimalSeparator\": \",\",\n  \"_thousandSeparator\": \".\",\n\n  // Suffixes for numbers\n  // When formatting numbers, big or small numers might be reformatted to\n  // shorter version, by applying a suffix.\n  //\n  // For example, 1000000 might become \"1m\".\n  // Or 1024 might become \"1KB\" if we're formatting byte numbers.\n  //\n  // This section defines such suffixes for all such cases.\n  \"_big_number_suffix_3\": \"k\",\n  \"_big_number_suffix_6\": \"M\",\n  \"_big_number_suffix_9\": \"G\",\n  \"_big_number_suffix_12\": \"T\",\n  \"_big_number_suffix_15\": \"P\",\n  \"_big_number_suffix_18\": \"E\",\n  \"_big_number_suffix_21\": \"Z\",\n  \"_big_number_suffix_24\": \"Y\",\n\n  \"_small_number_suffix_3\": \"m\",\n  \"_small_number_suffix_6\": \"μ\",\n  \"_small_number_suffix_9\": \"n\",\n  \"_small_number_suffix_12\": \"p\",\n  \"_small_number_suffix_15\": \"f\",\n  \"_small_number_suffix_18\": \"a\",\n  \"_small_number_suffix_21\": \"z\",\n  \"_small_number_suffix_24\": \"y\",\n\n  \"_byte_suffix_B\": \"B\",\n  \"_byte_suffix_KB\": \"KB\",\n  \"_byte_suffix_MB\": \"MB\",\n  \"_byte_suffix_GB\": \"GB\",\n  \"_byte_suffix_TB\": \"TB\",\n  \"_byte_suffix_PB\": \"PB\",\n\n  // Default date formats for various periods.\n  //\n  // This should reflect official or de facto formatting universally accepted\n  // in the country translation is being made for\n  // Available format codes here:\n  // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n  //\n  // This will be used when formatting date/time for particular granularity,\n  // e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n  \"_date_millisecond\": \"mm:ss SSS\",\n  \"_date_second\": \"HH:mm:ss\",\n  \"_date_minute\": \"HH:mm\",\n  \"_date_hour\": \"HH:mm\",\n  \"_date_day\": \"MMM dd\",\n  \"_date_week\": \"ww\",\n  \"_date_month\": \"MMM\",\n  \"_date_year\": \"yyyy\",\n\n  // Default duration formats for various base units.\n  //\n  // This will be used by DurationFormatter to format numeric values into\n  // duration.\n  //\n  // Notice how each duration unit comes in several versions. This is to ensure\n  // that each base unit is shown correctly.\n  //\n  // For example, if we have baseUnit set to \"second\", meaning our duration is\n  // in seconds.\n  //\n  // If we pass in `50` to formatter, it will know that we have just 50 seconds\n  // (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\n  // and the formatted result will be in like `\"50\"`.\n  //\n  // If we pass in `70`, which is more than a minute, the formatter will switch\n  // to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\n  // text.\n  //\n  // Available codes here:\n  // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n  \"_duration_millisecond\": \"SSS\",\n  \"_duration_millisecond_second\": \"ss.SSS\",\n  \"_duration_millisecond_minute\": \"mm:ss SSS\",\n  \"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\n  \"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\n  \"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\n  \"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\n  \"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\n\n  \"_duration_second\": \"ss\",\n  \"_duration_second_minute\": \"mm:ss\",\n  \"_duration_second_hour\": \"hh:mm:ss\",\n  \"_duration_second_day\": \"d'd' hh:mm:ss\",\n  \"_duration_second_week\": \"d'd' hh:mm:ss\",\n  \"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\n  \"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\n\n  \"_duration_minute\": \"mm\",\n  \"_duration_minute_hour\": \"hh:mm\",\n  \"_duration_minute_day\": \"d'd' hh:mm\",\n  \"_duration_minute_week\": \"d'd' hh:mm\",\n  \"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\n  \"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\n\n  \"_duration_hour\": \"hh'h'\",\n  \"_duration_hour_day\": \"d'd' hh'h'\",\n  \"_duration_hour_week\": \"d'd' hh'h'\",\n  \"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\n  \"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\n\n  \"_duration_day\": \"d'd'\",\n  \"_duration_day_week\": \"d'd'\",\n  \"_duration_day_month\": \"M'm' dd'd'\",\n  \"_duration_day_year\": \"y'y' MM'm' dd'd'\",\n\n  \"_duration_week\": \"w'w'\",\n  \"_duration_week_month\": \"w'w'\",\n  \"_duration_week_year\": \"w'w'\",\n\n  \"_duration_month\": \"M'm'\",\n  \"_duration_month_year\": \"y'y' MM'm'\",\n\n  \"_duration_year\": \"y'y'\",\n\n  // Era translations\n  \"_era_ad\": \"e.Kr.\",\n  \"_era_bc\": \"f.Kr.\",\n\n  // Day part, used in 12-hour formats, e.g. 5 P.M.\n  // Please note that these come in 3 variants:\n  // * one letter (e.g. \"A\")\n  // * two letters (e.g. \"AM\")\n  // * two letters with dots (e.g. \"A.M.\")\n  //\n  // All three need to to be translated even if they are all the same. Some\n  // users might use one, some the other.\n  \"A\": \"a\",\n  \"P\": \"p\",\n  \"AM\": \"AM\",\n  \"PM\": \"PM\",\n  \"A.M.\": \"AM\",\n  \"P.M.\": \"PM\",\n\n  // Date-related stuff.\n  //\n  // When translating months, if there's a difference, use the form which is\n  // best for a full date, e.g. as you would use it in \"2018 January 1\".\n  //\n  // Note that May is listed twice. This is because in English May is the same\n  // in both long and short forms, while in other languages it may not be the\n  // case. Translate \"May\" to full word, while \"May(short)\" to shortened\n  // version.\n  //\n  // Should month names and weekdays be capitalized or not?\n  //\n  // Rule of thumb is this: if the names should always be capitalized,\n  // regardless of name position within date (\"January\", \"21st January 2018\",\n  // etc.) use capitalized names. Otherwise enter all lowercase.\n  //\n  // The date formatter will automatically capitalize names if they are the\n  // first (or only) word in resulting date.\n  \"January\": \"januar\",\n  \"February\": \"februar\",\n  \"March\": \"marts\",\n  \"April\": \"april\",\n  \"May\": \"maj\",\n  \"June\": \"juni\",\n  \"July\": \"juli\",\n  \"August\": \"august\",\n  \"September\": \"september\",\n  \"October\": \"oktober\",\n  \"November\": \"november\",\n  \"December\": \"december\",\n  \"Jan\": \"jan.\",\n  \"Feb\": \"feb.\",\n  \"Mar\": \"mar.\",\n  \"Apr\": \"apr.\",\n  \"May(short)\": \"maj\",\n  \"Jun\": \"jun.\",\n  \"Jul\": \"jul.\",\n  \"Aug\": \"aug.\",\n  \"Sep\": \"sep.\",\n  \"Oct\": \"okt.\",\n  \"Nov\": \"nov.\",\n  \"Dec\": \"dec.\",\n\n  // Weekdays.\n  \"Sunday\": \"søndag\",\n  \"Monday\": \"mandag\",\n  \"Tuesday\": \"tirsdag\",\n  \"Wednesday\": \"onsdag\",\n  \"Thursday\": \"torsdag\",\n  \"Friday\": \"fredag\",\n  \"Saturday\": \"lørdag\",\n  \"Sun\": \"søn.\",\n  \"Mon\": \"man.\",\n  \"Tue\": \"tir.\",\n  \"Wed\": \"ons.\",\n  \"Thu\": \"tor.\",\n  \"Fri\": \"fre.\",\n  \"Sat\": \"lør.\",\n\n  // Date ordinal function.\n  //\n  // This is used when adding number ordinal when formatting days in dates.\n  //\n  // E.g. \"January 1st\", \"February 2nd\".\n  //\n  // The function accepts day number, and returns a string to be added to the\n  // day, like in default English translation, if we pass in 2, we will receive\n  // \"nd\" back.\n  \"_dateOrd\": function(day: number): string {\n    let res = \"th\";\n    if ((day < 11) || (day > 13)) {\n      switch (day % 10) {\n        case 1:\n          res = \"st\";\n          break;\n        case 2:\n          res = \"nd\";\n          break;\n        case 3:\n          res = \"rd\"\n          break;\n      }\n    }\n    return res;\n  },\n\n  // Various chart controls.\n  // Shown as a tooltip on zoom out button.\n  \"Zoom Out\": \"Zoom\",\n\n  // Timeline buttons\n  \"Play\": \"Afspil\",\n  \"Stop\": \"Stop\",\n\n  // Chart's Legend screen reader title.\n  \"Legend\": \"Signaturforklaring\",\n\n  // Legend's item screen reader indicator.\n  \"Click, tap or press ENTER to toggle\": \"\",\n\n  // Shown when the chart is busy loading something.\n  \"Loading\": \"Indlæser\",\n\n  // Shown as the first button in the breadcrumb navigation, e.g.:\n  // Home > First level > ...\n  \"Home\": \"Hjem\",\n\n  // Chart types.\n  // Those are used as default screen reader titles for the main chart element\n  // unless developer has set some more descriptive title.\n  \"Chart\": \"\",\n  \"Serial chart\": \"\",\n  \"X/Y chart\": \"\",\n  \"Pie chart\": \"\",\n  \"Gauge chart\": \"\",\n  \"Radar chart\": \"\",\n  \"Sankey diagram\": \"\",\n  \"Flow diagram\": \"\",\n  \"Chord diagram\": \"\",\n  \"TreeMap chart\": \"\",\n  \"Sliced chart\": \"\",\n\n  // Series types.\n  // Used to name series by type for screen readers if they do not have their\n  // name set.\n  \"Series\": \"\",\n  \"Candlestick Series\": \"\",\n  \"OHLC Series\": \"\",\n  \"Column Series\": \"\",\n  \"Line Series\": \"\",\n  \"Pie Slice Series\": \"\",\n  \"Funnel Series\": \"\",\n  \"Pyramid Series\": \"\",\n  \"X/Y Series\": \"\",\n\n  // Map-related stuff.\n  \"Map\": \"\",\n  \"Press ENTER to zoom in\": \"\",\n  \"Press ENTER to zoom out\": \"\",\n  \"Use arrow keys to zoom in and out\": \"\",\n  \"Use plus and minus keys on your keyboard to zoom in and out\": \"\",\n\n  // Export-related stuff.\n  // These prompts are used in Export menu labels.\n  //\n  // \"Export\" is the top-level menu item.\n  //\n  // \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n  // operation.\n  //\n  // Leave actual format untranslated, unless you absolutely know that they\n  // would convey more meaning in some other way.\n  \"Export\": \"Udskriv\",\n  \"Image\": \"Billede\",\n  \"Data\": \"Data\",\n  \"Print\": \"Udskriv\",\n  \"Click, tap or press ENTER to open\": \"\",\n  \"Click, tap or press ENTER to print.\": \"\",\n  \"Click, tap or press ENTER to export as %1.\": \"\",\n  'To save the image, right-click this link and choose \"Save picture as...\"': \"\",\n  'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': \"\",\n  \"(Press ESC to close this message)\": \"\",\n  \"Image Export Complete\": \"\",\n  \"Export operation took longer than expected. Something might have gone wrong.\": \"\",\n  \"Saved from\": \"\",\n  \"PNG\": \"\",\n  \"JPG\": \"\",\n  \"GIF\": \"\",\n  \"SVG\": \"\",\n  \"PDF\": \"\",\n  \"JSON\": \"\",\n  \"CSV\": \"\",\n  \"XLSX\": \"\",\n\n  // Scrollbar-related stuff.\n  //\n  // Scrollbar is a control which can zoom and pan the axes on the chart.\n  //\n  // Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n  // upper and lower (for vertical one).\n  //\n  // Prompts change in relation to whether Scrollbar is vertical or horizontal.\n  //\n  // The final section is used to indicate the current range of selection.\n  \"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\n  \"Use left and right arrows to move selection\": \"\",\n  \"Use left and right arrows to move left selection\": \"\",\n  \"Use left and right arrows to move right selection\": \"\",\n  \"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\n  \"Use up and down arrows to move selection\": \"\",\n  \"Use up and down arrows to move lower selection\": \"\",\n  \"Use up and down arrows to move upper selection\": \"\",\n  \"From %1 to %2\": \"Fra %1 til %2\",\n  \"From %1\": \"Fra %1\",\n  \"To %1\": \"Til %1\",\n\n  // Data loader-related.\n  \"No parser available for file: %1\": \"\",\n  \"Error parsing file: %1\": \"\",\n  \"Unable to load file: %1\": \"\",\n  \"Invalid date\": \"\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/da_DK.ts"], "sourceRoot": ""}