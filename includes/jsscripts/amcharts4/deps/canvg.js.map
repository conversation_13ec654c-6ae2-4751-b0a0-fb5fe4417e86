{"version": 3, "sources": ["webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/iterableToArrayLimit.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/stackblur-canvas/dist/stackblur-es.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/rgbcolor/index.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/arrayLikeToArray.js", "webpack:///../node_modules/regenerator-runtime/runtime.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/assertThisInitialized.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/regenerator/index.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/get.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/asyncToGenerator.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/arrayWithHoles.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/superPropBase.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/slicedToArray.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/inherits.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/nonIterableSpread.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/getPrototypeOf.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/toConsumableArray.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/regenerator-runtime/runtime.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/nonIterableRest.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/process/browser.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/iterableToArray.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/performance-now/src/performance-now.coffee", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/createClass.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/raf/index.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/defineProperty.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/setPrototypeOf.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/classCallCheck.js", "webpack:///C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/typeof.js"], "names": ["module", "exports", "arr", "i", "Symbol", "iterator", "Object", "_arr", "_n", "_d", "_e", "undefined", "_s", "_i", "next", "done", "push", "value", "length", "err", "_typeof", "obj", "constructor", "prototype", "__webpack_require__", "d", "__webpack_exports__", "processCanvasRGBA", "mulTable", "shgTable", "getImageDataFromCanvas", "canvas", "topX", "topY", "width", "height", "document", "getElementById", "TypeError", "context", "getContext", "getImageData", "e", "Error", "radius", "isNaN", "imageData", "x", "y", "p", "yp", "yi", "yw", "rSum", "gSum", "bSum", "aSum", "rOutSum", "gOutSum", "bOutSum", "aOutSum", "rInSum", "gInSum", "bInSum", "aInSum", "pr", "pg", "pb", "pa", "rbs", "stackEnd", "pixels", "data", "div", "widthMinus1", "heightMinus1", "radiusPlus1", "sumFactor", "stackStart", "BlurStack", "stack", "stackIn", "stackOut", "mulSum", "shgSum", "r", "g", "b", "a", "processImageDataRGBA", "putImageData", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck", "this", "color_string", "ok", "alpha", "char<PERSON>t", "substr", "replace", "toLowerCase", "simple_colors", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "dodgerblue", "feldspar", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslateblue", "lightslategray", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "violetred", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "color_defs", "re", "example", "process", "bits", "parseInt", "parseFloat", "processor", "exec", "channels", "toRGB", "toRGBA", "toHex", "toString", "getHelpXML", "examples", "Array", "j", "sc", "xml", "createElement", "setAttribute", "list_item", "list_color", "RGBColor", "example_div", "style", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "list_item_value", "len", "arr2", "runtime", "Op", "hasOwn", "hasOwnProperty", "$Symbol", "iteratorSymbol", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "create", "Context", "_invoke", "state", "GenStateSuspendedStart", "method", "arg", "GenStateExecuting", "GenStateCompleted", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "ContinueSentinel", "sent", "_sent", "dispatchException", "abrupt", "record", "tryCatch", "type", "GenStateSuspendedYield", "makeInvokeMethod", "fn", "call", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "AsyncIterator", "PromiseImpl", "previousPromise", "callInvokeWithMethodAndArg", "resolve", "reject", "invoke", "result", "__await", "then", "unwrapped", "error", "info", "resultName", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "object", "key", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "regeneratorRuntime", "accidentalStrictMode", "Function", "ReferenceError", "superPropBase", "_get", "target", "property", "receiver", "Reflect", "get", "base", "desc", "getOwnPropertyDescriptor", "asyncGeneratorStep", "gen", "_next", "_throw", "args", "arguments", "apply", "isArray", "arrayWithHoles", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest", "subClass", "superClass", "writable", "configurable", "_getPrototypeOf", "o", "arrayWithoutHoles", "iterableToArray", "nonIterableSpread", "arrayLikeToArray", "minLen", "n", "from", "test", "cachedSetTimeout", "cachedClearTimeout", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "setTimeout", "clearTimeout", "currentQueue", "queue", "draining", "queueIndex", "cleanUpNextTick", "concat", "drainQueue", "timeout", "run", "marker", "runClearTimeout", "<PERSON><PERSON>", "array", "noop", "nextTick", "title", "browser", "env", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "binding", "cwd", "chdir", "dir", "umask", "getNanoSeconds", "hrtime", "loadTime", "moduleLoadTime", "nodeLoadTime", "upTime", "performance", "now", "hr", "uptime", "Date", "getTime", "assertThisInitialized", "_defineProperties", "props", "descriptor", "enumerable", "defineProperty", "protoProps", "staticProps", "global", "root", "window", "vendors", "suffix", "raf", "caf", "last", "id", "callback", "_now", "Math", "max", "cp", "cancelled", "round", "cancel", "polyfill", "requestAnimationFrame", "cancelAnimationFrame", "_setPrototypeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;wDA2BAA,EAAAC,QA3BA,SAAAC,EAAAC,GACA,uBAAAC,eAAAC,YAAAC,OAAAJ,GAAA,CACA,IAAAK,KACAC,GAAA,EACAC,GAAA,EACAC,OAAAC,EAEA,IACA,QAAAC,EAAAC,EAAAX,EAAAE,OAAAC,cAA6CG,GAAAI,EAAAC,EAAAC,QAAAC,QAC7CR,EAAAS,KAAAJ,EAAAK,QAEAd,GAAAI,EAAAW,SAAAf,GAH4EK,GAAA,IAKzE,MAAAW,GACHV,GAAA,EACAC,EAAAS,EACG,QACH,IACAX,GAAA,MAAAK,EAAA,QAAAA,EAAA,SACK,QACL,GAAAJ,EAAA,MAAAC,GAIA,OAAAH,yCCxBA,SAAAa,EAAAC,GAWA,OATAD,EADA,mBAAAhB,QAAA,iBAAAA,OAAAC,SACA,SAAAgB,GACA,cAAAA,GAGA,SAAAA,GACA,OAAAA,GAAA,mBAAAjB,QAAAiB,EAAAC,cAAAlB,QAAAiB,IAAAjB,OAAAmB,UAAA,gBAAAF,IAIAA,GAXAG,EAAAC,EAAAC,EAAA,sBAAAC,IA2DA,IAAAC,GAAA,6/BACAC,GAAA,6vBA0DA,SAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAKA,GAJA,iBAAAJ,IACAA,EAAAK,SAAAC,eAAAN,MAGAA,GAAA,WAAAX,EAAAW,IAAA,eAAAA,GACA,UAAAO,UAAA,2EAGA,IAAAC,EAAAR,EAAAS,WAAA,MAEA,IACA,OAAAD,EAAAE,aAAAT,EAAAC,EAAAC,EAAAC,GACG,MAAAO,GACH,UAAAC,MAAA,gCAAAD,IAcA,SAAAf,EAAAI,EAAAC,EAAAC,EAAAC,EAAAC,EAAAS,GACA,KAAAC,MAAAD,MAAA,IAIAA,GAAA,EACA,IAAAE,EAAAhB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GACAW,EAcA,SAAAA,EAAAd,EAAAC,EAAAC,EAAAC,EAAAS,GACA,IACAG,EAAAC,EAAA7C,EAAA8C,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EASAC,EAVAC,EAAAzB,EAAA0B,KAEAC,EAAA,EAAA7B,EAAA,EAEA8B,EAAAxC,EAAA,EACAyC,EAAAxC,EAAA,EACAyC,EAAAhC,EAAA,EACAiC,EAAAD,KAAA,KACAE,EAAA,IAAAC,EACAC,EAAAF,EAGA,IAAA3E,EAAA,EAAaA,EAAAsE,EAAStE,IACtB6E,IAAAlE,KAAA,IAAAiE,EAEA5E,IAAAyE,IACAN,EAAAU,GAIAA,EAAAlE,KAAAgE,EACA,IAAAG,EAAA,KACAC,EAAA,KACA9B,EAAAD,EAAA,EACA,IAAAgC,EAAAvD,EAAAgB,GACAwC,EAAAvD,EAAAe,GAEA,IAAAI,EAAA,EAAaA,EAAAb,EAAYa,IAAA,CAYzB,IAXAa,EAAAC,EAAAC,EAAAC,EAAAX,EAAAC,EAAAC,EAAAC,EAAA,EACAC,EAAAmB,GAAAX,EAAAM,EAAApB,IACAO,EAAAkB,GAAAV,EAAAK,EAAApB,EAAA,IACAQ,EAAAiB,GAAAT,EAAAI,EAAApB,EAAA,IACAS,EAAAgB,GAAAR,EAAAG,EAAApB,EAAA,IACAE,GAAAwB,EAAAZ,EACAX,GAAAuB,EAAAX,EACAX,GAAAsB,EAAAV,EACAX,GAAAqB,EAAAT,EACAY,EAAAF,EAEA3E,EAAA,EAAeA,EAAAyE,EAAiBzE,IAChC6E,EAAAK,EAAApB,EACAe,EAAAM,EAAApB,EACAc,EAAAO,EAAApB,EACAa,EAAAQ,EAAApB,EACAY,IAAAlE,KAGA,IAAAX,EAAA,EAAeA,EAAAyE,EAAiBzE,IAChC8C,EAAAE,IAAAuB,EAAAvE,EAAAuE,EAAAvE,IAAA,GACAkD,IAAA2B,EAAAK,EAAApB,EAAAM,EAAAtB,KAAAoB,EAAAO,EAAAzE,GACAmD,IAAA0B,EAAAM,EAAApB,EAAAK,EAAAtB,EAAA,IAAAoB,EACAd,IAAAyB,EAAAO,EAAApB,EAAAI,EAAAtB,EAAA,IAAAoB,EACAb,IAAAwB,EAAAQ,EAAApB,EAAAG,EAAAtB,EAAA,IAAAoB,EACAR,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAY,IAAAlE,KAMA,IAHAmE,EAAAH,EACAI,EAAAZ,EAEAvB,EAAA,EAAeA,EAAAb,EAAWa,IAC1BwB,EAAApB,EAAA,GAAAiB,EAAAZ,EAAA2B,GAAAC,EAEA,IAAAhB,GACAA,EAAA,IAAAA,EACAG,EAAApB,IAAAE,EAAA8B,GAAAC,GAAAhB,EACAG,EAAApB,EAAA,IAAAG,EAAA6B,GAAAC,GAAAhB,EACAG,EAAApB,EAAA,IAAAI,EAAA4B,GAAAC,GAAAhB,GAEAG,EAAApB,GAAAoB,EAAApB,EAAA,GAAAoB,EAAApB,EAAA,KAGAE,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAwB,EAAAI,EACA3B,GAAAuB,EAAAK,EACA3B,GAAAsB,EAAAM,EACA3B,GAAAqB,EAAAO,EACAvC,EAAAG,IAAAH,EAAAF,EAAAH,EAAA,GAAA8B,EAAAzB,EAAAyB,IAAA,EACAb,GAAAoB,EAAAI,EAAAd,EAAAtB,GACAa,GAAAmB,EAAAK,EAAAf,EAAAtB,EAAA,GACAc,GAAAkB,EAAAM,EAAAhB,EAAAtB,EAAA,GACAe,GAAAiB,EAAAO,EAAAjB,EAAAtB,EAAA,GACAI,GAAAQ,EACAP,GAAAQ,EACAP,GAAAQ,EACAP,GAAAQ,EACAiB,IAAAnE,KACA2C,GAAAQ,EAAAiB,EAAAG,EACA3B,GAAAQ,EAAAgB,EAAAI,EACA3B,GAAAQ,EAAAe,EAAAK,EACA3B,GAAAQ,EAAAc,EAAAM,EACA3B,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAc,IAAApE,KACAqC,GAAA,EAGAC,GAAAlB,EAGA,IAAAa,EAAA,EAAaA,EAAAb,EAAWa,IAAA,CAaxB,IAZAe,EAAAC,EAAAC,EAAAH,EAAAP,EAAAC,EAAAC,EAAAH,EAAA,EAEAI,EAAAmB,GAAAX,EAAAM,EADApB,EAAAJ,GAAA,IAEAW,EAAAkB,GAAAV,EAAAK,EAAApB,EAAA,IACAQ,EAAAiB,GAAAT,EAAAI,EAAApB,EAAA,IACAS,EAAAgB,GAAAR,EAAAG,EAAApB,EAAA,IACAE,GAAAwB,EAAAZ,EACAX,GAAAuB,EAAAX,EACAX,GAAAsB,EAAAV,EACAX,GAAAqB,EAAAT,EACAY,EAAAF,EAEA3E,EAAA,EAAeA,EAAAyE,EAAiBzE,IAChC6E,EAAAK,EAAApB,EACAe,EAAAM,EAAApB,EACAc,EAAAO,EAAApB,EACAa,EAAAQ,EAAApB,EACAY,IAAAlE,KAKA,IAFAoC,EAAAhB,EAEA/B,EAAA,EAAeA,GAAAyC,EAAazC,IAC5BgD,EAAAD,EAAAH,GAAA,EACAM,IAAA2B,EAAAK,EAAApB,EAAAM,EAAApB,KAAAkB,EAAAO,EAAAzE,GACAmD,IAAA0B,EAAAM,EAAApB,EAAAK,EAAApB,EAAA,IAAAkB,EACAd,IAAAyB,EAAAO,EAAApB,EAAAI,EAAApB,EAAA,IAAAkB,EACAb,IAAAwB,EAAAQ,EAAApB,EAAAG,EAAApB,EAAA,IAAAkB,EACAR,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAY,IAAAlE,KAEAX,EAAAwE,IACAzB,GAAAhB,GAQA,IAJAiB,EAAAJ,EACAkC,EAAAH,EACAI,EAAAZ,EAEAtB,EAAA,EAAeA,EAAAb,EAAYa,IAE3BuB,GADAtB,EAAAE,GAAA,GACA,GAAAiB,EAAAZ,EAAA2B,GAAAC,EAEAhB,EAAA,GACAA,EAAA,IAAAA,EACAG,EAAAtB,IAAAI,EAAA8B,GAAAC,GAAAhB,EACAG,EAAAtB,EAAA,IAAAK,EAAA6B,GAAAC,GAAAhB,EACAG,EAAAtB,EAAA,IAAAM,EAAA4B,GAAAC,GAAAhB,GAEAG,EAAAtB,GAAAsB,EAAAtB,EAAA,GAAAsB,EAAAtB,EAAA,KAGAI,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAwB,EAAAI,EACA3B,GAAAuB,EAAAK,EACA3B,GAAAsB,EAAAM,EACA3B,GAAAqB,EAAAO,EACAvC,EAAAF,IAAAE,EAAAD,EAAA4B,GAAAD,EAAA1B,EAAA0B,GAAAzC,GAAA,EACAmB,GAAAQ,GAAAoB,EAAAI,EAAAd,EAAAtB,GACAK,GAAAQ,GAAAmB,EAAAK,EAAAf,EAAAtB,EAAA,GACAM,GAAAQ,GAAAkB,EAAAM,EAAAhB,EAAAtB,EAAA,GACAO,GAAAQ,GAAAiB,EAAAO,EAAAjB,EAAAtB,EAAA,GACAgC,IAAAnE,KACA2C,GAAAQ,EAAAiB,EAAAG,EACA3B,GAAAQ,EAAAgB,EAAAI,EACA3B,GAAAQ,EAAAe,EAAAK,EACA3B,GAAAQ,EAAAc,EAAAM,EACA3B,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAH,GAAAI,EACAc,IAAApE,KACAqC,GAAAjB,EAIA,OAAAY,EA/MA2C,CAAA3C,EAAAd,EAAAC,EAAAC,EAAAC,EAAAS,GACAb,EAAAS,WAAA,MAAAkD,aAAA5C,EAAAd,EAAAC,IAmZA,IAAA8C,EAAA,SAAAA,KA/hBA,SAAAY,EAAAC,GACA,KAAAD,aAAAC,GACA,UAAAtD,UAAA,qCA8hBAuD,CAAAC,KAAAf,GAEAe,KAAAT,EAAA,EACAS,KAAAR,EAAA,EACAQ,KAAAP,EAAA,EACAO,KAAAN,EAAA,EACAM,KAAAhF,KAAA,4BC/iBAd,EAAAC,QAAA,SAAA8F,GACAD,KAAAE,IAAA,EACAF,KAAAG,MAAA,EAGA,KAAAF,EAAAG,OAAA,KACAH,IAAAI,OAAA,MAIAJ,GADAA,IAAAK,QAAA,UACAC,cAIA,IAAAC,GACAC,UAAA,SACAC,aAAA,SACAC,KAAA,SACAC,WAAA,SACAC,MAAA,SACAC,MAAA,SACAC,OAAA,SACAC,MAAA,SACAC,eAAA,SACAC,KAAA,SACAC,WAAA,SACAC,MAAA,SACAC,UAAA,SACAC,UAAA,SACAC,WAAA,SACAC,UAAA,SACAC,MAAA,SACAC,eAAA,SACAC,SAAA,SACAC,QAAA,SACAC,KAAA,SACAC,SAAA,SACAC,SAAA,SACAC,cAAA,SACAC,SAAA,SACAC,UAAA,SACAC,UAAA,SACAC,YAAA,SACAC,eAAA,SACAC,WAAA,SACAC,WAAA,SACAC,QAAA,SACAC,WAAA,SACAC,aAAA,SACAC,cAAA,SACAC,cAAA,SACAC,cAAA,SACAC,WAAA,SACAC,SAAA,SACAC,YAAA,SACAC,QAAA,SACAC,WAAA,SACAC,SAAA,SACAC,UAAA,SACAC,YAAA,SACAC,YAAA,SACAC,QAAA,SACAC,UAAA,SACAC,WAAA,SACAC,KAAA,SACAC,UAAA,SACAC,KAAA,SACAC,MAAA,SACAC,YAAA,SACAC,SAAA,SACAC,QAAA,SACAC,UAAA,SACAC,OAAA,SACAC,MAAA,SACAC,MAAA,SACAC,SAAA,SACAC,cAAA,SACAC,UAAA,SACAC,aAAA,SACAC,UAAA,SACAC,WAAA,SACAC,UAAA,SACAC,qBAAA,SACAC,UAAA,SACAC,WAAA,SACAC,UAAA,SACAC,YAAA,SACAC,cAAA,SACAC,aAAA,SACAC,eAAA,SACAC,eAAA,SACAC,eAAA,SACAC,YAAA,SACAC,KAAA,SACAC,UAAA,SACAC,MAAA,SACAC,QAAA,SACAC,OAAA,SACAC,iBAAA,SACAC,WAAA,SACAC,aAAA,SACAC,aAAA,SACAC,eAAA,SACAC,gBAAA,SACAC,kBAAA,SACAC,gBAAA,SACAC,gBAAA,SACAC,aAAA,SACAC,UAAA,SACAC,UAAA,SACAC,SAAA,SACAC,YAAA,SACAC,KAAA,SACAC,QAAA,SACAC,MAAA,SACAC,UAAA,SACAC,OAAA,SACAC,UAAA,SACAC,OAAA,SACAC,cAAA,SACAC,UAAA,SACAC,cAAA,SACAC,cAAA,SACAC,WAAA,SACAC,UAAA,SACAC,KAAA,SACAC,KAAA,SACAC,KAAA,SACAC,WAAA,SACAC,OAAA,SACAC,cAAA,SACAC,IAAA,SACAC,UAAA,SACAC,UAAA,SACAC,YAAA,SACAC,OAAA,SACAC,WAAA,SACAC,SAAA,SACAC,SAAA,SACAC,OAAA,SACAC,OAAA,SACAC,QAAA,SACAC,UAAA,SACAC,UAAA,SACAC,KAAA,SACAC,YAAA,SACAC,UAAA,SACAC,IAAA,SACAC,KAAA,SACAC,QAAA,SACAC,OAAA,SACAC,UAAA,SACAC,OAAA,SACAC,UAAA,SACAC,MAAA,SACAC,MAAA,SACAC,WAAA,SACAC,OAAA,SACAC,YAAA,UAEAvJ,EAAAO,EAAAP,MAqDA,IAjDA,IAAAwJ,IAEAC,GAAA,kEACAC,SAAA,mDACAC,QAAA,SAAAC,GACA,OACAC,SAAAD,EAAA,IACAC,SAAAD,EAAA,IACAC,SAAAD,EAAA,IACAE,WAAAF,EAAA,QAKAH,GAAA,+CACAC,SAAA,wCACAC,QAAA,SAAAC,GACA,OACAC,SAAAD,EAAA,IACAC,SAAAD,EAAA,IACAC,SAAAD,EAAA,QAKAH,GAAA,qDACAC,SAAA,oBACAC,QAAA,SAAAC,GACA,OACAC,SAAAD,EAAA,OACAC,SAAAD,EAAA,OACAC,SAAAD,EAAA,WAKAH,GAAA,qDACAC,SAAA,cACAC,QAAA,SAAAC,GACA,OACAC,SAAAD,EAAA,GAAAA,EAAA,OACAC,SAAAD,EAAA,GAAAA,EAAA,OACAC,SAAAD,EAAA,GAAAA,EAAA,WAOAxP,EAAA,EAAmBA,EAAAoP,EAAArO,OAAuBf,IAAA,CAC1C,IAAAqP,EAAAD,EAAApP,GAAAqP,GACAM,EAAAP,EAAApP,GAAAuP,QACAC,EAAAH,EAAAO,KAAAhK,GACA,GAAA4J,EAAA,CACA,IAAAK,EAAAF,EAAAH,GACA7J,KAAAT,EAAA2K,EAAA,GACAlK,KAAAR,EAAA0K,EAAA,GACAlK,KAAAP,EAAAyK,EAAA,GACAA,EAAA9O,OAAA,IACA4E,KAAAG,MAAA+J,EAAA,IAEAlK,KAAAE,IAAA,GAMAF,KAAAT,EAAAS,KAAAT,EAAA,GAAAxC,MAAAiD,KAAAT,GAAA,EAAAS,KAAAT,EAAA,QAAAS,KAAAT,EACAS,KAAAR,EAAAQ,KAAAR,EAAA,GAAAzC,MAAAiD,KAAAR,GAAA,EAAAQ,KAAAR,EAAA,QAAAQ,KAAAR,EACAQ,KAAAP,EAAAO,KAAAP,EAAA,GAAA1C,MAAAiD,KAAAP,GAAA,EAAAO,KAAAP,EAAA,QAAAO,KAAAP,EACAO,KAAAG,MAAAH,KAAAG,MAAA,IAAAH,KAAAG,MAAA,GAAApD,MAAAiD,KAAAG,OAAA,EAAAH,KAAAG,MAGAH,KAAAmK,MAAA,WACA,aAAAnK,KAAAT,EAAA,KAAAS,KAAAR,EAAA,KAAAQ,KAAAP,EAAA,KAEAO,KAAAoK,OAAA,WACA,cAAApK,KAAAT,EAAA,KAAAS,KAAAR,EAAA,KAAAQ,KAAAP,EAAA,KAAAO,KAAAG,MAAA,KAEAH,KAAAqK,MAAA,WACA,IAAA9K,EAAAS,KAAAT,EAAA+K,SAAA,IACA9K,EAAAQ,KAAAR,EAAA8K,SAAA,IACA7K,EAAAO,KAAAP,EAAA6K,SAAA,IAIA,OAHA,GAAA/K,EAAAnE,SAAAmE,EAAA,IAAAA,GACA,GAAAC,EAAApE,SAAAoE,EAAA,IAAAA,GACA,GAAAC,EAAArE,SAAAqE,EAAA,IAAAA,GACA,IAAAF,EAAAC,EAAAC,GAIAO,KAAAuK,WAAA,WAIA,IAFA,IAAAC,EAAA,IAAAC,MAEApQ,EAAA,EAAuBA,EAAAoP,EAAArO,OAAuBf,IAE9C,IADA,IAAAsP,EAAAF,EAAApP,GAAAsP,QACAe,EAAA,EAA2BA,EAAAf,EAAAvO,OAAoBsP,IAC/CF,IAAApP,QAAAuO,EAAAe,GAIA,QAAAC,KAAAnK,EACAgK,IAAApP,QAAAuP,EAGA,IAAAC,EAAAtO,SAAAuO,cAAA,MACAD,EAAAE,aAAA,0BACA,IAAAzQ,EAAA,EAAuBA,EAAAmQ,EAAApP,OAAqBf,IAC5C,IACA,IAAA0Q,EAAAzO,SAAAuO,cAAA,MACAG,EAAA,IAAAC,SAAAT,EAAAnQ,IACA6Q,EAAA5O,SAAAuO,cAAA,OACAK,EAAAC,MAAAC,QACA,oDAEAJ,EAAAX,QAAA,WACAW,EAAAX,QAEAa,EAAAG,YAAA/O,SAAAgP,eAAA,SACA,IAAAC,EAAAjP,SAAAgP,eACA,IAAAd,EAAAnQ,GAAA,OAAA2Q,EAAAb,QAAA,OAAAa,EAAAX,SAEAU,EAAAM,YAAAH,GACAH,EAAAM,YAAAE,GACAX,EAAAS,YAAAN,GAEa,MAAAnO,IAEb,OAAAgO,0BC/RA1Q,EAAAC,QAVA,SAAAC,EAAAoR,IACA,MAAAA,KAAApR,EAAAgB,UAAAoQ,EAAApR,EAAAgB,QAEA,QAAAf,EAAA,EAAAoR,EAAA,IAAAhB,MAAAe,GAAwCnR,EAAAmR,EAASnR,IACjDoR,EAAApR,GAAAD,EAAAC,GAGA,OAAAoR,2sNCAA,IAAIC,EAAW,SAAUvR,GAGvB,IAEIU,EAFA8Q,EAAKnR,OAAOiB,UACZmQ,EAASD,EAAGE,eAEZC,EAA4B,mBAAXxR,OAAwBA,UACzCyR,EAAiBD,EAAQvR,UAAY,aACrCyR,EAAsBF,EAAQG,eAAiB,kBAC/CC,EAAoBJ,EAAQK,aAAe,gBAE/C,SAASC,EAAKC,EAASC,EAASC,EAAMC,GAEpC,IAAIC,EAAiBH,GAAWA,EAAQ7Q,qBAAqBiR,EAAYJ,EAAUI,EAC/EC,EAAYnS,OAAOoS,OAAOH,EAAehR,WACzCgB,EAAU,IAAIoQ,EAAQL,OAM1B,OAFAG,EAAUG,QAqMZ,SAA0BT,EAASE,EAAM9P,GACvC,IAAIsQ,EAAQC,EAEZ,OAAO,SAAgBC,EAAQC,GAC7B,GAAIH,IAAUI,EACZ,MAAM,IAAItQ,MAAM,gCAGlB,GAAIkQ,IAAUK,EAAmB,CAC/B,GAAe,UAAXH,EACF,MAAMC,EAKR,OAAOG,IAMT,IAHA5Q,EAAQwQ,OAASA,EACjBxQ,EAAQyQ,IAAMA,IAED,CACX,IAAII,EAAW7Q,EAAQ6Q,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAU7Q,GACnD,GAAI8Q,EAAgB,CAClB,GAAIA,IAAmBE,EAAkB,SACzC,OAAOF,GAIX,GAAuB,SAAnB9Q,EAAQwQ,OAGVxQ,EAAQiR,KAAOjR,EAAQkR,MAAQlR,EAAQyQ,SAElC,GAAuB,UAAnBzQ,EAAQwQ,OAAoB,CACrC,GAAIF,IAAUC,EAEZ,MADAD,EAAQK,EACF3Q,EAAQyQ,IAGhBzQ,EAAQmR,kBAAkBnR,EAAQyQ,SAEN,WAAnBzQ,EAAQwQ,QACjBxQ,EAAQoR,OAAO,SAAUpR,EAAQyQ,KAGnCH,EAAQI,EAER,IAAIW,EAASC,EAAS1B,EAASE,EAAM9P,GACrC,GAAoB,WAAhBqR,EAAOE,KAAmB,CAO5B,GAJAjB,EAAQtQ,EAAQxB,KACZmS,EACAa,EAEAH,EAAOZ,MAAQO,EACjB,SAGF,OACEtS,MAAO2S,EAAOZ,IACdjS,KAAMwB,EAAQxB,MAGS,UAAhB6S,EAAOE,OAChBjB,EAAQK,EAGR3Q,EAAQwQ,OAAS,QACjBxQ,EAAQyQ,IAAMY,EAAOZ,OA7QPgB,CAAiB7B,EAASE,EAAM9P,GAE7CkQ,EAcT,SAASoB,EAASI,EAAI5S,EAAK2R,GACzB,IACE,OAASc,KAAM,SAAUd,IAAKiB,EAAGC,KAAK7S,EAAK2R,IAC3C,MAAO7R,GACP,OAAS2S,KAAM,QAASd,IAAK7R,IAhBjClB,EAAQiS,KAAOA,EAoBf,IAAIY,EAAyB,iBACzBiB,EAAyB,iBACzBd,EAAoB,YACpBC,EAAoB,YAIpBK,KAMJ,SAASf,KACT,SAAS2B,KACT,SAASC,KAIT,IAAIC,KACJA,EAAkBxC,GAAkB,WAClC,OAAO/L,MAGT,IAAIwO,EAAWhU,OAAOiU,eAClBC,EAA0BF,GAAYA,EAASA,EAASG,QACxDD,GACAA,IAA4B/C,GAC5BC,EAAOwC,KAAKM,EAAyB3C,KAGvCwC,EAAoBG,GAGtB,IAAIE,EAAKN,EAA2B7S,UAClCiR,EAAUjR,UAAYjB,OAAOoS,OAAO2B,GAQtC,SAASM,EAAsBpT,IAC5B,OAAQ,QAAS,UAAUqT,QAAQ,SAAS7B,GAC3CxR,EAAUwR,GAAU,SAASC,GAC3B,OAAOlN,KAAK8M,QAAQG,EAAQC,MAoClC,SAAS6B,EAAcpC,EAAWqC,GAgChC,IAAIC,EAgCJjP,KAAK8M,QA9BL,SAAiBG,EAAQC,GACvB,SAASgC,IACP,OAAO,IAAIF,EAAY,SAASG,EAASC,IAnC7C,SAASC,EAAOpC,EAAQC,EAAKiC,EAASC,GACpC,IAAItB,EAASC,EAASpB,EAAUM,GAASN,EAAWO,GACpD,GAAoB,UAAhBY,EAAOE,KAEJ,CACL,IAAIsB,EAASxB,EAAOZ,IAChB/R,EAAQmU,EAAOnU,MACnB,OAAIA,GACiB,iBAAVA,GACPyQ,EAAOwC,KAAKjT,EAAO,WACd6T,EAAYG,QAAQhU,EAAMoU,SAASC,KAAK,SAASrU,GACtDkU,EAAO,OAAQlU,EAAOgU,EAASC,IAC9B,SAAS/T,GACVgU,EAAO,QAAShU,EAAK8T,EAASC,KAI3BJ,EAAYG,QAAQhU,GAAOqU,KAAK,SAASC,GAI9CH,EAAOnU,MAAQsU,EACfN,EAAQG,IACP,SAASI,GAGV,OAAOL,EAAO,QAASK,EAAOP,EAASC,KAvBzCA,EAAOtB,EAAOZ,KAiCZmC,CAAOpC,EAAQC,EAAKiC,EAASC,KAIjC,OAAOH,EAaLA,EAAkBA,EAAgBO,KAChCN,EAGAA,GACEA,KAkHV,SAAS1B,EAAoBF,EAAU7Q,GACrC,IAAIwQ,EAASK,EAAS/S,SAASkC,EAAQwQ,QACvC,GAAIA,IAAWpS,EAAW,CAKxB,GAFA4B,EAAQ6Q,SAAW,KAEI,UAAnB7Q,EAAQwQ,OAAoB,CAE9B,GAAIK,EAAS/S,SAAT,SAGFkC,EAAQwQ,OAAS,SACjBxQ,EAAQyQ,IAAMrS,EACd2S,EAAoBF,EAAU7Q,GAEP,UAAnBA,EAAQwQ,QAGV,OAAOQ,EAIXhR,EAAQwQ,OAAS,QACjBxQ,EAAQyQ,IAAM,IAAI1Q,UAChB,kDAGJ,OAAOiR,EAGT,IAAIK,EAASC,EAASd,EAAQK,EAAS/S,SAAUkC,EAAQyQ,KAEzD,GAAoB,UAAhBY,EAAOE,KAIT,OAHAvR,EAAQwQ,OAAS,QACjBxQ,EAAQyQ,IAAMY,EAAOZ,IACrBzQ,EAAQ6Q,SAAW,KACZG,EAGT,IAAIkC,EAAO7B,EAAOZ,IAElB,OAAMyC,EAOFA,EAAK1U,MAGPwB,EAAQ6Q,EAASsC,YAAcD,EAAKxU,MAGpCsB,EAAQzB,KAAOsS,EAASuC,QAQD,WAAnBpT,EAAQwQ,SACVxQ,EAAQwQ,OAAS,OACjBxQ,EAAQyQ,IAAMrS,GAUlB4B,EAAQ6Q,SAAW,KACZG,GANEkC,GA3BPlT,EAAQwQ,OAAS,QACjBxQ,EAAQyQ,IAAM,IAAI1Q,UAAU,oCAC5BC,EAAQ6Q,SAAW,KACZG,GAoDX,SAASqC,EAAaC,GACpB,IAAIC,GAAUC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxB/P,KAAKqQ,WAAWnV,KAAK8U,GAGvB,SAASM,EAAcN,GACrB,IAAIlC,EAASkC,EAAMO,eACnBzC,EAAOE,KAAO,gBACPF,EAAOZ,IACd8C,EAAMO,WAAazC,EAGrB,SAASjB,EAAQL,GAIfxM,KAAKqQ,aAAgBJ,OAAQ,SAC7BzD,EAAYsC,QAAQgB,EAAc9P,MAClCA,KAAKwQ,OAAM,GA8Bb,SAAS7B,EAAO8B,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAAS1E,GAC9B,GAAI2E,EACF,OAAOA,EAAetC,KAAKqC,GAG7B,GAA6B,mBAAlBA,EAASzV,KAClB,OAAOyV,EAGT,IAAK1T,MAAM0T,EAASrV,QAAS,CAC3B,IAAIf,GAAK,EAAGW,EAAO,SAASA,IAC1B,OAASX,EAAIoW,EAASrV,QACpB,GAAIwQ,EAAOwC,KAAKqC,EAAUpW,GAGxB,OAFAW,EAAKG,MAAQsV,EAASpW,GACtBW,EAAKC,MAAO,EACLD,EAOX,OAHAA,EAAKG,MAAQN,EACbG,EAAKC,MAAO,EAELD,GAGT,OAAOA,EAAKA,KAAOA,GAKvB,OAASA,KAAMqS,GAIjB,SAASA,IACP,OAASlS,MAAON,EAAWI,MAAM,GA+MnC,OA3mBAoT,EAAkB5S,UAAYmT,EAAGpT,YAAc8S,EAC/CA,EAA2B9S,YAAc6S,EACzCC,EAA2BpC,GACzBmC,EAAkBsC,YAAc,oBAYlCxW,EAAQyW,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOrV,YAClD,QAAOsV,IACHA,IAASzC,GAG2B,uBAAnCyC,EAAKH,aAAeG,EAAKC,QAIhC5W,EAAQ6W,KAAO,SAASH,GAUtB,OATIrW,OAAOyW,eACTzW,OAAOyW,eAAeJ,EAAQvC,IAE9BuC,EAAOK,UAAY5C,EACbpC,KAAqB2E,IACzBA,EAAO3E,GAAqB,sBAGhC2E,EAAOpV,UAAYjB,OAAOoS,OAAOgC,GAC1BiC,GAOT1W,EAAQgX,MAAQ,SAASjE,GACvB,OAASqC,QAASrC,IAsEpB2B,EAAsBE,EAActT,WACpCsT,EAActT,UAAUuQ,GAAuB,WAC7C,OAAOhM,MAET7F,EAAQ4U,cAAgBA,EAKxB5U,EAAQiX,MAAQ,SAAS/E,EAASC,EAASC,EAAMC,EAAawC,QACxC,IAAhBA,IAAwBA,EAAcqC,SAE1C,IAAIC,EAAO,IAAIvC,EACb3C,EAAKC,EAASC,EAASC,EAAMC,GAC7BwC,GAGF,OAAO7U,EAAQyW,oBAAoBtE,GAC/BgF,EACAA,EAAKtW,OAAOwU,KAAK,SAASF,GACxB,OAAOA,EAAOrU,KAAOqU,EAAOnU,MAAQmW,EAAKtW,UAuKjD6T,EAAsBD,GAEtBA,EAAG1C,GAAqB,YAOxB0C,EAAG7C,GAAkB,WACnB,OAAO/L,MAGT4O,EAAGtE,SAAW,WACZ,MAAO,sBAkCTnQ,EAAQoX,KAAO,SAASC,GACtB,IAAID,KACJ,IAAK,IAAIE,KAAOD,EACdD,EAAKrW,KAAKuW,GAMZ,OAJAF,EAAKG,UAIE,SAAS1W,IACd,KAAOuW,EAAKnW,QAAQ,CAClB,IAAIqW,EAAMF,EAAKI,MACf,GAAIF,KAAOD,EAGT,OAFAxW,EAAKG,MAAQsW,EACbzW,EAAKC,MAAO,EACLD,EAQX,OADAA,EAAKC,MAAO,EACLD,IAsCXb,EAAQwU,OAASA,EAMjB9B,EAAQpR,WACND,YAAaqR,EAEb2D,MAAO,SAASoB,GAcd,GAbA5R,KAAK6R,KAAO,EACZ7R,KAAKhF,KAAO,EAGZgF,KAAK0N,KAAO1N,KAAK2N,MAAQ9S,EACzBmF,KAAK/E,MAAO,EACZ+E,KAAKsN,SAAW,KAEhBtN,KAAKiN,OAAS,OACdjN,KAAKkN,IAAMrS,EAEXmF,KAAKqQ,WAAWvB,QAAQwB,IAEnBsB,EACH,IAAK,IAAIb,KAAQ/Q,KAEQ,MAAnB+Q,EAAK3Q,OAAO,IACZwL,EAAOwC,KAAKpO,KAAM+Q,KACjBhU,OAAOgU,EAAKe,MAAM,MACrB9R,KAAK+Q,GAAQlW,IAMrBkX,KAAM,WACJ/R,KAAK/E,MAAO,EAEZ,IACI+W,EADYhS,KAAKqQ,WAAW,GACLE,WAC3B,GAAwB,UAApByB,EAAWhE,KACb,MAAMgE,EAAW9E,IAGnB,OAAOlN,KAAKiS,MAGdrE,kBAAmB,SAASsE,GAC1B,GAAIlS,KAAK/E,KACP,MAAMiX,EAGR,IAAIzV,EAAUuD,KACd,SAASmS,EAAOC,EAAKC,GAYnB,OAXAvE,EAAOE,KAAO,QACdF,EAAOZ,IAAMgF,EACbzV,EAAQzB,KAAOoX,EAEXC,IAGF5V,EAAQwQ,OAAS,OACjBxQ,EAAQyQ,IAAMrS,KAGNwX,EAGZ,IAAK,IAAIhY,EAAI2F,KAAKqQ,WAAWjV,OAAS,EAAGf,GAAK,IAAKA,EAAG,CACpD,IAAI2V,EAAQhQ,KAAKqQ,WAAWhW,GACxByT,EAASkC,EAAMO,WAEnB,GAAqB,SAAjBP,EAAMC,OAIR,OAAOkC,EAAO,OAGhB,GAAInC,EAAMC,QAAUjQ,KAAK6R,KAAM,CAC7B,IAAIS,EAAW1G,EAAOwC,KAAK4B,EAAO,YAC9BuC,EAAa3G,EAAOwC,KAAK4B,EAAO,cAEpC,GAAIsC,GAAYC,EAAY,CAC1B,GAAIvS,KAAK6R,KAAO7B,EAAME,SACpB,OAAOiC,EAAOnC,EAAME,UAAU,GACzB,GAAIlQ,KAAK6R,KAAO7B,EAAMG,WAC3B,OAAOgC,EAAOnC,EAAMG,iBAGjB,GAAImC,GACT,GAAItS,KAAK6R,KAAO7B,EAAME,SACpB,OAAOiC,EAAOnC,EAAME,UAAU,OAG3B,KAAIqC,EAMT,MAAM,IAAI1V,MAAM,0CALhB,GAAImD,KAAK6R,KAAO7B,EAAMG,WACpB,OAAOgC,EAAOnC,EAAMG,gBAU9BtC,OAAQ,SAASG,EAAMd,GACrB,IAAK,IAAI7S,EAAI2F,KAAKqQ,WAAWjV,OAAS,EAAGf,GAAK,IAAKA,EAAG,CACpD,IAAI2V,EAAQhQ,KAAKqQ,WAAWhW,GAC5B,GAAI2V,EAAMC,QAAUjQ,KAAK6R,MACrBjG,EAAOwC,KAAK4B,EAAO,eACnBhQ,KAAK6R,KAAO7B,EAAMG,WAAY,CAChC,IAAIqC,EAAexC,EACnB,OAIAwC,IACU,UAATxE,GACS,aAATA,IACDwE,EAAavC,QAAU/C,GACvBA,GAAOsF,EAAarC,aAGtBqC,EAAe,MAGjB,IAAI1E,EAAS0E,EAAeA,EAAajC,cAIzC,OAHAzC,EAAOE,KAAOA,EACdF,EAAOZ,IAAMA,EAETsF,GACFxS,KAAKiN,OAAS,OACdjN,KAAKhF,KAAOwX,EAAarC,WAClB1C,GAGFzN,KAAKyS,SAAS3E,IAGvB2E,SAAU,SAAS3E,EAAQsC,GACzB,GAAoB,UAAhBtC,EAAOE,KACT,MAAMF,EAAOZ,IAcf,MAXoB,UAAhBY,EAAOE,MACS,aAAhBF,EAAOE,KACThO,KAAKhF,KAAO8S,EAAOZ,IACM,WAAhBY,EAAOE,MAChBhO,KAAKiS,KAAOjS,KAAKkN,IAAMY,EAAOZ,IAC9BlN,KAAKiN,OAAS,SACdjN,KAAKhF,KAAO,OACa,WAAhB8S,EAAOE,MAAqBoC,IACrCpQ,KAAKhF,KAAOoV,GAGP3C,GAGTiF,OAAQ,SAASvC,GACf,IAAK,IAAI9V,EAAI2F,KAAKqQ,WAAWjV,OAAS,EAAGf,GAAK,IAAKA,EAAG,CACpD,IAAI2V,EAAQhQ,KAAKqQ,WAAWhW,GAC5B,GAAI2V,EAAMG,aAAeA,EAGvB,OAFAnQ,KAAKyS,SAASzC,EAAMO,WAAYP,EAAMI,UACtCE,EAAcN,GACPvC,IAKbkF,MAAS,SAAS1C,GAChB,IAAK,IAAI5V,EAAI2F,KAAKqQ,WAAWjV,OAAS,EAAGf,GAAK,IAAKA,EAAG,CACpD,IAAI2V,EAAQhQ,KAAKqQ,WAAWhW,GAC5B,GAAI2V,EAAMC,SAAWA,EAAQ,CAC3B,IAAInC,EAASkC,EAAMO,WACnB,GAAoB,UAAhBzC,EAAOE,KAAkB,CAC3B,IAAI4E,EAAS9E,EAAOZ,IACpBoD,EAAcN,GAEhB,OAAO4C,GAMX,MAAM,IAAI/V,MAAM,0BAGlBgW,cAAe,SAASpC,EAAUb,EAAYC,GAa5C,OAZA7P,KAAKsN,UACH/S,SAAUoU,EAAO8B,GACjBb,WAAYA,EACZC,QAASA,GAGS,SAAhB7P,KAAKiN,SAGPjN,KAAKkN,IAAMrS,GAGN4S,IAQJtT,EA1rBM,CAisBgBD,EAAOC,SAGtC,IACE2Y,mBAAqBpH,EACrB,MAAOqH,GAUPC,SAAS,IAAK,yBAAdA,CAAwCtH,ix7GC/sB1CxR,EAAAC,QARA,SAAAoS,GACA,YAAAA,EACA,UAAA0G,eAAA,6DAGA,OAAA1G,yBCLArS,EAAAC,QAAiBuB,EAAQ,8BCAzB,IAAAwX,EAAoBxX,EAAQ,QAE5B,SAAAyX,EAAAC,EAAAC,EAAAC,GAiBA,MAhBA,oBAAAC,iBAAAC,IACAtZ,EAAAC,QAAAgZ,EAAAI,QAAAC,IAEAtZ,EAAAC,QAAAgZ,EAAA,SAAAC,EAAAC,EAAAC,GACA,IAAAG,EAAAP,EAAAE,EAAAC,GACA,GAAAI,EAAA,CACA,IAAAC,EAAAlZ,OAAAmZ,yBAAAF,EAAAJ,GAEA,OAAAK,EAAAF,IACAE,EAAAF,IAAApF,KAAAkF,GAGAI,EAAAvY,QAIAgY,EAAAC,EAAAC,EAAAC,GAAAF,GAGAlZ,EAAAC,QAAAgZ,sBCtBA,SAAAS,EAAAC,EAAA1E,EAAAC,EAAA0E,EAAAC,EAAAtC,EAAAvE,GACA,IACA,IAAAyC,EAAAkE,EAAApC,GAAAvE,GACA/R,EAAAwU,EAAAxU,MACG,MAAAuU,GAEH,YADAN,EAAAM,GAIAC,EAAA1U,KACAkU,EAAAhU,GAEAkW,QAAAlC,QAAAhU,GAAAqU,KAAAsE,EAAAC,GAwBA7Z,EAAAC,QApBA,SAAAgU,GACA,kBACA,IAAA5B,EAAAvM,KACAgU,EAAAC,UACA,WAAA5C,QAAA,SAAAlC,EAAAC,GACA,IAAAyE,EAAA1F,EAAA+F,MAAA3H,EAAAyH,GAEA,SAAAF,EAAA3Y,GACAyY,EAAAC,EAAA1E,EAAAC,EAAA0E,EAAAC,EAAA,OAAA5Y,GAGA,SAAA4Y,EAAA1Y,GACAuY,EAAAC,EAAA1E,EAAAC,EAAA0E,EAAAC,EAAA,QAAA1Y,GAGAyY,OAAAjZ,2BC3BAX,EAAAC,QAJA,SAAAC,GACA,GAAAqQ,MAAA0J,QAAA/Z,GAAA,OAAAA,yBCDA,IAAAqU,EAAqB/S,EAAQ,QAW7BxB,EAAAC,QATA,SAAAqX,EAAA6B,GACA,MAAA7Y,OAAAiB,UAAAoQ,eAAAuC,KAAAoD,EAAA6B,IAEA,QADA7B,EAAA/C,EAAA+C,MAIA,OAAAA,yBCRA,IAAA4C,EAAqB1Y,EAAQ,QAE7B2Y,EAA2B3Y,EAAQ,QAEnC4Y,EAAiC5Y,EAAQ,QAEzC6Y,EAAsB7Y,EAAQ,QAM9BxB,EAAAC,QAJA,SAAAC,EAAAC,GACA,OAAA+Z,EAAAha,IAAAia,EAAAja,EAAAC,IAAAia,EAAAla,EAAAC,IAAAka,2BCTA,IAAAtD,EAAqBvV,EAAQ,QAiB7BxB,EAAAC,QAfA,SAAAqa,EAAAC,GACA,sBAAAA,GAAA,OAAAA,EACA,UAAAjY,UAAA,sDAGAgY,EAAA/Y,UAAAjB,OAAAoS,OAAA6H,KAAAhZ,WACAD,aACAL,MAAAqZ,EACAE,UAAA,EACAC,cAAA,KAGAF,GAAAxD,EAAAuD,EAAAC,wBCVAva,EAAAC,QAJA,WACA,UAAAqC,UAAA,6JCDA,SAAAoY,EAAAC,GAIA,OAHA3a,EAAAC,QAAAya,EAAApa,OAAAyW,eAAAzW,OAAAiU,eAAA,SAAAoG,GACA,OAAAA,EAAA3D,WAAA1W,OAAAiU,eAAAoG,IAEAD,EAAAC,GAGA3a,EAAAC,QAAAya,0BCPA,IAAAE,EAAwBpZ,EAAQ,QAEhCqZ,EAAsBrZ,EAAQ,QAE9B4Y,EAAiC5Y,EAAQ,QAEzCsZ,EAAwBtZ,EAAQ,QAMhCxB,EAAAC,QAJA,SAAAC,GACA,OAAA0a,EAAA1a,IAAA2a,EAAA3a,IAAAka,EAAAla,IAAA4a,2BCFA,IAAAtJ,EAAA,SAAAvR,GACA,aAEA,IAEAU,EAFA8Q,EAAAnR,OAAAiB,UACAmQ,EAAAD,EAAAE,eAEAC,EAAA,mBAAAxR,iBACAyR,EAAAD,EAAAvR,UAAA,aACAyR,EAAAF,EAAAG,eAAA,kBACAC,EAAAJ,EAAAK,aAAA,gBAEA,SAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,IAAAC,EAAAH,KAAA7Q,qBAAAiR,EAAAJ,EAAAI,EACAC,EAAAnS,OAAAoS,OAAAH,EAAAhR,WACAgB,EAAA,IAAAoQ,EAAAL,OAMA,OAFAG,EAAAG,QAqMA,SAAAT,EAAAE,EAAA9P,GACA,IAAAsQ,EAAAC,EAEA,gBAAAC,EAAAC,GACA,GAAAH,IAAAI,EACA,UAAAtQ,MAAA,gCAGA,GAAAkQ,IAAAK,EAAA,CACA,aAAAH,EACA,MAAAC,EAKA,OAAAG,IAMA,IAHA5Q,EAAAwQ,SACAxQ,EAAAyQ,QAEA,CACA,IAAAI,EAAA7Q,EAAA6Q,SACA,GAAAA,EAAA,CACA,IAAAC,EAAAC,EAAAF,EAAA7Q,GACA,GAAA8Q,EAAA,CACA,GAAAA,IAAAE,EAAA,SACA,OAAAF,GAIA,YAAA9Q,EAAAwQ,OAGAxQ,EAAAiR,KAAAjR,EAAAkR,MAAAlR,EAAAyQ,SAES,aAAAzQ,EAAAwQ,OAAA,CACT,GAAAF,IAAAC,EAEA,MADAD,EAAAK,EACA3Q,EAAAyQ,IAGAzQ,EAAAmR,kBAAAnR,EAAAyQ,SAES,WAAAzQ,EAAAwQ,QACTxQ,EAAAoR,OAAA,SAAApR,EAAAyQ,KAGAH,EAAAI,EAEA,IAAAW,EAAAC,EAAA1B,EAAAE,EAAA9P,GACA,cAAAqR,EAAAE,KAAA,CAOA,GAJAjB,EAAAtQ,EAAAxB,KACAmS,EACAa,EAEAH,EAAAZ,MAAAO,EACA,SAGA,OACAtS,MAAA2S,EAAAZ,IACAjS,KAAAwB,EAAAxB,MAGS,UAAA6S,EAAAE,OACTjB,EAAAK,EAGA3Q,EAAAwQ,OAAA,QACAxQ,EAAAyQ,IAAAY,EAAAZ,OA7QAgB,CAAA7B,EAAAE,EAAA9P,GAEAkQ,EAcA,SAAAoB,EAAAI,EAAA5S,EAAA2R,GACA,IACA,OAAcc,KAAA,SAAAd,IAAAiB,EAAAC,KAAA7S,EAAA2R,IACT,MAAA7R,GACL,OAAc2S,KAAA,QAAAd,IAAA7R,IAhBdlB,EAAAiS,OAoBA,IAAAY,EAAA,iBACAiB,EAAA,iBACAd,EAAA,YACAC,EAAA,YAIAK,KAMA,SAAAf,KACA,SAAA2B,KACA,SAAAC,KAIA,IAAAC,KACAA,EAAAxC,GAAA,WACA,OAAA/L,MAGA,IAAAwO,EAAAhU,OAAAiU,eACAC,EAAAF,OAAAG,QACAD,GACAA,IAAA/C,GACAC,EAAAwC,KAAAM,EAAA3C,KAGAwC,EAAAG,GAGA,IAAAE,EAAAN,EAAA7S,UACAiR,EAAAjR,UAAAjB,OAAAoS,OAAA2B,GAQA,SAAAM,EAAApT,IACA,yBAAAqT,QAAA,SAAA7B,GACAxR,EAAAwR,GAAA,SAAAC,GACA,OAAAlN,KAAA8M,QAAAG,EAAAC,MAoCA,SAAA6B,EAAApC,EAAAqC,GAgCA,IAAAC,EAgCAjP,KAAA8M,QA9BA,SAAAG,EAAAC,GACA,SAAAgC,IACA,WAAAF,EAAA,SAAAG,EAAAC,IAnCA,SAAAC,EAAApC,EAAAC,EAAAiC,EAAAC,GACA,IAAAtB,EAAAC,EAAApB,EAAAM,GAAAN,EAAAO,GACA,aAAAY,EAAAE,KAEO,CACP,IAAAsB,EAAAxB,EAAAZ,IACA/R,EAAAmU,EAAAnU,MACA,OAAAA,GACA,iBAAAA,GACAyQ,EAAAwC,KAAAjT,EAAA,WACA6T,EAAAG,QAAAhU,EAAAoU,SAAAC,KAAA,SAAArU,GACAkU,EAAA,OAAAlU,EAAAgU,EAAAC,IACW,SAAA/T,GACXgU,EAAA,QAAAhU,EAAA8T,EAAAC,KAIAJ,EAAAG,QAAAhU,GAAAqU,KAAA,SAAAC,GAIAH,EAAAnU,MAAAsU,EACAN,EAAAG,IACS,SAAAI,GAGT,OAAAL,EAAA,QAAAK,EAAAP,EAAAC,KAvBAA,EAAAtB,EAAAZ,KAiCAmC,CAAApC,EAAAC,EAAAiC,EAAAC,KAIA,OAAAH,EAaAA,IAAAO,KACAN,EAGAA,GACAA,KAkHA,SAAA1B,EAAAF,EAAA7Q,GACA,IAAAwQ,EAAAK,EAAA/S,SAAAkC,EAAAwQ,QACA,GAAAA,IAAApS,EAAA,CAKA,GAFA4B,EAAA6Q,SAAA,KAEA,UAAA7Q,EAAAwQ,OAAA,CAEA,GAAAK,EAAA/S,SAAA,SAGAkC,EAAAwQ,OAAA,SACAxQ,EAAAyQ,IAAArS,EACA2S,EAAAF,EAAA7Q,GAEA,UAAAA,EAAAwQ,QAGA,OAAAQ,EAIAhR,EAAAwQ,OAAA,QACAxQ,EAAAyQ,IAAA,IAAA1Q,UACA,kDAGA,OAAAiR,EAGA,IAAAK,EAAAC,EAAAd,EAAAK,EAAA/S,SAAAkC,EAAAyQ,KAEA,aAAAY,EAAAE,KAIA,OAHAvR,EAAAwQ,OAAA,QACAxQ,EAAAyQ,IAAAY,EAAAZ,IACAzQ,EAAA6Q,SAAA,KACAG,EAGA,IAAAkC,EAAA7B,EAAAZ,IAEA,OAAAyC,EAOAA,EAAA1U,MAGAwB,EAAA6Q,EAAAsC,YAAAD,EAAAxU,MAGAsB,EAAAzB,KAAAsS,EAAAuC,QAQA,WAAApT,EAAAwQ,SACAxQ,EAAAwQ,OAAA,OACAxQ,EAAAyQ,IAAArS,GAUA4B,EAAA6Q,SAAA,KACAG,GANAkC,GA3BAlT,EAAAwQ,OAAA,QACAxQ,EAAAyQ,IAAA,IAAA1Q,UAAA,oCACAC,EAAA6Q,SAAA,KACAG,GAoDA,SAAAqC,EAAAC,GACA,IAAAC,GAAiBC,OAAAF,EAAA,IAEjB,KAAAA,IACAC,EAAAE,SAAAH,EAAA,IAGA,KAAAA,IACAC,EAAAG,WAAAJ,EAAA,GACAC,EAAAI,SAAAL,EAAA,IAGA/P,KAAAqQ,WAAAnV,KAAA8U,GAGA,SAAAM,EAAAN,GACA,IAAAlC,EAAAkC,EAAAO,eACAzC,EAAAE,KAAA,gBACAF,EAAAZ,IACA8C,EAAAO,WAAAzC,EAGA,SAAAjB,EAAAL,GAIAxM,KAAAqQ,aAAwBJ,OAAA,SACxBzD,EAAAsC,QAAAgB,EAAA9P,MACAA,KAAAwQ,OAAA,GA8BA,SAAA7B,EAAA8B,GACA,GAAAA,EAAA,CACA,IAAAC,EAAAD,EAAA1E,GACA,GAAA2E,EACA,OAAAA,EAAAtC,KAAAqC,GAGA,sBAAAA,EAAAzV,KACA,OAAAyV,EAGA,IAAA1T,MAAA0T,EAAArV,QAAA,CACA,IAAAf,GAAA,EAAAW,EAAA,SAAAA,IACA,OAAAX,EAAAoW,EAAArV,QACA,GAAAwQ,EAAAwC,KAAAqC,EAAApW,GAGA,OAFAW,EAAAG,MAAAsV,EAAApW,GACAW,EAAAC,MAAA,EACAD,EAOA,OAHAA,EAAAG,MAAAN,EACAG,EAAAC,MAAA,EAEAD,GAGA,OAAAA,UAKA,OAAYA,KAAAqS,GAIZ,SAAAA,IACA,OAAYlS,MAAAN,EAAAI,MAAA,GA+MZ,OA3mBAoT,EAAA5S,UAAAmT,EAAApT,YAAA8S,EACAA,EAAA9S,YAAA6S,EACAC,EAAApC,GACAmC,EAAAsC,YAAA,oBAYAxW,EAAAyW,oBAAA,SAAAC,GACA,IAAAC,EAAA,mBAAAD,KAAArV,YACA,QAAAsV,IACAA,IAAAzC,GAGA,uBAAAyC,EAAAH,aAAAG,EAAAC,QAIA5W,EAAA6W,KAAA,SAAAH,GAUA,OATArW,OAAAyW,eACAzW,OAAAyW,eAAAJ,EAAAvC,IAEAuC,EAAAK,UAAA5C,EACApC,KAAA2E,IACAA,EAAA3E,GAAA,sBAGA2E,EAAApV,UAAAjB,OAAAoS,OAAAgC,GACAiC,GAOA1W,EAAAgX,MAAA,SAAAjE,GACA,OAAYqC,QAAArC,IAsEZ2B,EAAAE,EAAAtT,WACAsT,EAAAtT,UAAAuQ,GAAA,WACA,OAAAhM,MAEA7F,EAAA4U,gBAKA5U,EAAAiX,MAAA,SAAA/E,EAAAC,EAAAC,EAAAC,EAAAwC,QACA,IAAAA,MAAAqC,SAEA,IAAAC,EAAA,IAAAvC,EACA3C,EAAAC,EAAAC,EAAAC,EAAAC,GACAwC,GAGA,OAAA7U,EAAAyW,oBAAAtE,GACAgF,EACAA,EAAAtW,OAAAwU,KAAA,SAAAF,GACA,OAAAA,EAAArU,KAAAqU,EAAAnU,MAAAmW,EAAAtW,UAuKA6T,EAAAD,GAEAA,EAAA1C,GAAA,YAOA0C,EAAA7C,GAAA,WACA,OAAA/L,MAGA4O,EAAAtE,SAAA,WACA,4BAkCAnQ,EAAAoX,KAAA,SAAAC,GACA,IAAAD,KACA,QAAAE,KAAAD,EACAD,EAAArW,KAAAuW,GAMA,OAJAF,EAAAG,UAIA,SAAA1W,IACA,KAAAuW,EAAAnW,QAAA,CACA,IAAAqW,EAAAF,EAAAI,MACA,GAAAF,KAAAD,EAGA,OAFAxW,EAAAG,MAAAsW,EACAzW,EAAAC,MAAA,EACAD,EAQA,OADAA,EAAAC,MAAA,EACAD,IAsCAb,EAAAwU,SAMA9B,EAAApR,WACAD,YAAAqR,EAEA2D,MAAA,SAAAoB,GAcA,GAbA5R,KAAA6R,KAAA,EACA7R,KAAAhF,KAAA,EAGAgF,KAAA0N,KAAA1N,KAAA2N,MAAA9S,EACAmF,KAAA/E,MAAA,EACA+E,KAAAsN,SAAA,KAEAtN,KAAAiN,OAAA,OACAjN,KAAAkN,IAAArS,EAEAmF,KAAAqQ,WAAAvB,QAAAwB,IAEAsB,EACA,QAAAb,KAAA/Q,KAEA,MAAA+Q,EAAA3Q,OAAA,IACAwL,EAAAwC,KAAApO,KAAA+Q,KACAhU,OAAAgU,EAAAe,MAAA,MACA9R,KAAA+Q,GAAAlW,IAMAkX,KAAA,WACA/R,KAAA/E,MAAA,EAEA,IACA+W,EADAhS,KAAAqQ,WAAA,GACAE,WACA,aAAAyB,EAAAhE,KACA,MAAAgE,EAAA9E,IAGA,OAAAlN,KAAAiS,MAGArE,kBAAA,SAAAsE,GACA,GAAAlS,KAAA/E,KACA,MAAAiX,EAGA,IAAAzV,EAAAuD,KACA,SAAAmS,EAAAC,EAAAC,GAYA,OAXAvE,EAAAE,KAAA,QACAF,EAAAZ,IAAAgF,EACAzV,EAAAzB,KAAAoX,EAEAC,IAGA5V,EAAAwQ,OAAA,OACAxQ,EAAAyQ,IAAArS,KAGAwX,EAGA,QAAAhY,EAAA2F,KAAAqQ,WAAAjV,OAAA,EAA8Cf,GAAA,IAAQA,EAAA,CACtD,IAAA2V,EAAAhQ,KAAAqQ,WAAAhW,GACAyT,EAAAkC,EAAAO,WAEA,YAAAP,EAAAC,OAIA,OAAAkC,EAAA,OAGA,GAAAnC,EAAAC,QAAAjQ,KAAA6R,KAAA,CACA,IAAAS,EAAA1G,EAAAwC,KAAA4B,EAAA,YACAuC,EAAA3G,EAAAwC,KAAA4B,EAAA,cAEA,GAAAsC,GAAAC,EAAA,CACA,GAAAvS,KAAA6R,KAAA7B,EAAAE,SACA,OAAAiC,EAAAnC,EAAAE,UAAA,GACa,GAAAlQ,KAAA6R,KAAA7B,EAAAG,WACb,OAAAgC,EAAAnC,EAAAG,iBAGW,GAAAmC,GACX,GAAAtS,KAAA6R,KAAA7B,EAAAE,SACA,OAAAiC,EAAAnC,EAAAE,UAAA,OAGW,KAAAqC,EAMX,UAAA1V,MAAA,0CALA,GAAAmD,KAAA6R,KAAA7B,EAAAG,WACA,OAAAgC,EAAAnC,EAAAG,gBAUAtC,OAAA,SAAAG,EAAAd,GACA,QAAA7S,EAAA2F,KAAAqQ,WAAAjV,OAAA,EAA8Cf,GAAA,IAAQA,EAAA,CACtD,IAAA2V,EAAAhQ,KAAAqQ,WAAAhW,GACA,GAAA2V,EAAAC,QAAAjQ,KAAA6R,MACAjG,EAAAwC,KAAA4B,EAAA,eACAhQ,KAAA6R,KAAA7B,EAAAG,WAAA,CACA,IAAAqC,EAAAxC,EACA,OAIAwC,IACA,UAAAxE,GACA,aAAAA,IACAwE,EAAAvC,QAAA/C,GACAA,GAAAsF,EAAArC,aAGAqC,EAAA,MAGA,IAAA1E,EAAA0E,IAAAjC,cAIA,OAHAzC,EAAAE,OACAF,EAAAZ,MAEAsF,GACAxS,KAAAiN,OAAA,OACAjN,KAAAhF,KAAAwX,EAAArC,WACA1C,GAGAzN,KAAAyS,SAAA3E,IAGA2E,SAAA,SAAA3E,EAAAsC,GACA,aAAAtC,EAAAE,KACA,MAAAF,EAAAZ,IAcA,MAXA,UAAAY,EAAAE,MACA,aAAAF,EAAAE,KACAhO,KAAAhF,KAAA8S,EAAAZ,IACO,WAAAY,EAAAE,MACPhO,KAAAiS,KAAAjS,KAAAkN,IAAAY,EAAAZ,IACAlN,KAAAiN,OAAA,SACAjN,KAAAhF,KAAA,OACO,WAAA8S,EAAAE,MAAAoC,IACPpQ,KAAAhF,KAAAoV,GAGA3C,GAGAiF,OAAA,SAAAvC,GACA,QAAA9V,EAAA2F,KAAAqQ,WAAAjV,OAAA,EAA8Cf,GAAA,IAAQA,EAAA,CACtD,IAAA2V,EAAAhQ,KAAAqQ,WAAAhW,GACA,GAAA2V,EAAAG,eAGA,OAFAnQ,KAAAyS,SAAAzC,EAAAO,WAAAP,EAAAI,UACAE,EAAAN,GACAvC,IAKAkF,MAAA,SAAA1C,GACA,QAAA5V,EAAA2F,KAAAqQ,WAAAjV,OAAA,EAA8Cf,GAAA,IAAQA,EAAA,CACtD,IAAA2V,EAAAhQ,KAAAqQ,WAAAhW,GACA,GAAA2V,EAAAC,WAAA,CACA,IAAAnC,EAAAkC,EAAAO,WACA,aAAAzC,EAAAE,KAAA,CACA,IAAA4E,EAAA9E,EAAAZ,IACAoD,EAAAN,GAEA,OAAA4C,GAMA,UAAA/V,MAAA,0BAGAgW,cAAA,SAAApC,EAAAb,EAAAC,GAaA,OAZA7P,KAAAsN,UACA/S,SAAAoU,EAAA8B,GACAb,aACAC,WAGA,SAAA7P,KAAAiN,SAGAjN,KAAAkN,IAAArS,GAGA4S,IAQAtT,EA1rBA,CAisB4BD,EAAAC,SAG5B,IACA2Y,mBAAApH,EACC,MAAAqH,GAUDC,SAAA,6BAAAA,CAAAtH,wBCntBAxR,EAAAC,QAJA,WACA,UAAAqC,UAAA,oKCDA,IAAAyY,EAAuBvZ,EAAQ,QAW/BxB,EAAAC,QATA,SAAA0a,EAAAK,GACA,GAAAL,EAAA,CACA,oBAAAA,EAAA,OAAAI,EAAAJ,EAAAK,GACA,IAAAC,EAAA3a,OAAAiB,UAAA6O,SAAA8D,KAAAyG,GAAA/C,MAAA,MAEA,MADA,WAAAqD,GAAAN,EAAArZ,cAAA2Z,EAAAN,EAAArZ,YAAAuV,MACA,QAAAoE,GAAA,QAAAA,EAAA1K,MAAA2K,KAAAD,GACA,cAAAA,GAAA,2CAAAE,KAAAF,GAAAF,EAAAJ,EAAAK,QAAA,wBCPA,IAOAI,EACAC,EARA3L,EAAA1P,EAAAC,WAUA,SAAAqb,IACA,UAAA3Y,MAAA,mCAEA,SAAA4Y,IACA,UAAA5Y,MAAA,qCAsBA,SAAA6Y,EAAAC,GACA,GAAAL,IAAAM,WAEA,OAAAA,WAAAD,EAAA,GAGA,IAAAL,IAAAE,IAAAF,IAAAM,WAEA,OADAN,EAAAM,WACAA,WAAAD,EAAA,GAEA,IAEA,OAAAL,EAAAK,EAAA,GACK,MAAA/Y,GACL,IAEA,OAAA0Y,EAAAlH,KAAA,KAAAuH,EAAA,GACS,MAAA/Y,GAET,OAAA0Y,EAAAlH,KAAApO,KAAA2V,EAAA,MAvCA,WACA,IAEAL,EADA,mBAAAM,WACAA,WAEAJ,EAEK,MAAA5Y,GACL0Y,EAAAE,EAEA,IAEAD,EADA,mBAAAM,aACAA,aAEAJ,EAEK,MAAA7Y,GACL2Y,EAAAE,GAjBA,GAwEA,IAEAK,EAFAC,KACAC,GAAA,EAEAC,GAAA,EAEA,SAAAC,IACAF,GAAAF,IAGAE,GAAA,EACAF,EAAA1a,OACA2a,EAAAD,EAAAK,OAAAJ,GAEAE,GAAA,EAEAF,EAAA3a,QACAgb,KAIA,SAAAA,IACA,IAAAJ,EAAA,CAGA,IAAAK,EAAAX,EAAAQ,GACAF,GAAA,EAGA,IADA,IAAAxK,EAAAuK,EAAA3a,OACAoQ,GAAA,CAGA,IAFAsK,EAAAC,EACAA,OACAE,EAAAzK,GACAsK,GACAA,EAAAG,GAAAK,MAGAL,GAAA,EACAzK,EAAAuK,EAAA3a,OAEA0a,EAAA,KACAE,GAAA,EAnEA,SAAAO,GACA,GAAAhB,IAAAM,aAEA,OAAAA,aAAAU,GAGA,IAAAhB,IAAAE,IAAAF,IAAAM,aAEA,OADAN,EAAAM,aACAA,aAAAU,GAEA,IAEAhB,EAAAgB,GACK,MAAA3Z,GACL,IAEA,OAAA2Y,EAAAnH,KAAA,KAAAmI,GACS,MAAA3Z,GAGT,OAAA2Y,EAAAnH,KAAApO,KAAAuW,KAgDAC,CAAAH,IAiBA,SAAAI,EAAAd,EAAAe,GACA1W,KAAA2V,MACA3V,KAAA0W,QAYA,SAAAC,KA5BA/M,EAAAgN,SAAA,SAAAjB,GACA,IAAA3B,EAAA,IAAAvJ,MAAAwJ,UAAA7Y,OAAA,GACA,GAAA6Y,UAAA7Y,OAAA,EACA,QAAAf,EAAA,EAAuBA,EAAA4Z,UAAA7Y,OAAsBf,IAC7C2Z,EAAA3Z,EAAA,GAAA4Z,UAAA5Z,GAGA0b,EAAA7a,KAAA,IAAAub,EAAAd,EAAA3B,IACA,IAAA+B,EAAA3a,QAAA4a,GACAN,EAAAU,IASAK,EAAAhb,UAAA6a,IAAA,WACAtW,KAAA2V,IAAAzB,MAAA,KAAAlU,KAAA0W,QAEA9M,EAAAiN,MAAA,UACAjN,EAAAkN,SAAA,EACAlN,EAAAmN,OACAnN,EAAAoN,QACApN,EAAAqN,QAAA,GACArN,EAAAsN,YAIAtN,EAAAuN,GAAAR,EACA/M,EAAAwN,YAAAT,EACA/M,EAAAyN,KAAAV,EACA/M,EAAA0N,IAAAX,EACA/M,EAAA2N,eAAAZ,EACA/M,EAAA4N,mBAAAb,EACA/M,EAAA6N,KAAAd,EACA/M,EAAA8N,gBAAAf,EACA/M,EAAA+N,oBAAAhB,EAEA/M,EAAAgO,UAAA,SAAA7G,GAAqC,UAErCnH,EAAAiO,QAAA,SAAA9G,GACA,UAAAlU,MAAA,qCAGA+M,EAAAkO,IAAA,WAA2B,WAC3BlO,EAAAmO,MAAA,SAAAC,GACA,UAAAnb,MAAA,mCAEA+M,EAAAqO,MAAA,WAA4B,8BCnL5B/d,EAAAC,QAJA,SAAAmX,GACA,uBAAAhX,eAAAC,YAAAC,OAAA8W,GAAA,OAAA7G,MAAA2K,KAAA9D,wCCDA,eAAA4G,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAG,oBAAAC,aAAA,OAAAA,aAAiBA,YAAYC,IAC9Bve,EAAOC,QAAU,kBAAGqe,YAAYC,YAC1B,IAAA7O,GAAA,OAAAA,GAAaA,EAAQuO,QAC3Bje,EAAOC,QAAU,kBAAI+d,IAAmBI,GAAgB,KACxDH,EAASvO,EAAQuO,OAIjBE,GAHAH,EAAiB,WACf,IAAAQ,SACQ,KADRA,EAAKP,KACF,GAAWO,EAAG,OAEnBH,EAA4B,IAAnB3O,EAAQ+O,SACjBL,EAAeD,EAAiBE,GAC1BK,KAAKH,KACXve,EAAOC,QAAU,kBAAGye,KAAKH,MAAQL,GACjCA,EAAWQ,KAAKH,QAEhBve,EAAOC,QAAU,kBAAO,IAAAye,MAAOC,UAAYT,GAC3CA,GAAe,IAAAQ,MAAOC,kEChBxB,IAAAvd,EAAcI,EAAQ,QAEtBod,EAA4Bpd,EAAQ,QAUpCxB,EAAAC,QARA,SAAAoS,EAAA6B,GACA,OAAAA,GAAA,WAAA9S,EAAA8S,IAAA,mBAAAA,EAIA0K,EAAAvM,GAHA6B,uBCNA,SAAA2K,EAAA3F,EAAA4F,GACA,QAAA3e,EAAA,EAAiBA,EAAA2e,EAAA5d,OAAkBf,IAAA,CACnC,IAAA4e,EAAAD,EAAA3e,GACA4e,EAAAC,WAAAD,EAAAC,aAAA,EACAD,EAAAtE,cAAA,EACA,UAAAsE,MAAAvE,UAAA,GACAla,OAAA2e,eAAA/F,EAAA6F,EAAAxH,IAAAwH,IAUA/e,EAAAC,QANA,SAAA2F,EAAAsZ,EAAAC,GAGA,OAFAD,GAAAL,EAAAjZ,EAAArE,UAAA2d,GACAC,GAAAN,EAAAjZ,EAAAuZ,GACAvZ,0BCbA,SAAAwZ,GAOA,IAPA,IAAAb,EAAU/c,EAAQ,QAClB6d,EAAA,oBAAAC,OAAAF,EAAAE,OACAC,GAAA,gBACAC,EAAA,iBACAC,EAAAJ,EAAA,UAAAG,GACAE,EAAAL,EAAA,SAAAG,IAAAH,EAAA,gBAAAG,GAEArf,EAAA,GAAcsf,GAAAtf,EAAAof,EAAAre,OAA4Bf,IAC1Csf,EAAAJ,EAAAE,EAAApf,GAAA,UAAAqf,GACAE,EAAAL,EAAAE,EAAApf,GAAA,SAAAqf,IACAH,EAAAE,EAAApf,GAAA,gBAAAqf,GAIA,IAAAC,IAAAC,EAAA,CACA,IAAAC,EAAA,EACAC,EAAA,EACA/D,KAGA4D,EAAA,SAAAI,GACA,OAAAhE,EAAA3a,OAAA,CACA,IAAA4e,EAAAvB,IACAzd,EAAAif,KAAAC,IAAA,EALA,QAKAF,EAAAH,IACAA,EAAA7e,EAAAgf,EACApE,WAAA,WACA,IAAAuE,EAAApE,EAAAjE,MAAA,GAIAiE,EAAA3a,OAAA,EACA,QAAAf,EAAA,EAAsBA,EAAA8f,EAAA/e,OAAef,IACrC,IAAA8f,EAAA9f,GAAA+f,UACA,IACAD,EAAA9f,GAAA0f,SAAAF,GACa,MAAAjd,GACbgZ,WAAA,WAAqC,MAAAhZ,GAAU,KAIxCqd,KAAAI,MAAArf,IAOP,OALA+a,EAAA7a,MACAiX,SAAA2H,EACAC,WACAK,WAAA,IAEAN,GAGAF,EAAA,SAAAzH,GACA,QAAA9X,EAAA,EAAkBA,EAAA0b,EAAA3a,OAAkBf,IACpC0b,EAAA1b,GAAA8X,aACA4D,EAAA1b,GAAA+f,WAAA,IAMAlgB,EAAAC,QAAA,SAAAgU,GAIA,OAAAwL,EAAAvL,KAAAmL,EAAApL,IAEAjU,EAAAC,QAAAmgB,OAAA,WACAV,EAAA1F,MAAAqF,EAAAtF,YAEA/Z,EAAAC,QAAAogB,SAAA,SAAA/I,GACAA,IACAA,EAAA+H,GAEA/H,EAAAgJ,sBAAAb,EACAnI,EAAAiJ,qBAAAb,2CC1DA1f,EAAAC,QAfA,SAAAoB,EAAAkW,EAAAtW,GAYA,OAXAsW,KAAAlW,EACAf,OAAA2e,eAAA5d,EAAAkW,GACAtW,QACA+d,YAAA,EACAvE,cAAA,EACAD,UAAA,IAGAnZ,EAAAkW,GAAAtW,EAGAI,yBCZA,IAAA0Z,EAAuBvZ,EAAQ,QAM/BxB,EAAAC,QAJA,SAAAC,GACA,GAAAqQ,MAAA0J,QAAA/Z,GAAA,OAAA6a,EAAA7a,wBCHA,SAAAsgB,EAAA7F,EAAA1X,GAMA,OALAjD,EAAAC,QAAAugB,EAAAlgB,OAAAyW,gBAAA,SAAA4D,EAAA1X,GAEA,OADA0X,EAAA3D,UAAA/T,EACA0X,GAGA6F,EAAA7F,EAAA1X,GAGAjD,EAAAC,QAAAugB,sBCHAxgB,EAAAC,QANA,SAAA0F,EAAAC,GACA,KAAAD,aAAAC,GACA,UAAAtD,UAAA,0DCFA,SAAAlB,EAAAC,GACA,0BAYA,MAVA,mBAAAjB,QAAA,iBAAAA,OAAAC,SACAL,EAAAC,QAAAmB,EAAA,SAAAC,GACA,cAAAA,GAGArB,EAAAC,QAAAmB,EAAA,SAAAC,GACA,OAAAA,GAAA,mBAAAjB,QAAAiB,EAAAC,cAAAlB,QAAAiB,IAAAjB,OAAAmB,UAAA,gBAAAF,GAIAD,EAAAC,GAGArB,EAAAC,QAAAmB", "file": "deps/canvg.js", "sourcesContent": ["function _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nmodule.exports = _iterableToArrayLimit;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/iterableToArrayLimit.js\n// module id = 15tG\n// module chunks = 0471", "function _typeof(obj) {\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}\n* @module StackBlur\n* @version 0.5\n* <AUTHOR>\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 <PERSON> Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || !('naturalWidth' in img)) {\n    return;\n  }\n\n  var w = img.naturalWidth;\n  var h = img.naturalHeight;\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  canvas.style.width = w + 'px';\n  canvas.style.height = h + 'px';\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var x, y, i, p, yp, yi, yw, rSum, gSum, bSum, aSum, rOutSum, gOutSum, bOutSum, aOutSum, rInSum, gInSum, bInSum, aInSum, pr, pg, pb, pa, rbs;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  yw = yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (y = 0; y < height; y++) {\n    rInSum = gInSum = bInSum = aInSum = rSum = gSum = bSum = aSum = 0;\n    rOutSum = radiusPlus1 * (pr = pixels[yi]);\n    gOutSum = radiusPlus1 * (pg = pixels[yi + 1]);\n    bOutSum = radiusPlus1 * (pb = pixels[yi + 2]);\n    aOutSum = radiusPlus1 * (pa = pixels[yi + 3]);\n    rSum += sumFactor * pr;\n    gSum += sumFactor * pg;\n    bSum += sumFactor * pb;\n    aSum += sumFactor * pa;\n    stack = stackStart;\n\n    for (i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    for (i = 1; i < radiusPlus1; i++) {\n      p = yi + ((widthMinus1 < i ? widthMinus1 : i) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - i);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      aSum += (stack.a = pa = pixels[p + 3]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      aInSum += pa;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (x = 0; x < width; x++) {\n      pixels[yi + 3] = pa = aSum * mulSum >> shgSum;\n\n      if (pa !== 0) {\n        pa = 255 / pa;\n        pixels[yi] = (rSum * mulSum >> shgSum) * pa;\n        pixels[yi + 1] = (gSum * mulSum >> shgSum) * pa;\n        pixels[yi + 2] = (bSum * mulSum >> shgSum) * pa;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      aInSum += stackIn.a = pixels[p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      aOutSum += pa = stackOut.a;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      aInSum -= pa;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (x = 0; x < width; x++) {\n    gInSum = bInSum = aInSum = rInSum = gSum = bSum = aSum = rSum = 0;\n    yi = x << 2;\n    rOutSum = radiusPlus1 * (pr = pixels[yi]);\n    gOutSum = radiusPlus1 * (pg = pixels[yi + 1]);\n    bOutSum = radiusPlus1 * (pb = pixels[yi + 2]);\n    aOutSum = radiusPlus1 * (pa = pixels[yi + 3]);\n    rSum += sumFactor * pr;\n    gSum += sumFactor * pg;\n    bSum += sumFactor * pb;\n    aSum += sumFactor * pa;\n    stack = stackStart;\n\n    for (i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    yp = width;\n\n    for (i = 1; i <= radius; i++) {\n      yi = yp + x << 2;\n      rSum += (stack.r = pr = pixels[yi]) * (rbs = radiusPlus1 - i);\n      gSum += (stack.g = pg = pixels[yi + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[yi + 2]) * rbs;\n      aSum += (stack.a = pa = pixels[yi + 3]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      aInSum += pa;\n      stack = stack.next;\n\n      if (i < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (y = 0; y < height; y++) {\n      p = yi << 2;\n      pixels[p + 3] = pa = aSum * mulSum >> shgSum;\n\n      if (pa > 0) {\n        pa = 255 / pa;\n        pixels[p] = (rSum * mulSum >> shgSum) * pa;\n        pixels[p + 1] = (gSum * mulSum >> shgSum) * pa;\n        pixels[p + 2] = (bSum * mulSum >> shgSum) * pa;\n      } else {\n        pixels[p] = pixels[p + 1] = pixels[p + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n      p = x + ((p = y + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      rSum += rInSum += stackIn.r = pixels[p];\n      gSum += gInSum += stackIn.g = pixels[p + 1];\n      bSum += bInSum += stackIn.b = pixels[p + 2];\n      aSum += aInSum += stackIn.a = pixels[p + 3];\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      aOutSum += pa = stackOut.a;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      aInSum -= pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var x, y, i, p, yp, yi, yw, rSum, gSum, bSum, rOutSum, gOutSum, bOutSum, rInSum, gInSum, bInSum, pr, pg, pb, rbs;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  yw = yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (y = 0; y < height; y++) {\n    rInSum = gInSum = bInSum = rSum = gSum = bSum = 0;\n    rOutSum = radiusPlus1 * (pr = pixels[yi]);\n    gOutSum = radiusPlus1 * (pg = pixels[yi + 1]);\n    bOutSum = radiusPlus1 * (pb = pixels[yi + 2]);\n    rSum += sumFactor * pr;\n    gSum += sumFactor * pg;\n    bSum += sumFactor * pb;\n    stack = stackStart;\n\n    for (i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    for (i = 1; i < radiusPlus1; i++) {\n      p = yi + ((widthMinus1 < i ? widthMinus1 : i) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - i);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >> shgSum;\n      pixels[yi + 1] = gSum * mulSum >> shgSum;\n      pixels[yi + 2] = bSum * mulSum >> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (x = 0; x < width; x++) {\n    gInSum = bInSum = rInSum = gSum = bSum = rSum = 0;\n    yi = x << 2;\n    rOutSum = radiusPlus1 * (pr = pixels[yi]);\n    gOutSum = radiusPlus1 * (pg = pixels[yi + 1]);\n    bOutSum = radiusPlus1 * (pb = pixels[yi + 2]);\n    rSum += sumFactor * pr;\n    gSum += sumFactor * pg;\n    bSum += sumFactor * pb;\n    stack = stackStart;\n\n    for (i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    yp = width;\n\n    for (i = 1; i <= radius; i++) {\n      yi = yp + x << 2;\n      rSum += (stack.r = pr = pixels[yi]) * (rbs = radiusPlus1 - i);\n      gSum += (stack.g = pg = pixels[yi + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[yi + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n\n      if (i < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (y = 0; y < height; y++) {\n      p = yi << 2;\n      pixels[p] = rSum * mulSum >> shgSum;\n      pixels[p + 1] = gSum * mulSum >> shgSum;\n      pixels[p + 2] = bSum * mulSum >> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = x + ((p = y + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      rSum += rInSum += stackIn.r = pixels[p];\n      gSum += gInSum += stackIn.g = pixels[p + 1];\n      bSum += bInSum += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack = function BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\nexport { BlurStack, processImage as image, processCanvasRGBA as canvasRGBA, processCanvasRGB as canvasRGB, processImageDataRGBA as imageDataRGBA, processImageDataRGB as imageDataRGB };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/stackblur-canvas/dist/stackblur-es.js\n// module id = 2gqV\n// module chunks = 0471", "/*\n\tBased on rgbcolor.js by <PERSON><PERSON><PERSON> <<EMAIL>>\n\thttp://www.phpied.com/rgb-color-parser-in-javascript/\n*/\n\nmodule.exports = function(color_string) {\n    this.ok = false;\n    this.alpha = 1.0;\n\n    // strip any leading #\n    if (color_string.charAt(0) == '#') { // remove # if any\n        color_string = color_string.substr(1,6);\n    }\n\n    color_string = color_string.replace(/ /g,'');\n    color_string = color_string.toLowerCase();\n\n    // before getting into regexps, try simple matches\n    // and overwrite the input\n    var simple_colors = {\n        aliceblue: 'f0f8ff',\n        antiquewhite: 'faebd7',\n        aqua: '00ffff',\n        aquamarine: '7fffd4',\n        azure: 'f0ffff',\n        beige: 'f5f5dc',\n        bisque: 'ffe4c4',\n        black: '000000',\n        blanchedalmond: 'ffebcd',\n        blue: '0000ff',\n        blueviolet: '8a2be2',\n        brown: 'a52a2a',\n        burlywood: 'deb887',\n        cadetblue: '5f9ea0',\n        chartreuse: '7fff00',\n        chocolate: 'd2691e',\n        coral: 'ff7f50',\n        cornflowerblue: '6495ed',\n        cornsilk: 'fff8dc',\n        crimson: 'dc143c',\n        cyan: '00ffff',\n        darkblue: '00008b',\n        darkcyan: '008b8b',\n        darkgoldenrod: 'b8860b',\n        darkgray: 'a9a9a9',\n        darkgreen: '006400',\n        darkkhaki: 'bdb76b',\n        darkmagenta: '8b008b',\n        darkolivegreen: '556b2f',\n        darkorange: 'ff8c00',\n        darkorchid: '9932cc',\n        darkred: '8b0000',\n        darksalmon: 'e9967a',\n        darkseagreen: '8fbc8f',\n        darkslateblue: '483d8b',\n        darkslategray: '2f4f4f',\n        darkturquoise: '00ced1',\n        darkviolet: '9400d3',\n        deeppink: 'ff1493',\n        deepskyblue: '00bfff',\n        dimgray: '696969',\n        dodgerblue: '1e90ff',\n        feldspar: 'd19275',\n        firebrick: 'b22222',\n        floralwhite: 'fffaf0',\n        forestgreen: '228b22',\n        fuchsia: 'ff00ff',\n        gainsboro: 'dcdcdc',\n        ghostwhite: 'f8f8ff',\n        gold: 'ffd700',\n        goldenrod: 'daa520',\n        gray: '808080',\n        green: '008000',\n        greenyellow: 'adff2f',\n        honeydew: 'f0fff0',\n        hotpink: 'ff69b4',\n        indianred : 'cd5c5c',\n        indigo : '4b0082',\n        ivory: 'fffff0',\n        khaki: 'f0e68c',\n        lavender: 'e6e6fa',\n        lavenderblush: 'fff0f5',\n        lawngreen: '7cfc00',\n        lemonchiffon: 'fffacd',\n        lightblue: 'add8e6',\n        lightcoral: 'f08080',\n        lightcyan: 'e0ffff',\n        lightgoldenrodyellow: 'fafad2',\n        lightgrey: 'd3d3d3',\n        lightgreen: '90ee90',\n        lightpink: 'ffb6c1',\n        lightsalmon: 'ffa07a',\n        lightseagreen: '20b2aa',\n        lightskyblue: '87cefa',\n        lightslateblue: '8470ff',\n        lightslategray: '778899',\n        lightsteelblue: 'b0c4de',\n        lightyellow: 'ffffe0',\n        lime: '00ff00',\n        limegreen: '32cd32',\n        linen: 'faf0e6',\n        magenta: 'ff00ff',\n        maroon: '800000',\n        mediumaquamarine: '66cdaa',\n        mediumblue: '0000cd',\n        mediumorchid: 'ba55d3',\n        mediumpurple: '9370d8',\n        mediumseagreen: '3cb371',\n        mediumslateblue: '7b68ee',\n        mediumspringgreen: '00fa9a',\n        mediumturquoise: '48d1cc',\n        mediumvioletred: 'c71585',\n        midnightblue: '191970',\n        mintcream: 'f5fffa',\n        mistyrose: 'ffe4e1',\n        moccasin: 'ffe4b5',\n        navajowhite: 'ffdead',\n        navy: '000080',\n        oldlace: 'fdf5e6',\n        olive: '808000',\n        olivedrab: '6b8e23',\n        orange: 'ffa500',\n        orangered: 'ff4500',\n        orchid: 'da70d6',\n        palegoldenrod: 'eee8aa',\n        palegreen: '98fb98',\n        paleturquoise: 'afeeee',\n        palevioletred: 'd87093',\n        papayawhip: 'ffefd5',\n        peachpuff: 'ffdab9',\n        peru: 'cd853f',\n        pink: 'ffc0cb',\n        plum: 'dda0dd',\n        powderblue: 'b0e0e6',\n        purple: '800080',\n        rebeccapurple: '663399',\n        red: 'ff0000',\n        rosybrown: 'bc8f8f',\n        royalblue: '4169e1',\n        saddlebrown: '8b4513',\n        salmon: 'fa8072',\n        sandybrown: 'f4a460',\n        seagreen: '2e8b57',\n        seashell: 'fff5ee',\n        sienna: 'a0522d',\n        silver: 'c0c0c0',\n        skyblue: '87ceeb',\n        slateblue: '6a5acd',\n        slategray: '708090',\n        snow: 'fffafa',\n        springgreen: '00ff7f',\n        steelblue: '4682b4',\n        tan: 'd2b48c',\n        teal: '008080',\n        thistle: 'd8bfd8',\n        tomato: 'ff6347',\n        turquoise: '40e0d0',\n        violet: 'ee82ee',\n        violetred: 'd02090',\n        wheat: 'f5deb3',\n        white: 'ffffff',\n        whitesmoke: 'f5f5f5',\n        yellow: 'ffff00',\n        yellowgreen: '9acd32'\n    };\n    color_string = simple_colors[color_string] || color_string;\n    // emd of simple type-in colors\n\n    // array of color definition objects\n    var color_defs = [\n        {\n            re: /^rgba\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3}),\\s*((?:\\d?\\.)?\\d)\\)$/,\n            example: ['rgba(123, 234, 45, 0.8)', 'rgba(255,234,245,1.0)'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1]),\n                    parseInt(bits[2]),\n                    parseInt(bits[3]),\n                    parseFloat(bits[4])\n                ];\n            }\n        },\n        {\n            re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n            example: ['rgb(123, 234, 45)', 'rgb(255,234,245)'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1]),\n                    parseInt(bits[2]),\n                    parseInt(bits[3])\n                ];\n            }\n        },\n        {\n            re: /^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n            example: ['#00ff00', '336699'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1], 16),\n                    parseInt(bits[2], 16),\n                    parseInt(bits[3], 16)\n                ];\n            }\n        },\n        {\n            re: /^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n            example: ['#fb0', 'f0f'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1] + bits[1], 16),\n                    parseInt(bits[2] + bits[2], 16),\n                    parseInt(bits[3] + bits[3], 16)\n                ];\n            }\n        }\n    ];\n\n    // search through the definitions to find a match\n    for (var i = 0; i < color_defs.length; i++) {\n        var re = color_defs[i].re;\n        var processor = color_defs[i].process;\n        var bits = re.exec(color_string);\n        if (bits) {\n            var channels = processor(bits);\n            this.r = channels[0];\n            this.g = channels[1];\n            this.b = channels[2];\n            if (channels.length > 3) {\n                this.alpha = channels[3];\n            }\n            this.ok = true;\n        }\n\n    }\n\n    // validate/cleanup values\n    this.r = (this.r < 0 || isNaN(this.r)) ? 0 : ((this.r > 255) ? 255 : this.r);\n    this.g = (this.g < 0 || isNaN(this.g)) ? 0 : ((this.g > 255) ? 255 : this.g);\n    this.b = (this.b < 0 || isNaN(this.b)) ? 0 : ((this.b > 255) ? 255 : this.b);\n    this.alpha = (this.alpha < 0) ? 0 : ((this.alpha > 1.0 || isNaN(this.alpha)) ? 1.0 : this.alpha);\n\n    // some getters\n    this.toRGB = function () {\n        return 'rgb(' + this.r + ', ' + this.g + ', ' + this.b + ')';\n    }\n    this.toRGBA = function () {\n        return 'rgba(' + this.r + ', ' + this.g + ', ' + this.b + ', ' + this.alpha + ')';\n    }\n    this.toHex = function () {\n        var r = this.r.toString(16);\n        var g = this.g.toString(16);\n        var b = this.b.toString(16);\n        if (r.length == 1) r = '0' + r;\n        if (g.length == 1) g = '0' + g;\n        if (b.length == 1) b = '0' + b;\n        return '#' + r + g + b;\n    }\n\n    // help\n    this.getHelpXML = function () {\n\n        var examples = new Array();\n        // add regexps\n        for (var i = 0; i < color_defs.length; i++) {\n            var example = color_defs[i].example;\n            for (var j = 0; j < example.length; j++) {\n                examples[examples.length] = example[j];\n            }\n        }\n        // add type-in colors\n        for (var sc in simple_colors) {\n            examples[examples.length] = sc;\n        }\n\n        var xml = document.createElement('ul');\n        xml.setAttribute('id', 'rgbcolor-examples');\n        for (var i = 0; i < examples.length; i++) {\n            try {\n                var list_item = document.createElement('li');\n                var list_color = new RGBColor(examples[i]);\n                var example_div = document.createElement('div');\n                example_div.style.cssText =\n                        'margin: 3px; '\n                        + 'border: 1px solid black; '\n                        + 'background:' + list_color.toHex() + '; '\n                        + 'color:' + list_color.toHex()\n                ;\n                example_div.appendChild(document.createTextNode('test'));\n                var list_item_value = document.createTextNode(\n                    ' ' + examples[i] + ' -> ' + list_color.toRGB() + ' -> ' + list_color.toHex()\n                );\n                list_item.appendChild(example_div);\n                list_item.appendChild(list_item_value);\n                xml.appendChild(list_item);\n\n            } catch(e){}\n        }\n        return xml;\n\n    }\n\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/rgbcolor/index.js\n// module id = 4E4r\n// module chunks = 0471", "function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nmodule.exports = _arrayLikeToArray;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/arrayLikeToArray.js\n// module id = 4WZ4\n// module chunks = 0471", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunctionPrototype[toStringTagSymbol] =\n    GeneratorFunction.displayName = \"GeneratorFunction\";\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      prototype[method] = function(arg) {\n        return this._invoke(method, arg);\n      };\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      if (!(toStringTagSymbol in genFun)) {\n        genFun[toStringTagSymbol] = \"GeneratorFunction\";\n      }\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  Gp[toStringTagSymbol] = \"Generator\";\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n\n\n\n// WEBPACK FOOTER //\n// ../node_modules/regenerator-runtime/runtime.js", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nmodule.exports = _assertThisInitialized;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/assertThisInitialized.js\n// module id = AKLy\n// module chunks = 0471", "module.exports = require(\"regenerator-runtime\");\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/regenerator/index.js\n// module id = FOhS\n// module chunks = 0471", "var superPropBase = require(\"./superPropBase\");\n\nfunction _get(target, property, receiver) {\n  if (typeof Reflect !== \"undefined\" && Reflect.get) {\n    module.exports = _get = Reflect.get;\n  } else {\n    module.exports = _get = function _get(target, property, receiver) {\n      var base = superPropBase(target, property);\n      if (!base) return;\n      var desc = Object.getOwnPropertyDescriptor(base, property);\n\n      if (desc.get) {\n        return desc.get.call(receiver);\n      }\n\n      return desc.value;\n    };\n  }\n\n  return _get(target, property, receiver || target);\n}\n\nmodule.exports = _get;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/get.js\n// module id = GUmJ\n// module chunks = 0471", "function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\n\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n        args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n\n      _next(undefined);\n    });\n  };\n}\n\nmodule.exports = _asyncToGenerator;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/asyncToGenerator.js\n// module id = Gd1W\n// module chunks = 0471", "function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nmodule.exports = _arrayWithHoles;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/arrayWithHoles.js\n// module id = KED7\n// module chunks = 0471", "var getPrototypeOf = require(\"./getPrototypeOf\");\n\nfunction _superPropBase(object, property) {\n  while (!Object.prototype.hasOwnProperty.call(object, property)) {\n    object = getPrototypeOf(object);\n    if (object === null) break;\n  }\n\n  return object;\n}\n\nmodule.exports = _superPropBase;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/superPropBase.js\n// module id = Lilp\n// module chunks = 0471", "var arrayWithHoles = require(\"./arrayWithHoles\");\n\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit\");\n\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray\");\n\nvar nonIterableRest = require(\"./nonIterableRest\");\n\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\n\nmodule.exports = _slicedToArray;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/slicedToArray.js\n// module id = MGDH\n// module chunks = 0471", "var setPrototypeOf = require(\"./setPrototypeOf\");\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n\nmodule.exports = _inherits;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/inherits.js\n// module id = QUJ4\n// module chunks = 0471", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nmodule.exports = _nonIterableSpread;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/nonIterableSpread.js\n// module id = RAmp\n// module chunks = 0471", "function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nmodule.exports = _getPrototypeOf;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/getPrototypeOf.js\n// module id = RXKi\n// module chunks = 0471", "var arrayWithoutHoles = require(\"./arrayWithoutHoles\");\n\nvar iterableToArray = require(\"./iterableToArray\");\n\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray\");\n\nvar nonIterableSpread = require(\"./nonIterableSpread\");\n\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\n\nmodule.exports = _toConsumableArray;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/toConsumableArray.js\n// module id = S9/t\n// module chunks = 0471", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunctionPrototype[toStringTagSymbol] =\n    GeneratorFunction.displayName = \"GeneratorFunction\";\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      prototype[method] = function(arg) {\n        return this._invoke(method, arg);\n      };\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      if (!(toStringTagSymbol in genFun)) {\n        genFun[toStringTagSymbol] = \"GeneratorFunction\";\n      }\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  Gp[toStringTagSymbol] = \"Generator\";\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/regenerator-runtime/runtime.js\n// module id = TFFM\n// module chunks = 0471", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nmodule.exports = _nonIterableRest;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/nonIterableRest.js\n// module id = UqQ9\n// module chunks = 0471", "var arrayLikeToArray = require(\"./arrayLikeToArray\");\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(n);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\n\nmodule.exports = _unsupportedIterableToArray;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js\n// module id = Wd1Q\n// module chunks = 0471", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/process/browser.js\n// module id = Xm2t\n// module chunks = 076f 0471 bf3e", "function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nmodule.exports = _iterableToArray;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/iterableToArray.js\n// module id = bOmF\n// module chunks = 0471", "if performance? and performance.now\n  module.exports = -> performance.now()\nelse if process? and process.hrtime\n  module.exports = -> (getNanoSeconds() - nodeLoadTime) / 1e6\n  hrtime = process.hrtime\n  getNanoSeconds = ->\n    hr = hrtime()\n    hr[0] * 1e9 + hr[1]\n  moduleLoadTime = getNanoSeconds()\n  upTime = process.uptime() * 1e9\n  nodeLoadTime = moduleLoadTime - upTime\nelse if Date.now\n  module.exports = -> Date.now() - loadTime\n  loadTime = Date.now()\nelse\n  module.exports = -> new Date().getTime() - loadTime\n  loadTime = new Date().getTime()\n\n\n\n// WEBPACK FOOTER //\n// C:/Dropbox/www/amcharts4dev/node_modules/performance-now/src/performance-now.coffee", "var _typeof = require(\"../helpers/typeof\");\n\nvar assertThisInitialized = require(\"./assertThisInitialized\");\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return assertThisInitialized(self);\n}\n\nmodule.exports = _possibleConstructorReturn;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\n// module id = eYbk\n// module chunks = 0471", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nmodule.exports = _createClass;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/createClass.js\n// module id = gqWF\n// module chunks = 0471", "var now = require('performance-now')\n  , root = typeof window === 'undefined' ? global : window\n  , vendors = ['moz', 'webkit']\n  , suffix = 'AnimationFrame'\n  , raf = root['request' + suffix]\n  , caf = root['cancel' + suffix] || root['cancelRequest' + suffix]\n\nfor(var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix]\n  caf = root[vendors[i] + 'Cancel' + suffix]\n      || root[vendors[i] + 'CancelRequest' + suffix]\n}\n\n// Some versions of FF have rAF but not cAF\nif(!raf || !caf) {\n  var last = 0\n    , id = 0\n    , queue = []\n    , frameDuration = 1000 / 60\n\n  raf = function(callback) {\n    if(queue.length === 0) {\n      var _now = now()\n        , next = Math.max(0, frameDuration - (_now - last))\n      last = next + _now\n      setTimeout(function() {\n        var cp = queue.slice(0)\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0\n        for(var i = 0; i < cp.length; i++) {\n          if(!cp[i].cancelled) {\n            try{\n              cp[i].callback(last)\n            } catch(e) {\n              setTimeout(function() { throw e }, 0)\n            }\n          }\n        }\n      }, Math.round(next))\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    })\n    return id\n  }\n\n  caf = function(handle) {\n    for(var i = 0; i < queue.length; i++) {\n      if(queue[i].handle === handle) {\n        queue[i].cancelled = true\n      }\n    }\n  }\n}\n\nmodule.exports = function(fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn)\n}\nmodule.exports.cancel = function() {\n  caf.apply(root, arguments)\n}\nmodule.exports.polyfill = function(object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf\n  object.cancelAnimationFrame = caf\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/raf/index.js\n// module id = kdhv\n// module chunks = 0471", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nmodule.exports = _defineProperty;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/defineProperty.js\n// module id = l3u9\n// module chunks = 0471", "var arrayLikeToArray = require(\"./arrayLikeToArray\");\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\n\nmodule.exports = _arrayWithoutHoles;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js\n// module id = qj8e\n// module chunks = 0471", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nmodule.exports = _setPrototypeOf;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/setPrototypeOf.js\n// module id = r1fo\n// module chunks = 0471", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nmodule.exports = _classCallCheck;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/classCallCheck.js\n// module id = v2Fs\n// module chunks = 0471", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    module.exports = _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    module.exports = _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nmodule.exports = _typeof;\n\n\n//////////////////\n// WEBPACK FOOTER\n// C:/Dropbox/www/amcharts4dev/node_modules/@babel/runtime/helpers/typeof.js\n// module id = wSZt\n// module chunks = 0471"], "sourceRoot": ""}