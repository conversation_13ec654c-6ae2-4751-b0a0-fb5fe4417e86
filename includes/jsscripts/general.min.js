function confirmDelete(t,e,n,i,o,s,a,r,l){if($href=t,$title=e,$text=n,$type=""!=i&&void 0!==o?i:"warning",$showCancelButton=void 0!==o&&o,$confirmButtonText=s||"OK",$cancelButtonText=l||"Annuleren",$html=void 0!==r&&r,"undefined"!=typeof swal){let d={title:$title,type:$type,showCancelButton:!0,cancelButtonText:$cancelButtonText,confirmButtonColor:"warning"==$type?"#DD6B55":"info"==$type?"#34A7DD":"success"==$type?"#34A7DD":"#AEDEF4",confirmButtonText:$confirmButtonText,useRejections:!0,heightAuto:!1};!1!=$html?d.html=$html:d.text=$text,swal(d).then(function(t){window.location.href=$href},function(t){return!1})}else confirm($title)&&(window.location.href=$href)}function getSwalConfirmConfig(t,e){return{title:t,html:e,type:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Ja",cancelButtonText:"Nee"}}function swalConfirm(t,e,n){swal({title:t,html:e,type:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Ja",cancelButtonText:"Nee"}).then(t=>{n(t&&!0===t.value)})}function swalError(t,e){swal({title:t,html:e,type:"error",confirmButtonColor:"#3085d6",confirmButtonText:"Sluiten"}).catch(swal.noop)}function swalInfo(t,e){swal({title:t,html:e,type:"info",confirmButtonColor:"#3085d6",confirmButtonText:"Sluiten"}).catch(swal.noop)}function doPrint(){document.body.focus(),self.print()}function openwindow(t){window.search_win&&window.search_win.close(),search_win=window.open(t,"win_ref","width=900,height=600,resizable=1,scrollbars=yes")}var dirty=!1;function setDirty(t){dirty=t}function isDirty(){return dirty}function Round(t,e){var n=parseFloat(t);return(n+=1e-10)<0?-1*(Math.round(-1*n*Math.pow(10,e))/Math.pow(10,e)):Math.round(n*Math.pow(10,e))/Math.pow(10,e)}function RoundFixed(t,e){return Round(t,e).toFixed(e)}function is_numeric(t){return("number"==typeof t||"string"==typeof t)&&""!==t&&!isNaN(t)}function decimalNL(t,e){return(null==e&&(e=2),isNaN(t=RoundFixed(t=(t=String(t)).replace(",","."),e).toString()))?"":t}function decimalPerc(t){return isNaN(t=RoundFixed(t=(t=String(t)).replace(",","."),1))?"":t}function currencyFormat(t,e){return t=parseFloat(t),isNaN(e)&&(e=2),t.toFixed(e).replace(".",",").replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1.")}function numberFormat(t,e){return isNaN(e)&&(e=0),currencyFormat(t,e)}function getFloatEsc(t){return parseFloat(t=(t=String(t)).replace(",","."))}function favorieten(t,e){document.all?window.external.AddFavorite(t,e):window.sidebar&&window.sidebar.addPanel(e,t,"")}function zoekinkvk(t,e,n){""!=t?(t=t.substr(0,8),window.open("https://server.db.kvk.nl/TST-BIN/ZS/ZSWWW01@?TYPE=NDNR&NDNR="+t+"&NSDN=%3F")):""!=e?(e=e.replace(" ",""),""==n&&(n=1),window.open("https://server.db.kvk.nl/TST-BIN/ZS/ZSWWW01@?TAAL=NL++&TYPE=PCHN&AANT=0&AWCD="+e+"&NHVC="+n+"&HIST=+")):alert("Niet mogelijk")}function zoekpostcode(t,e,n,i,o){if(2==o||"be"==o)window.open("http://www.bpost.be/site/nl/residential/customerservice/search/postal_codes.html");else if(3==o||"de"==o)window.open("http://www.postdirekt.de/plzserver/");else{var s="";i&&""!=i?s+=i:s+=t+" "+e+" + "+n,window.open("http://www.postcode.nl/zoek/"+s)}}function textCounter(t,e,n){t.value.length>n?t.value=t.value.substring(0,n):e&&(e.value=n-t.value.length)}function textCounterJquery(t,e,n){t.val().length>n?t.val(t.val().substring(0,n)):e.val(n-t.val().length)}function lengthCounter(t){void 0===t&&(t="lengthcounter"),$("."+t).each(function(){var e=$(this);e.on("keyup",function(){textCounterJquery(e,e.parent().find("."+t+"-length"),e.attr("data-lengthcounter"))}).on("keydown",function(){textCounterJquery(e,e.parent().find("."+t+"-length"),e.attr("data-lengthcounter"))}),e.trigger("keyup")})}function trim(t){return t.replace(/^\s+|\s+$/g,"")}function isInt(t){var e=parseInt(t);return!isNaN(e)&&t==e&&t.toString()==e.toString()}function isFloat(t){var e=trim(t);return/\./.test(e.toString())}function zeroFill(t,e){return(e=e||2,(e-=t.toString().length)>0)?Array(e+(/\./.test(t)?2:1)).join("0")+t:t+""}function convertSecondsToTimestring(t){return(t=parseFloat(t))>0?zeroFill(Math.floor(t/60/60),2)+":"+zeroFill(Math.floor(t/60%60),2):"00:00"}function convertTimestringToSeconds(t){if(!1!==t.indexOf(":")){var e=t.split(":");return 3600*parseFloat(e[0])+60*parseFloat(e[1])}return 0}function convertSecondsToHours(t){return(t=parseFloat(t))>0?parseFloat(zeroFill(t/60/60,2)).toFixed(2):"0.00"}function buildPopper(){if("undefined"==typeof Popper)return!1;let t=$(".qtipa");if(0===t.length)return!0;var e=$("#tooltip");0===e.length&&($("body").append('  <div id="tooltip">\n    <div id="tooltip-title">Titel</div>\n    <div id="tooltip-content">Content</div>\n    <div id="tooltip-arrow" data-popper-arrow></div>\n  </div>'),e=$("#tooltip"));let n=$("#tooltip-title"),i=$("#tooltip-content");return t.each(function(){$(this).on("mouseenter",function(){$(this).attr("data-content")||($(this).attr("data-content",$(this).attr("title")),$(this).attr("title","")),i.html($(this).attr("data-content")),$(this).attr("data-caption")&&$(this).attr("data-caption").length>0?(n.html($(this).attr("data-caption")),n.show()):n.hide(),0===$(this).attr("data-content").length?i.hide():i.show(),Popper.createPopper($(this)[0],e[0],{placement:"top",modifiers:[{name:"offset",options:{offset:[0,10]}}]}),e.show()}).on("mouseleave",function(){e.hide()})}),!0}function buildQtip(){console.warn("Qtip is deprecated. Stap over naar popper of ander tooltip."),void 0!==$.fn.qtip?$(".qtipa").each(function(){$(this).qtip({content:{text:$(this).attr("title"),title:{text:$(this).attr("data-caption")}},position:{viewport:$(window),at:"top right",my:"bottom left"},style:{classes:"qtip-def"},hide:{fixed:!0,delay:300}})}):$(".qtipa").each(function(){$(this).attr("data-caption")?($(this).attr("data-toggle","kt-popover"),$(this).attr("data-content",$(this).attr("title")),$(this).attr("title",$(this).attr("data-caption")),$(this).attr("data-html","true")):($(this).attr("data-toggle","kt-tooltip"),$(this).attr("data-html","true"))})}String.prototype.replaceArray=function(t,e){var n=this,i="string"==typeof t,o="string"==typeof e;if(i&&o)n=n.replace(t,e);else if(!i&&o)for(var s=0;s<t.length;s++)n=n.replace(t[s],e);else if(!i&&!o&&t.length>0&&e.length>0&&t.length==e.length)for(var s=0;s<t.length;s++)n=n.replace(t[s],e[s]);return n};var isMobile={Android:function(){return navigator.userAgent.match(/Android/i)},BlackBerry:function(){return navigator.userAgent.match(/BlackBerry/i)},iOS:function(){return navigator.userAgent.match(/iPhone|iPad|iPod/i)},Opera:function(){return navigator.userAgent.match(/Opera Mini/i)},Windows:function(){return navigator.userAgent.match(/IEMobile/i)},any:function(){return isMobile.Android()||isMobile.BlackBerry()||isMobile.iOS()||isMobile.Opera()||isMobile.Windows()}};function isValidURL(t){return RegExp("^(https?:\\/\\/)?((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$","i").test(t)}function responsiveyoutube(t){var e=$("iframe[src*='www.youtube.com'], iframe.frame-responsive");e.each(function(){$(this).data("aspectRatio",this.height/this.width).data("aspectHeight",this.height).data("aspectWidth",this.width).data("largerthenorig",t)}),$(window).resize(function(){e.each(function(){var t=$(this),e=t.parent().parent().width(),n=!1;"undefined"!=t.data("largerthenorig")&&1==t.data("largerthenorig")&&(n=!0),!n&&e>t.data("aspectWidth")?t.removeAttr("height").removeAttr("width").width(t.data("aspectWidth")).height(t.data("aspectHeight")):e>0&&t.removeAttr("height").removeAttr("width").width(e).height(e*t.data("aspectRatio"))})}).resize()}function validateEmail(t){return/^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i.test(t)}function validatePhoneNL(t){return!!t.match(/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9]){8}$/)}function validatePhone(t){return!!t.match(/^[(]{0,1}[0-9]{3}[)]{0,1}[-\s\.]{0,1}[0-9]{3}[-\s\.]{0,1}[0-9]{4}$/)}function jquery_id_esc(t){return t.replace(/(:|\.|\[|\]|,)/g,"\\$1")}function deg2rad(t){return t*Math.PI/180}function rad2deg(t){return 180*t/Math.PI}function nl2br(t,e){return(t+"").replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g,"$1"+(e||void 0===e?"<br />":"<br>")+"$2")}function escapeIdJs(t){return t.replace(/\[/g,"\\\\[").replace(/\]/g,"\\\\]")}function ibantoscreen(t){return""==t?"":(str=t.substring(0,4)+" ",str+=t.substring(4,4)+" ",str+=t.substring(8,4)+" ",str+=t.substring(12,4)+" ",trim(str+=t.substring(16)))}function preventDoubleclick(t){if(t.length){if(t.hasClass("is-clicked"))return!0;t.addClass("is-clicked")}return!1}function refresh_datatable_on_filter_change(t){if($(".list-filter-form").length>0){var e;$(".list-filter-form select").each(function(){$(this).on("change",function(){t.draw()})}),$('.list-filter-form input[type!="text"]').each(function(){$(this).on("change",function(){t.draw()})}),$('.list-filter-form input[type="text"]').length>0&&$('.list-filter-form input[type="text"]').each(function(){$(this).on("input propertychange paste",function(){clearTimeout(e),e=setTimeout(function(){t.draw()},500)})})}t.on("xhr",function(e,n,i,o){let s=!0;null!=i&&void 0!==i.data&&i.data.length>0&&(s=!1);let a=$(t.table().container()),r=$("#"+a.attr("id")+"-no-items-message");s?(a.addClass("datatable-empty"),r.show()):(a.removeClass("datatable-empty"),r.hide())})}function get_default_datatable_config(){return $.fn.DataTable.ext.pager.numbers_length=8,{processing:!0,serverSide:!0,stateSave:!0,ajax:{type:"POST",data:function(t){$(".list-filter-form").length>0&&($(".list-filter-form select").length>0&&$(".list-filter-form select").each(function(){t[$(this).attr("name")]=$(this).val()}),$('.list-filter-form input[type="text"]').each(function(){t[$(this).attr("name")]=$(this).val()}),$('.list-filter-form input[type="checkbox"]:checked').each(function(){t[$(this).attr("name")]=$(this).val()}))}},searching:!1,lengthMenu:[[25,50,100,99999],[25,50,100,"Alles"]],dom:"plBfrtip",buttons:[{extend:"copy",exportOptions:{columns:":not(.no-export)"}},{extend:"csv",exportOptions:{columns:":not(.no-export)"}},{extend:"print",exportOptions:{columns:":not(.no-export)"}},],select:!0,fixedHeader:!0,language:{sProcessing:"Een moment geduld aub - bezig met laden...",sLengthMenu:"_MENU_ resultaten weergeven",sZeroRecords:"Geen resultaten gevonden",sInfo:"_START_ tot _END_ van _TOTAL_ resultaten",sInfoEmpty:"Geen resultaten gevonden",sInfoFiltered:" (gefilterd uit _MAX_ resultaten)",sInfoPostFix:"",sSearch:"Zoeken:",sEmptyTable:"Geen resultaten gevonden",sInfoThousands:".",sLoadingRecords:"Een moment geduld aub - bezig met laden...",oPaginate:{sFirst:"Eerste",sLast:"Laatste",sNext:'<svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M7.33984 6.28516C7.58594 6.55859 7.58594 6.96875 7.33984 7.21484L2.08984 12.4648C1.81641 12.7383 1.40625 12.7383 1.16016 12.4648C0.886719 12.2188 0.886719 11.8086 1.16016 11.5625L5.94531 6.77734L1.16016 1.96484C0.886719 1.71875 0.886719 1.30859 1.16016 1.0625C1.40625 0.789062 1.81641 0.789062 2.0625 1.0625L7.33984 6.28516Z" fill="#15151E"/>\n</svg>\n',sPrevious:'<svg width="7" height="13" viewBox="0 0 7 13" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M0.410156 6.28516L5.66016 1.0625C5.90625 0.789062 6.31641 0.789062 6.58984 1.0625C6.83594 1.30859 6.83594 1.71875 6.58984 1.96484L1.77734 6.75L6.5625 11.5625C6.83594 11.8086 6.83594 12.2188 6.5625 12.4648C6.31641 12.7383 5.90625 12.7383 5.66016 12.4648L0.410156 7.21484C0.136719 6.96875 0.136719 6.55859 0.410156 6.28516Z" fill="#A5A5AB"/>\n</svg>\n'},oAria:{sSortAscending:": activeer om kolom oplopend te sorteren",sSortDescending:": activeer om kolom aflopend te sorteren"},select:{rows:"%d rijen geselecteerd"},buttons:{copy:"Kopi\xeber",csv:"CSV bestand",selectAll:"Selecteer alles",selectNone:"Deselecteer alles"}}}}function defaultConfirm(t,e){"undefined"!=typeof swal?((vals={title:void 0!==e.title?e.title:"",type:void 0!==e.type?e.type:"warning",showConfirmButton:void 0===e.showConfirmButton||e.showConfirmButton,showCancelButton:void 0===e.showCancelButton||e.showCancelButton,cancelButtonText:void 0!==e.cancelButtonText?e.cancelButtonText:"Annuleren",confirmButtonText:void 0!==e.confirmButtonText?e.confirmButtonText:"Ja",useRejections:!0}).confirmButtonColor="warning"==vals.type?"#DD6B55":"info"==vals.type?"#34A7DD":"success"==vals.type?"#34A7DD":"#AEDEF4",void 0!==e.html?vals.html=e.html:vals.text=e.text,swal(vals).then(function(e){t()},function(t){return!1})):confirm($title)&&t()}function setAcceptsCookies(){var t=new Date;t.setTime(t.getTime()+15552e6);var e="expires="+t.toUTCString();document.cookie="cc_first_visit=true;"+e+";path=/"}function hasAcceptedCookies(){for(var t=decodeURIComponent(document.cookie).split(";"),e=0;e<t.length;e++){for(var n=t[e];" "==n.charAt(0);)n=n.substring(1);if(0==n.indexOf("cc_first_visit="))return!0}return!1}function getCookieValue(t){var e=document.cookie.match("(^|;)\\s*"+t+"\\s*=\\s*([^;]+)");return e?e.pop():""}function setCookie(t,e,n){var i="";if(n){var o=new Date;o.setTime(o.getTime()+864e5*n),i="; expires="+o.toUTCString()}document.cookie=t+"="+(e||"")+i+"; path=/"}function blockEnterSubmit(){$(document).ready(function(){$(window).on("keydown",function(t){if(13==t.keyCode&&"textarea"!=t.target.type)return t.preventDefault(),!1})})}function parseJson(t){return""==t?t:t=JSON.parse(t,function(t,e){if("false"===e)return!1;if("true"===e)return!0;if("text"===t||"laser_text"===t)return e;if("number"==typeof e)return e;if("object"!=typeof e&&!0===isNumeric(e)&&isNumberFloat(e))return parseFloat(e,10);else if("string"==typeof e&&!0===isNumeric(e)&&0==e.substring(0,1))return e;else if("object"!=typeof e&&!0===isNumeric(e))return parseInt(e,10);else return e})}function isNumeric(t){return("number"==typeof t||"string"==typeof t)&&""!==t&&!isNaN(t)}function isNumberFloat(t){return isNumeric(t)&&t%1!=0}function addScrollListener(){$(window).on("scroll",function(){$(".scroll-animations").each(function(){var t,e,n,i;if(!0==(t=this,n=(e=$(window).scrollTop())+$(window).height(),(i=$(t).offset().top)+$(t).height()<=n&&i>=e)){var o=$(this);o.addClass($(this).attr("data-scoll-animations"));var s=0,a="1s";o.attr("data-scroll-animations-delay")&&(s=o.attr("data-appear-animation-delay")),o.attr("data-scroll-animations-duration")&&(a=o.attr("data-appear-animation-duration")),"1s"!=a&&o.css("animation-duration",a),setTimeout(function(){o.addClass("scroll-animations-visible")},s)}})}),$(window).trigger("scroll")}function showButtonLoader(t){$(t).attr("disabled",!0).prepend('<span class="fa fa-circle-o-notch fa-spin"></span> ')}function removeButtonLoader(t){$(t).attr("disabled",!1).find(".fa-circle-o-notch").remove()}function gsdRowSorter(t){$("#sortablecontrol").one("click",function(){$(".sort-td").show(),$("#sortablecontrol").val("Sorteren uitzetten").addClass("sorton"),$("#sortable").tableDnD({onDrop:function(e,n){$("#message").addClass("message").text("Volgorde opslaan...").load(t+$.tableDnD.serialize("id"),function(){$("#message").text("Volgorde opgeslagen")})}}),$(this).one("click",function(){$(".sort-td").hide(),$("#sortable tr").off().css({cursor:"default"}),$("#sortablecontrol").val("Sorteren aanzetten").removeClass("sorton"),gsdRowSorter(t)})})}function videoSeofriendly(){var t,e,n,i,o,s=document.querySelectorAll(".vi-lazyload");s.length>0&&(e=document.createElement("div"),n=document.createElement("div"),i=document.createElement("div"),o=document.createElement("iframe"),e.classList.add("vi-lazyload-wrap"),n.classList.add("vi-lazyload-content"),i.classList.add("vi-lazyload-playbtn"),o.setAttribute("allow","autoplay;fullscreen"),t=new IntersectionObserver(function(s){s.forEach(function(s){var a,r,l,d,c=s.target,u=s.target.dataset.id,h=s.target.dataset.type;this_data_thumb=s.target.dataset.thumb,!0===s.isIntersecting&&(a=e.cloneNode(),c.append(a),r=n.cloneNode(),a.append(r),l=i.cloneNode(),r.append(l),"vimeo"===h?(this_data_thumb&&""!=this_data_thumb?r.style.setProperty("--vi-lazyload-img",'url("'+this_data_thumb+'")'):$.get("https://vimeo.com/api/v2/video/"+u+".json",function(t){t[0]&&r.style.setProperty("--vi-lazyload-img",'url("'+t[0].thumbnail_large+'")')}),l.addEventListener("click",function(){(d=o.cloneNode()).src="https://player.vimeo.com/video/"+u+"?autoplay=1&autopause=0",r.append(d)})):"youtube"===h?(this_data_thumb&&""!=this_data_thumb?r.style.setProperty("--vi-lazyload-img",'url("'+this_data_thumb+'")'):r.style.setProperty("--vi-lazyload-img",'url("https://i.ytimg.com/vi/'+u+'/hqdefault.jpg")'),l.addEventListener("click",function(){(d=o.cloneNode()).src="https://www.youtube.com/embed/"+u+"?autoplay=1",r.append(d)})):console.error("videoSeofriendly: unkown type!!"),t.unobserve(c))})},{rootMargin:"200px 0px"}),s.forEach(function(e){t.observe(e)}))}function lazyLoadBackgroundImages(){document.addEventListener("DOMContentLoaded",function(){if("IntersectionObserver"in window){t=document.querySelectorAll(".lazybg");var t,e,n=new IntersectionObserver(function(t,e){t.forEach(function(t){if(t.isIntersecting){var e=$(t.target);e.attr("data-lazybg").length>0&&(e.css("background-image","url('"+e.attr("data-lazybg")+"')"),e.attr("data-lazybg",null),n.unobserve(e[0]))}})});t.forEach(function(t){n.observe(t)})}else{function i(){e&&clearTimeout(e),e=setTimeout(function(){var e=window.pageYOffset;t.forEach(function(t){if(t.offsetTop<window.innerHeight+e){var n=$(t);n.attr("data-lazybg").length>0&&(n.css("background-image","url('"+n.attr("data-lazybg")+"')"),n.attr("data-lazybg",null))}}),0==t.length&&(document.removeEventListener("scroll",i),window.removeEventListener("resize",i),window.removeEventListener("orientationChange",i))},20)}t=document.querySelectorAll(".lazybg"),document.addEventListener("scroll",i),window.addEventListener("resize",i),window.addEventListener("orientationChange",i)}})}function addErrorHandler(){if(window.document.documentMode){console.log("Please upgrade to an up-to-date browser! This website will not function properly.");return}window.addEventListener("error",function(t){if(t.url=location.href,t.userAgent=window.navigator.userAgent,""===t.message||"Script error."===t.message)return;let e=JSON.stringify(t,["message","arguments","type","name","filename","lineno","url","userAgent"]);void 0!==e&&fetch("/error404?type=js",{method:"POST",headers:{"Content-Type":"application/json"},body:e}).then(t=>t.json()).then(t=>{})})}$(document).ready(function(){buildPopper()||buildQtip(),"function"==typeof $("a.imagegallery").fancybox&&(console.warn("Fancybox is deprecated. Stap over naar SimpleLightbox of andere image popup"),$("a.imagegallery").fancybox({titlePosition:"inside",transitionIn:"elastic",transitionOut:"elastic",speedIn:600,speedOut:200,helpers:{overlay:{locked:!1}}})),"function"==typeof $(".time").on&&$(".time").on("change",function(){if(""!=$(this).val()){var t=$(this).hasClass("allow-single-minutes"),e=$(this).hasClass("convert-hours-format"),n=/^\d{1,2}:\d{2}([ap]m)?$/,i=$(this).val().trim(),o=i.split(":");if(e&&o[1])switch(parseFloat(o[1])){case 25:o[1]=15;break;case 50:o[1]=30;break;case 75:o[1]=45}var s=!0;if(!$(this).val().match(n)){s=!1;var a="",r="";if(a=zeroFill(a=(i=i.replaceArray([",",";",":"," ","."],"")).length<=2?i:i.substring(0,i.length-2),2),i.length>=3?r+=zeroFill(i.substring(i.length-2)):r+="00",e)switch(parseFloat(r)){case 25:r=15;break;case 50:r=30;break;case 75:r=45}var l=a+":"+r;l.match(n)&&($(this).val(l),o=l.split(":"),s=!0)}if(s&&(o[0]>23||o[1]>59)&&(s=!1),!s||t||0==o[1]||"00"==o[1]||15==o[1]||30==o[1]||45==o[1]||(s=!1),s)$(this).val(zeroFill(o[0],2)+":"+zeroFill(o[1]));else{$(this).val("");var d="Voer een geldige tijd in (formaat 00:00)";t||(d+="\nEen tijd bestaat uit uren en kwartieren."),alert(d),obj=$(this),setTimeout(function(){obj.trigger("focus")},50)}}}),"function"==typeof window.flatpickr&&($(".datepicker").each(function(){$(this).flatpickr({allowInput:!0,dateFormat:"d-m-Y",locale:"nl",weekNumbers:$(this).hasClass("datepicker_weeks"),onClose:function(t,e,n){n.setDate(n._input.value)}})}),$(document).on("change",".datepicker",function(){let t=$(this).val();8==t.length&&$(this)[0]._flatpickr.setDate(t.substring(0,2)+"-"+t.substring(2,4)+"-"+t.substring(4),!0,"d-m-Y")})),$(".emailencrypted").each(function(){for(var t=$(this).attr("title").split(","),e="",n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);$(this).attr("href","mailto:"+e),$(this).text(e),$(this).attr("title","")}),$(document).on("click","a.gsd-delete",function(t){t.preventDefault();var e,n="Verwijderen";$(this).is("[data-gsd-title]")?n=$(this).attr("data-gsd-title"):$(this).is("[title]")&&(n=$(this).attr("title")),$(this).is("[data-gsd-cancel]")&&(e=$(this).attr("data-gsd-cancel")),confirmDelete($(this).attr("href"),n,$(this).attr("data-gsd-text"),"",null,null,null,$(this).attr("data-gsd-text").replace(/\\n/g,"<br/>").replace(/\n/g,"<br/>"),e)}),$(".gsd-confirm").on("click",function(t){var e="Opslaan";$(this).is("[data-gsd-title]")?e=$(this).attr("data-gsd-title"):$(this).is("[title]")&&(e=$(this).attr("title"));var n="Weet u het zeker?";$(this).is("[data-gsd-text]")&&(n=$(this).attr("data-gsd-text").replace(/\\n/g,"\n"));var i="warning";if($(this).is("[data-gsd-type]")&&(i=$(this).attr("data-gsd-type")),"undefined"!=typeof swal){var o=$(this);t.preventDefault();var s={title:e,type:i,showCancelButton:!0,confirmButtonColor:"warning"==i?"#DD6B55":"info"==i?"#34A7DD":"success"==i?"#34A7DD":"#AEDEF4",confirmButtonText:"OK",cancelButtonText:"Annuleren",useRejections:!0};s.text=n,swal(s).then(function(t){return void 0!==o.attr("href")?window.location.href=o.attr("href"):(o.off(),o.trigger("click")),!0},function(t){return!1})}else if(!confirm(e+"\n"+n))return t.preventDefault(),!1}),$(".pager-pageamount").length&&$(".pager-pageamount").on("change",function(t){t.preventDefault(),$(this).parents("form").submit()}),$(".gsd_uploader_wrapper input[type=file]").each(function(){$(this).on("change",function(t){if(t.target.value){var e=t.target.value.split("\\").pop();$(this).parent().find(".gsd_uploader_filename").length>0?$(this).parent().find(".gsd_uploader_filename").html("<b> "+e+" </b>"):$(this).after('<span class="gsd_uploader_filename"><b> '+e+" </b></span>")}})}),$(".prevent-doubleclick").length&&$(".prevent-doubleclick").on("click",function(t){$(this).hasClass("is-clicked")?t.preventDefault():($(this).addClass("is-clicked"),$(this).is("[data-isloadingtext]")&&$(this).text($(this).data("isloadingtext")),$(this).is("[data-disabledclass]")&&$(this).addClass($(this).data("disabledclass")))}),$("input.lockonsubmit,a.lockonsubmit").on("click",function(t){if($(this).hasClass("disabled")){t.preventDefault();return}return $(this).addClass("disabled"),$(this).is("a")?$(this).append('<span class="fa fa-circle-o-notch fa-spin fa-fw lockonsubmit_spinner"></span>'):$(this).after('<span class="fa fa-circle-o-notch fa-spin fa-fw lockonsubmit_spinner"></span>'),!0})}),function(t){t.fn.table_to_div=function(e){return t.extend({},{},e),this.each(function(){var e="";columns=[],table_target=t(this);var n=0,i=t(e=table_target.html().replace(/<tbody/gi,"<div").replace(/<tr/gi,"<div").replace(/<\/tr>/gi,"</div>").replace(/<td/gi,"<div").replace(/<\/td>/gi,"</div>").replace(/<\/tbody/gi,"</div").replace(/trhover/gi,""));i.find("> div").each(function(){var e=0;t(this).find("> div").each(function(){0==n?columns.push(t(this).html()):t(this).wrap("<div>").before("<label>"+columns[e]+"</label>\n"),e++}),0==n&&t(this).remove(),n++}),t(this).wrap('<div class="default_table_mobile">'),table_target.replaceWith(i.html())})}}(jQuery);class GsdModal{id="";width="";height="";isPersistant=!1;position="center";constructor(t="gsd-modal-default-id",e=!1,n="center"){this.id=t,this.isPersistant=e,this.position=n}static gsdLoader=null;static loader=null;setLoader(t,e,n,i){this.loader=`<img src="${t}" style="width: ${e}; max-width: ${n}; height: ${i};"/>`}setWidth(t){this.width=t}getWidthStyle(){return""==this.width?"":"width: "+this.width+";"}setHeight(t){this.height=t}getHeightStyle(){return""==this.height?"":"height: "+this.height+";"}getId(){return this.id}getPosition(){return this.position}init(){let t=this,e='<div class="gsd-modal" id="'+this.getId()+'" data-id="'+this.getId()+`">
        
          <div class="gsd-modal-position-${this.getPosition()}">
        
            <div class="gsd-modal-bg-glass">
              <div></div>
            </div>
        
            <div class="gsd-modal-container" style="`+this.getWidthStyle()+`">
              <div class="gsd-modal-header">
                <div class="gsd-modal-title">
                  Modal Header
                </div>
                <a href="#" class="gsd-modal-close">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><rect opacity="0.3" x="2" y="2" width="20" height="20" rx="5" fill="currentColor"/><rect x="7" y="15.3137" width="12" height="2" rx="1" transform="rotate(-45 7 15.3137)" fill="currentColor"/><rect x="8.41422" y="7" width="12" height="2" rx="1" transform="rotate(45 8.41422 7)" fill="currentColor"/></svg>
                </a>
              </div>
              <div class="gsd-modal-content" style="`+this.getHeightStyle()+`"></div>
            </div>
        
          </div>
        
        </div>`;0==$("#"+this.getId()).length?$("body").append(e):this.getId(),$(document).ready(function(){$("#"+t.getId()+" .gsd-modal-close").on("click",function(e){t.close()}),$("#"+t.getId()+" .gsd-modal-bg-glass").on("click",function(e){t.isPersistant||t.close()}),$(document).on("keydown",function(e){"Escape"!==e.key||t.isPersistant||t.close()})})}open(t,e){$(document).trigger("gsdModalPreOpen",{id:this.getId(),url:t}),$("#"+this.getId()+" .gsd-modal-content").html(this.loader),void 0!=e&&""!=e?$("#"+this.getId()+" .gsd-modal-title").html(e).show():$("#"+this.getId()+" .gsd-modal-title").hide(),this.show();let n=this;$.get(t,function(e){$("#"+n.getId()+" .gsd-modal-content").html(e),$(document).trigger("gsdModalPostOpen",{id:n.getId(),url:t})}).fail(function(t){alert("Foutmelding: het is niet gelukt om deze request uit te voeren.")})}openIframe(t,e){$(document).trigger("gsdModalPreOpen",{id:this.getId(),url:t}),$("#"+this.getId()+" .gsd-modal-content").html(this.loader),void 0!=e&&""!=e?$("#"+this.getId()+" .gsd-modal-title").html(e).show():$("#"+this.getId()+" .gsd-modal-title").hide(),this.show();let n=$('<iframe src="'+t+'"></iframe>');$("#"+this.getId()+" .gsd-modal-content").append(n),$(document).trigger("gsdModalPostOpen",{id:this.getId(),url:t})}openContent(t,e){$(document).trigger("gsdModalPreOpen",{id:this.getId()}),void 0!=t&&""!=t?$("#"+this.getId()+" .gsd-modal-title").html(t).show():$("#"+this.getId()+" .gsd-modal-title").hide(),this.show(),$("#"+this.getId()+" .gsd-modal-content").html(e),$(document).trigger("gsdModalPostOpen",{id:this.getId()})}openContentJQuery(t,e){$(document).trigger("gsdModalPreOpen",{id:this.getId()}),void 0!=t&&""!=t?$("#"+this.getId()+" .gsd-modal-title").html(t).show():$("#"+this.getId()+" .gsd-modal-title").hide(),this.show(),$("#"+this.getId()+" .gsd-modal-content").append(e),$(document).trigger("gsdModalPostOpen",{id:this.getId()})}buildLoader(t){void 0!=t&&""!=t?$("#"+this.getId()+" .gsd-modal-title").html(t).show():$("#"+this.getId()+" .gsd-modal-title").hide(),$("#"+this.getId()+" .gsd-modal-content").html(this.loader)}show(){$("body").css("overflow","hidden"),$("#"+this.getId()).addClass("gsd-modal-show")}hide(){$("body").css("overflow","auto"),$("#"+this.getId()).removeClass("gsd-modal-show")}close(){$(document).trigger("gsdModalClose",[this.getId()]),this.hide()}static showLoader(t="Loading...",e="/gsdfw/images/loading1.gif",n="450px",i="100%",o="auto"){let s=new GsdModal("loader");s.setLoader(e,n,i,o),s.setWidth("400px"),s.init(),s.buildLoader(t),s.show(),GsdModal.gsdLoader=s}static hideLoader(){null!=GsdModal.gsdLoader&&GsdModal.gsdLoader.close()}}function hideLoader(){swal.close()}function gsdSelect(t,e="Selecteer opties...",n=!0,i=!0){let o=document.getElementById(t),s=o.hasAttribute("multiple"),a=o.options;o.style.display="none";let r=document.createElement("div");function l(t){let e=document.createElement("div");return e.innerHTML=t.trim(),e.firstChild}r.className="gsd-select",r.setAttribute("data-multiselect-id",t);let d=document.createElement("div");if(d.className="gsd-select-options",r.appendChild(l('<span class="gsd-select-placeholder"><span style="float: right; margin: 4px;" class="fa fa-angle-down"></span></span>')),n){let c=l('<div style="display: flex; padding-right: 5px"><input type="text" class="gsd-select-searchbox" placeholder="Zoeken..."/><span title="Zoekbalk legen" class="fa fa-times gsd-select-reset" style="align-self: center; cursor: pointer;"></span></div>');d.appendChild(c)}if(s){let u=l('<label for="'+t+'_multiselect_select_all"><input type="checkbox" id="'+t+'_multiselect_select_all" /><span>Selecteer alles</span></label>');d.appendChild(u)}[...a].forEach(function(n){let i;if(s||0!==[...a].indexOf(n))i=""===n.value?l('<span class="gsd-select-empty">'+n.text+"</span>"):s?l('<label for="'+t+"_"+n.value+'"><input type="checkbox" class="gsd-select-option" id="'+t+"_"+n.value+'" />'+n.text+"</label>"):l('<label for="'+t+"_"+n.value+'"><input type="checkbox" style="display: none" class="gsd-select-option" id="'+t+"_"+n.value+'" />'+n.text+"</label>");else{let o=e;""!==n.textContent&&(o=n.textContent),i=l('<label for="placeholder_'+t+'"><input type="checkbox" style="display: none" class="gsd-select-option" id="placeholder_'+t+'" />'+o+"</label>")}d.appendChild(i)}),r.appendChild(d),o.parentNode.insertBefore(r,o.nextSibling),gsdSelectHandler(t,s,e,i)}function gsdSelectHandler(t,e,n,i){let o=document.getElementById(t),s=document.querySelector("[data-multiselect-id="+t+"]"),a=s.querySelector(".gsd-select-options"),r=s.querySelector("#"+t+"_multiselect_select_all"),l=s.querySelector(".gsd-select-searchbox"),d=s.querySelector(".gsd-select-placeholder"),c=a.querySelector("#placeholder_"+t),u=a.querySelectorAll(".gsd-select-empty");function h(){let e=0;a.querySelectorAll(":checked").forEach(function(n){n.id!==t+"_multiselect_select_all"&&e++});let i=document.createElement("span");i.style.float="right",i.style.margin="4px",i.className="fa fa-angle-down",e>0?(d.textContent=n+"("+e+")",d.appendChild(i)):(d.textContent=n,d.appendChild(i))}function p(n,r=!1){let l=o.querySelector("[value='"+n.id.replace(t+"_","")+"']");if(n.id==="placeholder_"+t&&(l=o.querySelector("option:not([disabled])")),!n||!l)return;let u=n.parentElement.textContent;if(u.length>18&&i&&(u=u.slice(0,17)+"&hellip;"),r)n.checked=l.selected,!e&&n.checked?d.innerHTML=u+"<span style='float: right; margin: 4px;' class='fa fa-angle-down'></span>":e&&h();else{if(e)h();else if(a.querySelectorAll(":checked").forEach(function(t){t.id!==n.id&&(t.checked=!1)}),n.checked)d.innerHTML=u+"<span style='float: right; margin: 4px;' class='fa fa-angle-down'></span>";else{let p=c.parentElement.textContent;p.length>18&&i&&(p=p.slice(0,17)+"&hellip;"),d.innerHTML=p,s.querySelector(".gsd-select-placeholder").innerHTML+="<span style='float: right; margin: 4px;' class='fa fa-angle-down'></span>"}if(l.selected!==n.checked){l.selected=n.checked;let f=new Event("change");o.dispatchEvent(f)}else l.selected=n.checked}}function f(){let t=s.querySelectorAll(".gsd-select-option:checked"),n=a.querySelectorAll(".gsd-select-option");t.length==n.length&&(r.checked=!0,r.parentNode.classList.add("gsd-select-option-selected")),e||0!=t.length||c.parentElement.classList.add("gsd-select-option-selected")}a.querySelectorAll('input[type="checkbox"]').forEach(function(t){p(t,!0),t.parentElement.classList.toggle("gsd-select-option-selected",t.checked)}),a.querySelectorAll(":checked").length===a.querySelectorAll('input[type="checkbox"]').length-1&&(r.checked=!0,r.parentElement.classList.add("gsd-select-option-selected")),d.addEventListener("click",function(){a.classList.toggle("gsd-select-flex"),l&&a.classList.contains("gsd-select-flex")&&l.focus()}),document.addEventListener("mouseup",function(t){s.contains(t.target)||"none"===window.getComputedStyle(a).display||a.classList.toggle("gsd-select-flex")}),document.addEventListener("keyup",function(t){"Escape"===t.key&&a.classList.contains("gsd-select-flex")&&a.classList.remove("gsd-select-flex")}),a.querySelectorAll('input[type="checkbox"]').forEach(function(n){n.addEventListener("change",function(){let i=this.id===n.id;p(this),e||a.querySelectorAll('input[type="checkbox"]').forEach(function(e){i&&e.id==="placeholder_"+t||e.parentElement.classList.remove("gsd-select-option-selected")}),this.checked?(this.parentElement.classList.add("gsd-select-option-selected"),c&&this.id!=="placeholder_"+t&&c.parentElement.classList.remove("gsd-select-option-selected")):(i&&this.id==="placeholder_"+t||this.parentElement.classList.remove("gsd-select-option-selected"),r&&(r.parentElement.classList.remove("gsd-select-option-selected"),r.checked=!1))}),e||n.addEventListener("click",function(){a.classList.toggle("gsd-select-flex")})}),Array.from(s.querySelectorAll(".gsd-select-option")).forEach(function(t){t.addEventListener("change",f)}),r&&r.addEventListener("click",function(){var t=this.checked;Array.from(s.querySelectorAll(".gsd-select-option")).forEach(function(e){e.checked=t,t?e.parentNode.classList.add("gsd-select-option-selected"):e.parentNode.classList.remove("gsd-select-option-selected"),p(e)})}),l&&l.addEventListener("keyup",function(){let e=this.value.toLowerCase(),n=a.querySelectorAll("label");u.forEach(function(t){t.style.display="block",""!==e&&(t.style.display="none")}),n.forEach(function(n){let i=n.textContent.toLowerCase();n.style.display="block",(-1==i.indexOf(e)||""!==e&&("selecteer alles"===i||n.getAttribute("for")==="placeholder_"+t))&&(n.style.display="none")})}),document.addEventListener("keydown",function(t){"Enter"===t.key&&document.activeElement.classList.contains("gsd-select-searchbox")&&t.preventDefault()}),l&&s.querySelector(".gsd-select-reset").addEventListener("click",function(){l.value="";let t=new Event("keyup");l.dispatchEvent(t)})}function saveScrollPosition(t,e){e.getItem(t)&&window.scroll(0,parseInt(e.getItem(t))),addEventListener("scroll",n=>{e.setItem(t,Math.round(window.scrollY))})}function initDelayKeyUp(){var t;(t=jQuery).fn.delayKeyup=function(e,n){var i=0;return t(this).on("keyup",function(){clearTimeout(i),i=setTimeout(e,n)}),t(this)}}function updateCookieBanner(){localStorage.setItem("consentSet","true"),$("#cookie-banner").css("display","none"),$("body").css("overflow","auto")}